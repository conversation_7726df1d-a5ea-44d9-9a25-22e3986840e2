<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI水源与水管管理中心</title>
    <link rel="stylesheet" href="AIWaterManagementPanel.css">
</head>
<body>
    <!-- AI水源管理主容器 -->
    <div id="ai-water-management-container" class="water-management-container">
        <!-- 头部导航 -->
        <div class="management-header">
            <h1 class="management-title">
                <span class="title-icon">💧</span>
                AI水源与水管管理中心
            </h1>
            <div class="header-actions">
                <button class="btn btn-secondary" id="health-check-btn">
                    <span class="btn-icon">🔍</span>
                    系统检查
                </button>
                <button class="btn btn-primary" id="refresh-status-btn">
                    <span class="btn-icon">🔄</span>
                    刷新状态
                </button>
            </div>
        </div>

        <!-- 系统状态概览 -->
        <div class="system-overview">
            <div class="overview-card">
                <div class="overview-header">
                    <h3>🏭 水厂状态</h3>
                    <span class="status-badge active" id="factory-status">正常运行</span>
                </div>
                <div class="overview-stats">
                    <div class="stat-item">
                        <span class="stat-label">活跃水源</span>
                        <span class="stat-value" id="active-sources">1/3</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">今日请求</span>
                        <span class="stat-value" id="today-requests">150</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">今日成本</span>
                        <span class="stat-value" id="today-cost">$2.45</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">系统健康度</span>
                        <span class="stat-value" id="system-health">98.5%</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 标签页导航 -->
        <div class="tab-navigation">
            <button class="tab-btn active" data-tab="water-factory">
                <span class="tab-icon">🏭</span>
                水厂分配
            </button>
            <button class="tab-btn" data-tab="water-sources">
                <span class="tab-icon">💧</span>
                水源管理
            </button>
            <button class="tab-btn" data-tab="pipeline-network">
                <span class="tab-icon">🚿</span>
                水管网络
            </button>
            <button class="tab-btn" data-tab="usage-monitoring">
                <span class="tab-icon">📊</span>
                使用监控
            </button>
            <button class="tab-btn" data-tab="cost-control">
                <span class="tab-icon">💰</span>
                成本控制
            </button>
        </div>

        <!-- 水厂分配标签页 -->
        <div id="water-factory-tab" class="tab-content active">
            <div class="factory-panel">
                <div class="panel-header">
                    <h3>🏭 智能水厂分配系统</h3>
                    <p>根据质量、成本和性能自动为各终端分配最优AI水源</p>
                </div>

                <!-- 分配策略配置 -->
                <div class="allocation-config">
                    <h4>全局分配策略</h4>
                    <div class="config-grid">
                        <div class="config-item">
                            <label for="global-strategy">分配模式</label>
                            <select id="global-strategy">
                                <option value="quality_first">质量优先</option>
                                <option value="cost_first">成本优先</option>
                                <option value="balanced" selected>平衡模式</option>
                                <option value="custom">自定义</option>
                            </select>
                        </div>
                        <div class="config-item">
                            <label for="failover-enabled">故障转移</label>
                            <input type="checkbox" id="failover-enabled" checked>
                            <span class="checkbox-label">启用自动故障转移</span>
                        </div>
                        <div class="config-item">
                            <label for="health-check-interval">健康检查间隔</label>
                            <select id="health-check-interval">
                                <option value="60">1分钟</option>
                                <option value="300" selected>5分钟</option>
                                <option value="600">10分钟</option>
                                <option value="1800">30分钟</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- 终端分配列表 -->
                <div class="terminal-allocations">
                    <h4>终端分配状态</h4>
                    <div class="allocation-list" id="allocation-list">
                        <!-- 动态生成终端分配项 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 水源管理标签页 -->
        <div id="water-sources-tab" class="tab-content">
            <div class="sources-panel">
                <div class="panel-header">
                    <h3>💧 AI水源管理</h3>
                    <p>管理AI提供商水源，配置API密钥和连接参数</p>
                    <button class="btn btn-primary" id="add-source-btn">
                        <span class="btn-icon">➕</span>
                        添加水源
                    </button>
                </div>

                <!-- 水源列表 -->
                <div class="sources-list" id="sources-list">
                    <!-- 动态生成水源项 -->
                </div>
            </div>
        </div>

        <!-- 水管网络标签页 -->
        <div id="pipeline-network-tab" class="tab-content">
            <div class="pipeline-panel">
                <div class="panel-header">
                    <h3>🚿 水管网络监控</h3>
                    <p>监控服务网关和路由状态，确保数据流畅通</p>
                </div>

                <!-- 网络拓扑图 -->
                <div class="network-topology">
                    <div class="topology-container" id="topology-container">
                        <!-- 动态生成网络拓扑图 -->
                    </div>
                </div>

                <!-- 网络统计 -->
                <div class="network-stats">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-header">
                                <span class="stat-icon">🌐</span>
                                <span class="stat-title">网关状态</span>
                            </div>
                            <div class="stat-value" id="gateway-status">健康</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-header">
                                <span class="stat-icon">⚡</span>
                                <span class="stat-title">平均延迟</span>
                            </div>
                            <div class="stat-value" id="avg-latency">245ms</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-header">
                                <span class="stat-icon">📈</span>
                                <span class="stat-title">吞吐量</span>
                            </div>
                            <div class="stat-value" id="throughput">150/min</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-header">
                                <span class="stat-icon">🔄</span>
                                <span class="stat-title">负载均衡</span>
                            </div>
                            <div class="stat-value" id="load-balance">正常</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 使用监控标签页 -->
        <div id="usage-monitoring-tab" class="tab-content">
            <div class="monitoring-panel">
                <div class="panel-header">
                    <h3>📊 使用监控</h3>
                    <p>实时监控各终端的AI服务使用情况和性能指标</p>
                </div>

                <!-- 监控图表 -->
                <div class="monitoring-charts">
                    <div class="chart-container">
                        <div class="chart-header">
                            <h4>请求量趋势</h4>
                            <div class="chart-controls">
                                <select id="chart-timerange">
                                    <option value="1h">最近1小时</option>
                                    <option value="24h" selected>最近24小时</option>
                                    <option value="7d">最近7天</option>
                                    <option value="30d">最近30天</option>
                                </select>
                            </div>
                        </div>
                        <div class="chart-content" id="requests-chart">
                            <!-- 图表内容 -->
                        </div>
                    </div>

                    <div class="chart-container">
                        <div class="chart-header">
                            <h4>响应时间分布</h4>
                        </div>
                        <div class="chart-content" id="response-time-chart">
                            <!-- 图表内容 -->
                        </div>
                    </div>
                </div>

                <!-- 终端使用统计 -->
                <div class="terminal-usage">
                    <h4>终端使用统计</h4>
                    <div class="usage-table" id="usage-table">
                        <!-- 动态生成使用统计表 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 成本控制标签页 -->
        <div id="cost-control-tab" class="tab-content">
            <div class="cost-panel">
                <div class="panel-header">
                    <h3>💰 成本控制</h3>
                    <p>监控和控制AI服务成本，优化资源配置</p>
                </div>

                <!-- 成本概览 -->
                <div class="cost-overview">
                    <div class="cost-cards">
                        <div class="cost-card">
                            <div class="cost-header">
                                <span class="cost-icon">📅</span>
                                <span class="cost-title">今日成本</span>
                            </div>
                            <div class="cost-amount" id="daily-cost">$2.45</div>
                            <div class="cost-budget">预算: $50.00</div>
                            <div class="cost-progress">
                                <div class="progress-bar">
                                    <div class="progress-fill" id="daily-progress" style="width: 4.9%"></div>
                                </div>
                            </div>
                        </div>

                        <div class="cost-card">
                            <div class="cost-header">
                                <span class="cost-icon">📊</span>
                                <span class="cost-title">本月成本</span>
                            </div>
                            <div class="cost-amount" id="monthly-cost">$48.90</div>
                            <div class="cost-budget">预算: $1500.00</div>
                            <div class="cost-progress">
                                <div class="progress-bar">
                                    <div class="progress-fill" id="monthly-progress" style="width: 3.3%"></div>
                                </div>
                            </div>
                        </div>

                        <div class="cost-card">
                            <div class="cost-header">
                                <span class="cost-icon">💡</span>
                                <span class="cost-title">预计月底</span>
                            </div>
                            <div class="cost-amount" id="projected-cost">$73.50</div>
                            <div class="cost-budget">基于当前趋势</div>
                            <div class="cost-status">
                                <span class="status-badge success">预算内</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 成本分析 -->
                <div class="cost-analysis">
                    <div class="analysis-section">
                        <h4>按水源分析</h4>
                        <div class="cost-breakdown" id="source-breakdown">
                            <!-- 动态生成成本分解 -->
                        </div>
                    </div>

                    <div class="analysis-section">
                        <h4>按终端分析</h4>
                        <div class="cost-breakdown" id="terminal-breakdown">
                            <!-- 动态生成成本分解 -->
                        </div>
                    </div>
                </div>

                <!-- 成本优化建议 -->
                <div class="cost-optimization">
                    <h4>💡 优化建议</h4>
                    <div class="optimization-list" id="optimization-suggestions">
                        <!-- 动态生成优化建议 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加水源模态框 -->
    <div id="add-source-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>添加AI水源</h3>
                <button class="modal-close" id="close-add-source-modal">&times;</button>
            </div>
            <div class="modal-body">
                <form id="add-source-form" class="source-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="source-id">水源ID</label>
                            <input type="text" id="source-id" placeholder="例如: claude" required>
                        </div>
                        <div class="form-group">
                            <label for="source-name">水源名称</label>
                            <input type="text" id="source-name" placeholder="例如: Anthropic Claude" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="source-type">水源类型</label>
                            <select id="source-type" required>
                                <option value="">请选择</option>
                                <option value="primary">主要水源</option>
                                <option value="backup">备用水源</option>
                                <option value="secondary">次要水源</option>
                                <option value="emergency">应急水源</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="source-model">AI模型</label>
                            <input type="text" id="source-model" placeholder="例如: claude-3-sonnet" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="source-endpoint">API端点</label>
                        <input type="url" id="source-endpoint" placeholder="https://api.example.com/v1/chat/completions" required>
                    </div>
                    <div class="form-group">
                        <label for="source-api-key">API密钥</label>
                        <input type="password" id="source-api-key" placeholder="输入API密钥" required>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="source-max-concurrent">最大并发</label>
                            <input type="number" id="source-max-concurrent" min="1" max="100" value="5">
                        </div>
                        <div class="form-group">
                            <label for="source-rate-limit">速率限制 (请求/分钟)</label>
                            <input type="number" id="source-rate-limit" min="1" max="1000" value="60">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="source-cost-per-token">每Token成本 (USD)</label>
                        <input type="number" id="source-cost-per-token" step="0.000001" placeholder="0.000030">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="cancel-add-source">取消</button>
                <button type="button" class="btn btn-primary" id="test-and-add-source">测试并添加</button>
            </div>
        </div>
    </div>

    <!-- 水源详情模态框 -->
    <div id="source-details-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="source-details-title">水源详情</h3>
                <button class="modal-close" id="close-source-details-modal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="source-details" id="source-details-content">
                    <!-- 动态生成水源详情 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="edit-source">编辑配置</button>
                <button type="button" class="btn btn-primary" id="test-source-connection">测试连接</button>
            </div>
        </div>
    </div>

    <!-- 网络拓扑详情模态框 -->
    <div id="topology-details-modal" class="modal" style="display: none;">
        <div class="modal-content large">
            <div class="modal-header">
                <h3>网络拓扑详情</h3>
                <button class="modal-close" id="close-topology-details-modal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="topology-details" id="topology-details-content">
                    <!-- 动态生成拓扑详情 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 引入脚本 -->
    <script src="../services/waterSourceService.js"></script>
    <script src="../services/pipelineNetworkService.js"></script>
    <script src="../services/loadBalancerService.js"></script>
    <script src="../services/costControlService.js"></script>
    <script src="../utils/healthChecker.js"></script>
    <script src="../utils/performanceMonitor.js"></script>
    <script src="../utils/failoverManager.js"></script>
    <script src="AIWaterManagementPanel.js"></script>
</body>
</html>
