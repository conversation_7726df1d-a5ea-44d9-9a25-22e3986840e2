/* AI水源与水管管理面板样式 */

/* CSS变量定义 */
:root {
    --primary-color: #1890ff;
    --primary-hover: #40a9ff;
    --success-color: #52c41a;
    --success-hover: #73d13d;
    --warning-color: #faad14;
    --warning-hover: #ffc53d;
    --error-color: #ff4d4f;
    --error-hover: #ff7875;
    --secondary-color: #d9d9d9;
    --secondary-hover: #bfbfbf;
    
    --bg-color: #f0f2f5;
    --card-bg: #ffffff;
    --border-color: #d9d9d9;
    --text-color: #262626;
    --text-secondary: #8c8c8c;
    --text-disabled: #bfbfbf;
    
    --water-blue: #1890ff;
    --water-light: #e6f7ff;
    --factory-orange: #fa8c16;
    --factory-light: #fff7e6;
    --pipeline-green: #52c41a;
    --pipeline-light: #f6ffed;
    
    --border-radius: 6px;
    --border-radius-lg: 8px;
    --box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    --box-shadow-lg: 0 4px 12px rgba(0, 0, 0, 0.15);
    --transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-sm: 12px;
    --font-size-base: 14px;
    --font-size-lg: 16px;
    --font-size-xl: 18px;
    --font-size-xxl: 20px;
}

/* 全局样式重置 */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.5715;
    color: var(--text-color);
    background-color: var(--bg-color);
}

/* 主容器 */
.water-management-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 24px;
}

/* 头部样式 */
.management-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 20px 24px;
    background: linear-gradient(135deg, var(--water-blue) 0%, var(--factory-orange) 100%);
    color: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow);
}

.management-title {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: var(--font-size-xxl);
    font-weight: 600;
    margin: 0;
}

.title-icon {
    font-size: 28px;
}

.header-actions {
    display: flex;
    gap: 12px;
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border: 1px solid transparent;
    border-radius: var(--border-radius);
    font-size: var(--font-size-base);
    font-weight: 500;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    white-space: nowrap;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
}

.btn-secondary {
    background-color: var(--card-bg);
    border-color: var(--border-color);
    color: var(--text-color);
}

.btn-secondary:hover:not(:disabled) {
    background-color: var(--secondary-hover);
    border-color: var(--secondary-hover);
}

.btn-icon {
    font-size: 16px;
}

/* 系统概览 */
.system-overview {
    margin-bottom: 24px;
}

.overview-card {
    background: var(--card-bg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow);
    padding: 20px;
}

.overview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.overview-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.status-badge.active {
    background-color: var(--pipeline-light);
    color: var(--pipeline-green);
    border: 1px solid #b7eb8f;
}

.status-badge.inactive {
    background-color: #f5f5f5;
    color: var(--text-disabled);
    border: 1px solid var(--border-color);
}

.status-badge.error {
    background-color: #fff2f0;
    color: var(--error-color);
    border: 1px solid #ffccc7;
}

.status-badge.warning {
    background-color: var(--factory-light);
    color: var(--factory-orange);
    border: 1px solid #ffd591;
}

.status-badge.success {
    background-color: var(--pipeline-light);
    color: var(--pipeline-green);
    border: 1px solid #b7eb8f;
}

.overview-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #fafafa;
    border-radius: var(--border-radius);
    border: 1px solid #f0f0f0;
}

.stat-label {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.stat-value {
    font-weight: 600;
    font-size: var(--font-size-lg);
    color: var(--text-color);
}

/* 标签页导航 */
.tab-navigation {
    display: flex;
    gap: 4px;
    margin-bottom: 24px;
    background: var(--card-bg);
    padding: 4px;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow);
}

.tab-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    border: none;
    border-radius: var(--border-radius);
    background: transparent;
    color: var(--text-secondary);
    font-size: var(--font-size-base);
    cursor: pointer;
    transition: var(--transition);
    flex: 1;
    justify-content: center;
}

.tab-btn:hover {
    background-color: #f5f5f5;
    color: var(--text-color);
}

.tab-btn.active {
    background-color: var(--primary-color);
    color: white;
}

.tab-icon {
    font-size: 16px;
}

/* 标签页内容 */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 面板通用样式 */
.factory-panel,
.sources-panel,
.pipeline-panel,
.monitoring-panel,
.cost-panel {
    background: var(--card-bg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow);
    overflow: hidden;
}

.panel-header {
    padding: 20px 24px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.panel-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
}

.panel-header p {
    color: var(--text-secondary);
    margin: 4px 0 0 0;
    font-size: var(--font-size-sm);
}

/* 水厂分配面板 */
.allocation-config {
    padding: 20px 24px;
    border-bottom: 1px solid #f0f0f0;
}

.allocation-config h4 {
    margin-bottom: 16px;
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--text-color);
}

.config-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
}

.config-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.config-item label {
    font-weight: 500;
    color: var(--text-color);
    font-size: var(--font-size-sm);
}

.config-item select,
.config-item input {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: var(--font-size-base);
    transition: var(--transition);
}

.config-item select:focus,
.config-item input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.checkbox-label {
    margin-left: 8px;
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

/* 终端分配 */
.terminal-allocations {
    padding: 20px 24px;
}

.terminal-allocations h4 {
    margin-bottom: 16px;
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--text-color);
}

.allocation-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.allocation-item {
    padding: 16px;
    border: 1px solid #f0f0f0;
    border-radius: var(--border-radius);
    background: #fafafa;
}

.allocation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.allocation-title {
    font-weight: 600;
    color: var(--text-color);
}

.allocation-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
    font-size: var(--font-size-sm);
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.detail-label {
    color: var(--text-secondary);
}

.detail-value {
    font-weight: 500;
    color: var(--text-color);
}

/* 水源列表 */
.sources-list {
    padding: 20px 24px;
}

.source-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    border: 1px solid #f0f0f0;
    border-radius: var(--border-radius);
    margin-bottom: 12px;
    background: #fafafa;
    transition: var(--transition);
}

.source-item:hover {
    background: #f0f0f0;
    cursor: pointer;
}

.source-info {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 1;
}

.source-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    font-weight: bold;
    color: white;
}

.source-avatar.openai {
    background: linear-gradient(135deg, #10a37f, #1a7f64);
}

.source-avatar.grok {
    background: linear-gradient(135deg, #1da1f2, #0d8bd9);
}

.source-avatar.gemini {
    background: linear-gradient(135deg, #4285f4, #34a853);
}

.source-avatar.claude {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
}

.source-details {
    flex: 1;
}

.source-name {
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 4px;
}

.source-meta {
    display: flex;
    gap: 16px;
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.source-actions {
    display: flex;
    gap: 8px;
}

.action-btn {
    padding: 6px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: white;
    color: var(--text-color);
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: var(--transition);
}

.action-btn:hover {
    background: #f0f0f0;
}

.action-btn.primary {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.action-btn.primary:hover {
    background: var(--primary-hover);
}

/* 网络统计 */
.network-stats {
    padding: 20px 24px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.stat-card {
    padding: 16px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: var(--border-radius);
    text-align: center;
}

.stat-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-bottom: 8px;
}

.stat-icon {
    font-size: 18px;
}

.stat-title {
    font-size: var(--font-size-sm);
    opacity: 0.9;
}

.stat-card .stat-value {
    font-size: var(--font-size-xl);
    font-weight: bold;
    color: white;
}

/* 网络拓扑 */
.network-topology {
    padding: 20px 24px;
    border-bottom: 1px solid #f0f0f0;
}

.topology-container {
    min-height: 300px;
    background: #fafafa;
    border: 1px solid #f0f0f0;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    font-size: var(--font-size-base);
}

/* 监控图表 */
.monitoring-charts {
    padding: 20px 24px;
    border-bottom: 1px solid #f0f0f0;
}

.chart-container {
    margin-bottom: 24px;
    border: 1px solid #f0f0f0;
    border-radius: var(--border-radius);
    overflow: hidden;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #fafafa;
    border-bottom: 1px solid #f0f0f0;
}

.chart-header h4 {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
}

.chart-controls select {
    padding: 4px 8px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
}

.chart-content {
    height: 200px;
    padding: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    background: white;
}

/* 使用统计表 */
.terminal-usage {
    padding: 20px 24px;
}

.terminal-usage h4 {
    margin-bottom: 16px;
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--text-color);
}

.usage-table {
    border: 1px solid #f0f0f0;
    border-radius: var(--border-radius);
    overflow: hidden;
}

.table-header {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 1fr 1fr;
    gap: 16px;
    padding: 12px 16px;
    background: #fafafa;
    border-bottom: 1px solid #f0f0f0;
    font-weight: 600;
    font-size: var(--font-size-sm);
    color: var(--text-color);
}

.table-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 1fr 1fr;
    gap: 16px;
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    font-size: var(--font-size-sm);
    color: var(--text-color);
}

.table-row:last-child {
    border-bottom: none;
}

.table-row:hover {
    background: #fafafa;
}

/* 成本卡片 */
.cost-overview {
    padding: 20px 24px;
    border-bottom: 1px solid #f0f0f0;
}

.cost-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.cost-card {
    padding: 20px;
    background: var(--card-bg);
    border: 1px solid #f0f0f0;
    border-radius: var(--border-radius);
}

.cost-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
}

.cost-icon {
    font-size: 18px;
    color: var(--primary-color);
}

.cost-title {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.cost-amount {
    font-size: 24px;
    font-weight: bold;
    color: var(--text-color);
    margin-bottom: 4px;
}

.cost-budget {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-bottom: 12px;
}

.cost-progress {
    margin-bottom: 8px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background-color: #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--success-color), var(--warning-color));
    transition: width 0.3s ease;
}

.cost-status {
    text-align: center;
}

/* 成本分析 */
.cost-analysis {
    padding: 20px 24px;
    border-bottom: 1px solid #f0f0f0;
}

.analysis-section {
    margin-bottom: 24px;
}

.analysis-section:last-child {
    margin-bottom: 0;
}

.analysis-section h4 {
    margin-bottom: 16px;
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--text-color);
}

.cost-breakdown {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.breakdown-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #fafafa;
    border-radius: var(--border-radius);
    border: 1px solid #f0f0f0;
}

.breakdown-label {
    font-weight: 500;
    color: var(--text-color);
}

.breakdown-value {
    font-weight: 600;
    color: var(--primary-color);
}

/* 优化建议 */
.cost-optimization {
    padding: 20px 24px;
}

.cost-optimization h4 {
    margin-bottom: 16px;
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--text-color);
}

.optimization-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.optimization-item {
    padding: 16px;
    background: var(--factory-light);
    border: 1px solid #ffd591;
    border-radius: var(--border-radius);
}

.optimization-title {
    font-weight: 600;
    color: var(--factory-orange);
    margin-bottom: 8px;
}

.optimization-description {
    font-size: var(--font-size-sm);
    color: var(--text-color);
    margin-bottom: 8px;
}

.optimization-impact {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

/* 模态框样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.45);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: var(--card-bg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow-lg);
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.modal-content.large {
    max-width: 900px;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 20px;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: var(--transition);
}

.modal-close:hover {
    background-color: #f5f5f5;
    color: var(--text-color);
}

.modal-body {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 20px;
    border-top: 1px solid var(--border-color);
}

/* 表单样式 */
.source-form {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    font-weight: 500;
    color: var(--text-color);
    font-size: var(--font-size-sm);
}

.form-group input,
.form-group select {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: var(--font-size-base);
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .water-management-container {
        padding: 16px;
    }
    
    .management-header {
        flex-direction: column;
        gap: 16px;
        text-align: center;
    }
    
    .tab-navigation {
        flex-wrap: wrap;
    }
    
    .tab-btn {
        flex: none;
        min-width: 120px;
    }
    
    .overview-stats {
        grid-template-columns: 1fr;
    }
    
    .config-grid {
        grid-template-columns: 1fr;
    }
    
    .cost-cards {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .modal-content {
        width: 95%;
        margin: 20px;
    }
    
    .table-header,
    .table-row {
        grid-template-columns: 1fr;
        gap: 8px;
    }
    
    .source-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }
    
    .source-actions {
        width: 100%;
        justify-content: flex-end;
    }
}

@media (max-width: 480px) {
    .water-management-container {
        padding: 12px;
    }
    
    .header-actions {
        flex-direction: column;
        width: 100%;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .source-meta {
        flex-direction: column;
        gap: 4px;
    }
}

/* 暗色模式支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-color: #141414;
        --card-bg: #1f1f1f;
        --border-color: #434343;
        --text-color: #ffffff;
        --text-secondary: #a6a6a6;
        --text-disabled: #595959;
        
        --water-light: #111b26;
        --factory-light: #2b1d11;
        --pipeline-light: #162312;
    }
    
    .management-header {
        background: linear-gradient(135deg, #1890ff 0%, #fa8c16 100%);
    }
    
    .stat-item {
        background: #262626;
        border-color: #434343;
    }
    
    .source-item {
        background: #262626;
        border-color: #434343;
    }
    
    .source-item:hover {
        background: #303030;
    }
    
    .chart-content {
        background: #1f1f1f;
    }
    
    .chart-header {
        background: #262626;
        border-color: #434343;
    }
    
    .table-header {
        background: #262626;
        border-color: #434343;
    }
    
    .table-row:hover {
        background: #262626;
    }
    
    .cost-card {
        background: #1f1f1f;
        border-color: #434343;
    }
    
    .breakdown-item {
        background: #262626;
        border-color: #434343;
    }
    
    .optimization-item {
        background: #2b1d11;
        border-color: #613400;
    }
}
