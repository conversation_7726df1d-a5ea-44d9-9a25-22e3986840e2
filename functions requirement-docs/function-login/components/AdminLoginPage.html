<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录 - 大学生就业调研平台</title>
    <link rel="stylesheet" href="AdminLoginPage.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <!-- 登录头部 -->
            <div class="login-header">
                <h1 class="login-title">管理员登录</h1>
                <p class="login-description">请输入您的管理员账号和密码</p>
            </div>

            <!-- 演示项目提示 -->
            <div class="demo-notice">
                <h4 class="demo-title">演示项目</h4>
                <p class="demo-text">
                    这是一个演示项目，使用模拟数据。您可以使用下方的一键登录按钮直接体验不同角色的功能，无需输入用户名和密码。
                </p>
            </div>

            <!-- 登录表单 -->
            <form id="loginForm" class="login-form">
                <!-- 用户名输入 -->
                <div class="form-group">
                    <label for="username" class="form-label">用户名</label>
                    <input 
                        type="text" 
                        id="username" 
                        name="username" 
                        class="form-input" 
                        placeholder="请输入用户名"
                        required
                    >
                    <div class="form-error" id="usernameError"></div>
                </div>

                <!-- 密码输入 -->
                <div class="form-group">
                    <label for="password" class="form-label">密码</label>
                    <div class="password-input-wrapper">
                        <input 
                            type="password" 
                            id="password" 
                            name="password" 
                            class="form-input" 
                            placeholder="请输入密码"
                            required
                        >
                        <button type="button" class="password-toggle" id="passwordToggle">
                            <svg class="eye-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                                <circle cx="12" cy="12" r="3"></circle>
                            </svg>
                        </button>
                    </div>
                    <div class="form-error" id="passwordError"></div>
                </div>

                <!-- 登录按钮 -->
                <button type="submit" class="login-button" id="loginButton">
                    <span class="button-text">登录</span>
                    <div class="loading-spinner" id="loadingSpinner"></div>
                </button>
            </form>

            <!-- 一键登录区域 -->
            <div class="quick-login-section">
                <div class="section-divider">
                    <span class="divider-text">或使用一键登录</span>
                </div>

                <!-- 管理员登录 -->
                <div class="quick-login-group">
                    <h5 class="group-title">管理员</h5>
                    <button type="button" class="quick-login-button admin-button" data-role="admin">
                        管理员
                    </button>
                </div>

                <!-- 超级管理员登录 -->
                <div class="quick-login-group">
                    <h5 class="group-title">超级管理员</h5>
                    <button type="button" class="quick-login-button superadmin-button" data-role="superadmin">
                        超级管理员
                    </button>
                </div>

                <!-- 审核员登录 -->
                <div class="quick-login-group">
                    <h5 class="group-title">审核员</h5>
                    <div class="reviewer-buttons">
                        <button type="button" class="quick-login-button reviewer-button" data-role="reviewerA">
                            审核员A
                        </button>
                        <button type="button" class="quick-login-button reviewer-button" data-role="reviewerB">
                            审核员B
                        </button>
                        <button type="button" class="quick-login-button reviewer-button" data-role="reviewerC">
                            审核员C
                        </button>
                    </div>
                </div>
            </div>

            <!-- 帮助信息 -->
            <div class="help-section">
                <p class="help-text">
                    忘记密码？请联系系统管理员
                </p>
            </div>
        </div>
    </div>

    <!-- 消息提示容器 -->
    <div id="toastContainer" class="toast-container"></div>

    <!-- 加载脚本 -->
    <script src="../services/authService.js"></script>
    <script src="AdminLoginPage.js"></script>
</body>
</html>
