/* 管理员登录页面样式 */

/* CSS变量定义 */
:root {
    --primary-color: #3b82f6;
    --primary-hover: #2563eb;
    --success-color: #10b981;
    --error-color: #ef4444;
    --warning-color: #f59e0b;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    --blue-50: #eff6ff;
    --blue-100: #dbeafe;
    --blue-200: #bfdbfe;
    --blue-600: #2563eb;
    --blue-700: #1d4ed8;
    --border-radius: 0.5rem;
    --border-radius-lg: 0.75rem;
    --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --box-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --transition: all 0.15s ease-in-out;
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background-color: var(--gray-100);
    color: var(--gray-900);
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 登录容器 */
.login-container {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: 1rem;
}

/* 登录卡片 */
.login-card {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow-lg);
    padding: 2rem;
    width: 100%;
    max-width: 28rem;
    border: 1px solid var(--gray-200);
}

/* 登录头部 */
.login-header {
    text-align: center;
    margin-bottom: 1.5rem;
}

.login-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 0.5rem;
}

.login-description {
    color: var(--gray-600);
    font-size: 0.875rem;
}

/* 演示项目提示 */
.demo-notice {
    background: var(--blue-50);
    border: 1px solid var(--blue-200);
    border-radius: var(--border-radius);
    padding: 0.75rem;
    margin-bottom: 1.5rem;
}

.demo-title {
    color: var(--blue-700);
    font-weight: 500;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.demo-text {
    color: var(--blue-600);
    font-size: 0.75rem;
    line-height: 1.5;
}

/* 表单样式 */
.login-form {
    margin-bottom: 1.5rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-label {
    display: block;
    font-weight: 500;
    font-size: 0.875rem;
    color: var(--gray-700);
    margin-bottom: 0.5rem;
}

.form-input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    transition: var(--transition);
    background: white;
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input::placeholder {
    color: var(--gray-400);
}

/* 密码输入框 */
.password-input-wrapper {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--gray-400);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 0.25rem;
    transition: var(--transition);
}

.password-toggle:hover {
    color: var(--gray-600);
}

.eye-icon {
    width: 1rem;
    height: 1rem;
    stroke-width: 2;
}

/* 表单错误 */
.form-error {
    color: var(--error-color);
    font-size: 0.75rem;
    margin-top: 0.25rem;
    min-height: 1rem;
}

/* 登录按钮 */
.login-button {
    width: 100%;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-button:hover:not(:disabled) {
    background: var(--primary-hover);
}

.login-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.button-text {
    transition: var(--transition);
}

.login-button.loading .button-text {
    opacity: 0;
}

/* 加载动画 */
.loading-spinner {
    position: absolute;
    width: 1rem;
    height: 1rem;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    opacity: 0;
    transition: var(--transition);
}

.login-button.loading .loading-spinner {
    opacity: 1;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 一键登录区域 */
.quick-login-section {
    margin-bottom: 1.5rem;
}

.section-divider {
    position: relative;
    text-align: center;
    margin: 1.5rem 0;
}

.section-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--gray-200);
}

.divider-text {
    background: white;
    color: var(--gray-500);
    font-size: 0.75rem;
    padding: 0 1rem;
    position: relative;
}

/* 一键登录组 */
.quick-login-group {
    margin-bottom: 1rem;
}

.group-title {
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--gray-600);
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* 一键登录按钮 */
.quick-login-button {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    background: white;
    color: var(--gray-700);
    font-size: 0.75rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    margin-bottom: 0.5rem;
}

.quick-login-button:hover:not(:disabled) {
    background: var(--gray-50);
    border-color: var(--gray-400);
}

.quick-login-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* 角色特定按钮样式 */
.admin-button {
    background: #f0f9ff;
    border-color: #bfdbfe;
    color: var(--blue-700);
}

.admin-button:hover:not(:disabled) {
    background: #dbeafe;
    border-color: #93c5fd;
}

.superadmin-button {
    background: #fef3c7;
    border-color: #fcd34d;
    color: #92400e;
}

.superadmin-button:hover:not(:disabled) {
    background: #fde68a;
    border-color: #f59e0b;
}

.reviewer-button {
    background: #f0fdf4;
    border-color: #bbf7d0;
    color: #166534;
}

.reviewer-button:hover:not(:disabled) {
    background: #dcfce7;
    border-color: #86efac;
}

/* 审核员按钮组 */
.reviewer-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
}

.reviewer-buttons .quick-login-button {
    margin-bottom: 0;
}

/* 帮助区域 */
.help-section {
    text-align: center;
    padding-top: 1rem;
    border-top: 1px solid var(--gray-200);
}

.help-text {
    color: var(--gray-500);
    font-size: 0.75rem;
}

/* 消息提示 */
.toast-container {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 1000;
}

.toast {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-lg);
    padding: 1rem;
    margin-bottom: 0.5rem;
    border-left: 4px solid var(--primary-color);
    max-width: 20rem;
    animation: slideIn 0.3s ease-out;
}

.toast.success {
    border-left-color: var(--success-color);
}

.toast.error {
    border-left-color: var(--error-color);
}

.toast-title {
    font-weight: 600;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.toast-description {
    font-size: 0.75rem;
    color: var(--gray-600);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 响应式设计 */
@media (max-width: 640px) {
    .login-container {
        padding: 0.5rem;
    }

    .login-card {
        padding: 1.5rem;
    }

    .login-title {
        font-size: 1.5rem;
    }

    .reviewer-buttons {
        grid-template-columns: 1fr;
    }
}

/* 暗色模式支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --gray-50: #1f2937;
        --gray-100: #111827;
        --gray-200: #374151;
        --gray-300: #4b5563;
        --gray-400: #6b7280;
        --gray-500: #9ca3af;
        --gray-600: #d1d5db;
        --gray-700: #e5e7eb;
        --gray-800: #f3f4f6;
        --gray-900: #f9fafb;
    }

    body {
        background-color: var(--gray-100);
        color: var(--gray-900);
    }

    .login-card {
        background: var(--gray-50);
        border-color: var(--gray-200);
    }

    .form-input {
        background: var(--gray-100);
        border-color: var(--gray-300);
        color: var(--gray-900);
    }

    .quick-login-button {
        background: var(--gray-100);
        border-color: var(--gray-300);
        color: var(--gray-700);
    }
}
