{"encryption": {"algorithm": "SHA-256", "salt": "college_employment_survey_2024", "keyLength": 32, "iterations": 10000, "webCryptoEnabled": true, "fallbackToSimpleHash": true}, "authentication": {"tokenExpiration": 86400000, "sessionTimeout": 86400000, "maxLoginAttempts": 5, "lockoutDuration": 900000, "rememberDuration": 604800000, "autoLogoutOnInactivity": true, "inactivityTimeout": 3600000}, "validation": {"aValue": {"pattern": "^\\d{11}$", "minLength": 11, "maxLength": 11, "required": true, "description": "11位数字，通常为手机号"}, "bValue": {"pattern": "^\\d{4}$|^\\d{6}$", "allowedLengths": [4, 6], "required": true, "description": "4位或6位数字密码"}, "realTimeValidation": true, "showValidationStatus": true}, "storage": {"useEncryption": true, "storageType": "localStorage", "keyPrefix": "ab_auth_", "cleanupExpired": true, "cleanupInterval": 3600000, "maxStorageSize": 1048576}, "security": {"preventBruteForce": true, "rateLimiting": {"enabled": true, "maxAttempts": 5, "windowMs": 900000, "blockDuration": 1800000}, "fingerprinting": {"enabled": true, "trackDevice": true, "trackBrowser": true, "trackScreen": true}, "csrfProtection": true, "xssProtection": true, "contentSecurityPolicy": true}, "privacy": {"storeOriginalValues": false, "hashSensitiveData": true, "anonymizeUserData": true, "dataRetentionDays": 30, "allowDataExport": false, "allowDataDeletion": true}, "logging": {"enabled": true, "logLevel": "info", "logAuthAttempts": true, "logSecurityEvents": true, "logDataAccess": false, "maxLogSize": 10485760, "logRotation": true}, "features": {"rememberIdentity": true, "autoFillLastUsed": false, "showPasswordStrength": false, "allowPasswordVisibility": true, "showSecurityTips": true, "enableHelpSystem": true}, "ui": {"theme": "default", "showProgressIndicator": true, "animationEnabled": true, "responsiveDesign": true, "accessibilityEnabled": true, "keyboardNavigation": true}, "api": {"baseUrl": "https://college-employment-survey-api-isolated.justpm2099.workers.dev/api", "timeout": 30000, "retryAttempts": 3, "retryDelay": 1000, "useHttps": true, "validateCertificates": true}, "development": {"debugMode": false, "mockData": true, "testAccounts": [{"a": "***********", "b": "1234", "name": "测试用户1", "description": "基础测试账号"}, {"a": "***********", "b": "123456", "name": "测试用户2", "description": "6位密码测试账号"}, {"a": "***********", "b": "0000", "name": "测试用户3", "description": "简单密码测试账号"}, {"a": "***********", "b": "888888", "name": "测试用户4", "description": "重复数字密码测试账号"}, {"a": "***********", "b": "1111", "name": "测试用户5", "description": "重复数字A值测试账号"}, {"a": "***********", "b": "222222", "name": "测试用户6", "description": "全重复数字测试账号"}], "enableTestMode": true, "skipValidation": false, "logVerbose": true}, "compliance": {"gdprCompliant": true, "ccpaCompliant": true, "dataMinimization": true, "consentRequired": false, "privacyPolicyUrl": "/privacy", "termsOfServiceUrl": "/terms"}, "monitoring": {"enabled": true, "trackLoginSuccess": true, "trackLoginFailure": true, "trackSecurityEvents": true, "trackPerformance": true, "alertOnSuspiciousActivity": true, "metricsEndpoint": "/api/metrics"}, "backup": {"enabled": false, "backupInterval": 86400000, "maxBackups": 7, "backupLocation": "local", "encryptBackups": true}, "integration": {"singleSignOn": false, "ldapIntegration": false, "oauthProviders": [], "webhooks": {"enabled": false, "onLogin": null, "onLogout": null, "onSecurityEvent": null}}, "performance": {"cacheEnabled": true, "cacheDuration": 300000, "preloadResources": true, "lazyLoading": true, "compressionEnabled": true, "minifyAssets": true}, "errorHandling": {"showDetailedErrors": false, "logErrors": true, "errorReporting": false, "fallbackBehavior": "graceful", "retryOnFailure": true, "maxRetries": 3}}