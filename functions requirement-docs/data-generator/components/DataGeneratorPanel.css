/* 数据生成器面板样式 */

/* CSS变量定义 */
:root {
    --primary-color: #1890ff;
    --primary-hover: #40a9ff;
    --success-color: #52c41a;
    --success-hover: #73d13d;
    --warning-color: #faad14;
    --warning-hover: #ffc53d;
    --error-color: #ff4d4f;
    --error-hover: #ff7875;
    --secondary-color: #d9d9d9;
    --secondary-hover: #bfbfbf;
    
    --bg-color: #f0f2f5;
    --card-bg: #ffffff;
    --border-color: #d9d9d9;
    --text-color: #262626;
    --text-secondary: #8c8c8c;
    --text-disabled: #bfbfbf;
    
    --border-radius: 6px;
    --border-radius-lg: 8px;
    --box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    --box-shadow-lg: 0 4px 12px rgba(0, 0, 0, 0.15);
    --transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-sm: 12px;
    --font-size-base: 14px;
    --font-size-lg: 16px;
    --font-size-xl: 18px;
    --font-size-xxl: 20px;
}

/* 全局样式重置 */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.5715;
    color: var(--text-color);
    background-color: var(--bg-color);
}

/* 主容器 */
.generator-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 24px;
}

/* 头部样式 */
.generator-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 16px 24px;
    background: var(--card-bg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow);
}

.generator-title {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: var(--font-size-xxl);
    font-weight: 600;
    color: var(--text-color);
}

.title-icon {
    font-size: 24px;
}

.header-actions {
    display: flex;
    gap: 12px;
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border: 1px solid transparent;
    border-radius: var(--border-radius);
    font-size: var(--font-size-base);
    font-weight: 500;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    white-space: nowrap;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
    color: white;
}

.btn-success:hover:not(:disabled) {
    background-color: var(--success-hover);
    border-color: var(--success-hover);
}

.btn-secondary {
    background-color: var(--card-bg);
    border-color: var(--border-color);
    color: var(--text-color);
}

.btn-secondary:hover:not(:disabled) {
    background-color: var(--secondary-hover);
    border-color: var(--secondary-hover);
}

.btn-icon {
    font-size: 16px;
}

/* 警告提示 */
.alert {
    display: flex;
    gap: 12px;
    padding: 16px;
    margin-bottom: 24px;
    border-radius: var(--border-radius);
    border: 1px solid;
}

.alert-warning {
    background-color: #fffbe6;
    border-color: #ffe58f;
    color: #d48806;
}

.alert-icon {
    font-size: 20px;
    flex-shrink: 0;
}

.alert-content h4 {
    margin-bottom: 8px;
    font-weight: 600;
}

.alert-content ul {
    margin: 8px 0 0 20px;
}

.alert-content li {
    margin-bottom: 4px;
}

/* 标签页导航 */
.tab-navigation {
    display: flex;
    gap: 4px;
    margin-bottom: 24px;
    background: var(--card-bg);
    padding: 4px;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow);
}

.tab-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    border: none;
    border-radius: var(--border-radius);
    background: transparent;
    color: var(--text-secondary);
    font-size: var(--font-size-base);
    cursor: pointer;
    transition: var(--transition);
    flex: 1;
    justify-content: center;
}

.tab-btn:hover {
    background-color: #f5f5f5;
    color: var(--text-color);
}

.tab-btn.active {
    background-color: var(--primary-color);
    color: white;
}

.tab-icon {
    font-size: 16px;
}

/* 标签页内容 */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 仪表板网格 */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
}

/* 统计卡片 */
.stat-card {
    background: var(--card-bg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow);
    overflow: hidden;
}

.stat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-color);
}

.stat-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-color);
}

.stat-icon {
    font-size: 20px;
    color: var(--text-secondary);
}

.stat-content {
    padding: 20px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.stat-item:last-child {
    margin-bottom: 0;
}

.stat-label {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.stat-value {
    font-weight: 600;
    font-size: var(--font-size-lg);
    color: var(--text-color);
}

/* AI提供商状态 */
.provider-status {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.provider-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.provider-name {
    font-weight: 500;
    color: var(--text-color);
}

.provider-status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.provider-status-badge.active {
    background-color: #f6ffed;
    color: var(--success-color);
    border: 1px solid #b7eb8f;
}

.provider-status-badge.disabled {
    background-color: #fff2e8;
    color: var(--warning-color);
    border: 1px solid #ffd591;
}

.provider-status-badge.inactive {
    background-color: #f5f5f5;
    color: var(--text-disabled);
    border: 1px solid var(--border-color);
}

/* 活动卡片 */
.activity-card {
    grid-column: 1 / -1;
    background: var(--card-bg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow);
    overflow: hidden;
}

.activity-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-color);
}

.activity-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-color);
}

.activity-icon {
    font-size: 20px;
    color: var(--text-secondary);
}

.activity-content {
    padding: 20px;
    max-height: 300px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    gap: 12px;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-time {
    flex-shrink: 0;
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    width: 80px;
}

.activity-description {
    flex: 1;
    color: var(--text-color);
}

.activity-status {
    flex-shrink: 0;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.activity-status.success {
    background-color: #f6ffed;
    color: var(--success-color);
}

.activity-status.error {
    background-color: #fff2f0;
    color: var(--error-color);
}

.activity-status.warning {
    background-color: #fffbe6;
    color: var(--warning-color);
}

/* 生成面板 */
.generation-panel {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 24px;
}

.panel-left,
.panel-right {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

/* 配置卡片 */
.config-card,
.preview-card,
.stats-card {
    background: var(--card-bg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow);
    overflow: hidden;
}

.config-card h3,
.preview-card h3,
.stats-card h3 {
    padding: 16px 20px;
    margin: 0;
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-color);
    border-bottom: 1px solid var(--border-color);
}

/* 表单样式 */
.config-form {
    padding: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    margin-bottom: 16px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    font-weight: 500;
    color: var(--text-color);
    font-size: var(--font-size-sm);
}

.form-group input,
.form-group select {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: var(--font-size-base);
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.form-group small {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.form-group input[type="range"] {
    padding: 0;
}

.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.checkbox-item {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

.checkbox-item input[type="checkbox"] {
    margin: 0;
    width: 16px;
    height: 16px;
}

.form-actions {
    display: flex;
    gap: 12px;
    margin-top: 24px;
}

.form-actions .btn {
    flex: 1;
}

/* 预览内容 */
.preview-content {
    padding: 20px;
}

.preview-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.preview-item:last-child {
    margin-bottom: 0;
}

.preview-label {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.preview-value {
    font-weight: 500;
    color: var(--text-color);
}

/* 统计内容 */
.stats-content {
    padding: 20px;
    text-align: center;
}

.stats-item {
    margin-bottom: 16px;
}

.stats-item:last-child {
    margin-bottom: 0;
}

.stats-number {
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-color);
    line-height: 1;
}

.stats-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-top: 4px;
}

.stats-suffix {
    font-size: var(--font-size-sm);
    color: var(--text-disabled);
}

.stats-divider {
    height: 1px;
    background-color: var(--border-color);
    margin: 16px 0;
}

/* 故事预览 */
.story-preview {
    padding: 20px;
}

.story-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 12px;
    line-height: 1.4;
}

.story-content {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 16px;
}

.story-meta {
    display: flex;
    gap: 12px;
    align-items: center;
}

.story-category,
.story-length {
    padding: 4px 8px;
    background-color: #f0f0f0;
    border-radius: 12px;
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

/* 用户ID预览 */
.user-id-preview {
    margin-top: 20px;
    padding: 16px;
    background-color: #f9f9f9;
    border-radius: var(--border-radius);
}

.user-id-preview h4 {
    margin-bottom: 8px;
    font-size: var(--font-size-base);
    color: var(--text-color);
}

.user-id-preview code {
    display: block;
    padding: 8px;
    background-color: #f0f0f0;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: var(--font-size-sm);
    margin-bottom: 8px;
}

.user-id-preview p {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin: 0;
}

/* 本地测试面板 */
.local-test-panel {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.test-info-card,
.test-config-card,
.test-results-card {
    background: var(--card-bg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow);
    padding: 20px;
}

.test-info-card h3,
.test-config-card h4,
.test-results-card h4 {
    margin-bottom: 12px;
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-color);
}

.test-info-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* 历史面板 */
.history-panel {
    background: var(--card-bg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow);
    overflow: hidden;
}

.history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-color);
}

.history-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-color);
}

.history-actions {
    display: flex;
    gap: 12px;
}

.history-filters {
    display: flex;
    gap: 12px;
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-color);
    background-color: #fafafa;
}

.history-filters select,
.history-filters input {
    padding: 6px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
}

.history-list {
    max-height: 500px;
    overflow-y: auto;
}

.history-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
    transition: var(--transition);
}

.history-item:hover {
    background-color: #fafafa;
}

.history-item:last-child {
    border-bottom: none;
}

.history-info {
    flex: 1;
}

.history-title {
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 4px;
}

.history-meta {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.history-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.history-status.completed {
    background-color: #f6ffed;
    color: var(--success-color);
}

.history-status.running {
    background-color: #e6f7ff;
    color: var(--primary-color);
}

.history-status.failed {
    background-color: #fff2f0;
    color: var(--error-color);
}

.history-status.stopped {
    background-color: #f5f5f5;
    color: var(--text-disabled);
}

/* 模态框样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.45);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: var(--card-bg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow-lg);
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-color);
}

.modal-close {
    background: none;
    border: none;
    font-size: 20px;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: var(--transition);
}

.modal-close:hover {
    background-color: #f5f5f5;
    color: var(--text-color);
}

.modal-body {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 20px;
    border-top: 1px solid var(--border-color);
}

/* 进度条样式 */
.progress-info {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 20px;
}

.progress-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.progress-label {
    font-weight: 500;
    color: var(--text-color);
}

.progress-value {
    color: var(--text-secondary);
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.progress-bar-container {
    position: relative;
    margin-bottom: 20px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background-color: #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background-color: var(--primary-color);
    transition: width 0.3s ease;
}

.progress-percentage {
    position: absolute;
    top: -24px;
    right: 0;
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.progress-details {
    background-color: #f9f9f9;
    border-radius: var(--border-radius);
    padding: 16px;
    max-height: 200px;
    overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .generator-container {
        padding: 16px;
    }

    .generator-header {
        flex-direction: column;
        gap: 16px;
        text-align: center;
    }

    .tab-navigation {
        flex-wrap: wrap;
    }

    .tab-btn {
        flex: none;
        min-width: 120px;
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .generation-panel {
        grid-template-columns: 1fr;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .history-filters {
        flex-direction: column;
    }

    .history-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .modal-content {
        width: 95%;
        margin: 20px;
    }
}

@media (max-width: 480px) {
    .generator-container {
        padding: 12px;
    }

    .header-actions {
        flex-direction: column;
        width: 100%;
    }

    .form-actions {
        flex-direction: column;
    }

    .history-actions {
        flex-direction: column;
        gap: 8px;
    }
}

/* 暗色模式支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-color: #141414;
        --card-bg: #1f1f1f;
        --border-color: #434343;
        --text-color: #ffffff;
        --text-secondary: #a6a6a6;
        --text-disabled: #595959;
    }

    .alert-warning {
        background-color: #2d2a1f;
        border-color: #594214;
        color: #d89614;
    }

    .provider-status-badge.active {
        background-color: #162312;
        color: #52c41a;
        border-color: #274916;
    }

    .provider-status-badge.disabled {
        background-color: #2b1d11;
        color: #fa8c16;
        border-color: #613400;
    }

    .activity-status.success {
        background-color: #162312;
        color: #52c41a;
    }

    .activity-status.error {
        background-color: #2a1215;
        color: #ff4d4f;
    }

    .activity-status.warning {
        background-color: #2b1d11;
        color: #fa8c16;
    }

    .history-status.completed {
        background-color: #162312;
        color: #52c41a;
    }

    .history-status.running {
        background-color: #111b26;
        color: #1890ff;
    }

    .history-status.failed {
        background-color: #2a1215;
        color: #ff4d4f;
    }
}
