# 用户权限体系设计

## 概述

本项目采用4层用户权限体系，实现精细化的权限控制和角色隔离，确保数据安全和用户体验的平衡。

## 用户角色定义

### 1. 普通浏览用户 (Guest)
**角色标识**: `guest` | **无需注册登录**

**权限范围**:
- ✅ 访问首页
- ✅ 查看数据可视化页面 (只读)
- ✅ 浏览故事墙内容 (已发布的故事)
- ✅ 查看问卷心声分享 (已审核的心声)
- ❌ 填写问卷
- ❌ 发布内容
- ❌ 下载数据

**访问路径**:
- `/` - 首页
- `/analytics` - 数据可视化 (只读模式)
- `/stories` - 故事墙 (浏览模式)
- `/voices` - 问卷心声 (浏览模式)

### 2. 注册用户 (User)
**角色标识**: `user` | **快捷注册/匿名注册**

**权限范围**:
- ✅ 普通浏览用户的所有权限
- ✅ 填写问卷 (匿名或实名)
- ✅ 发布故事到故事墙
- ✅ 发表问卷心声
- ✅ 查看自己的发布记录
- ✅ 下载数据可视化报告
- ✅ 编辑/删除自己的内容
- ❌ 查看其他用户信息
- ❌ 审核功能

**访问路径**:
- 包含普通浏览用户所有路径
- `/questionnaire` - 问卷填写
- `/profile` - 个人中心
- `/my-submissions` - 我的提交记录

**注册方式**:
- **快捷注册**: 仅需用户名，自动生成匿名ID
- **完整注册**: 用户名 + 邮箱 + 密码
- **匿名模式**: 临时令牌，无需任何信息

### 3. 审核员 (Reviewer)
**角色标识**: `reviewer` | **管理员创建账号**

**权限范围**:
- ✅ 专用审核工作台
- ✅ 审核问卷提交
- ✅ 审核故事墙内容
- ✅ 审核问卷心声
- ✅ 查看审核历史
- ✅ 导出审核报告
- ❌ 用户管理
- ❌ 系统设置
- ❌ 创建其他审核员

**访问路径**:
- `/reviewer` - 审核员专用入口
- `/reviewer/dashboard` - 审核工作台
- `/reviewer/questionnaires` - 问卷审核
- `/reviewer/stories` - 故事审核
- `/reviewer/voices` - 心声审核
- `/reviewer/history` - 审核历史

**登录入口**: 独立的审核员登录页面 `/reviewer/login`

### 4. 管理员 (Admin)
**角色标识**: `admin` / `super_admin` | **系统预设账号**

**权限范围**:
- ✅ 所有用户权限
- ✅ 用户管理 (创建、编辑、禁用)
- ✅ 审核员管理
- ✅ 系统设置
- ✅ 数据统计分析
- ✅ 日志查看
- ✅ 权限分配
- ✅ 系统监控

**访问路径**:
- `/admin` - 管理员控制台
- `/admin/users` - 用户管理
- `/admin/reviewers` - 审核员管理
- `/admin/system` - 系统设置
- `/admin/analytics` - 高级数据分析
- `/admin/logs` - 系统日志
- `/admin/api-data` - API与数据管理

**登录入口**: 独立的管理员登录页面 `/admin/login`

---

## 权限矩阵

| 功能模块 | 普通浏览用户 | 注册用户 | 审核员 | 管理员 |
|---------|-------------|---------|--------|--------|
| **基础浏览** |
| 首页访问 | ✅ | ✅ | ✅ | ✅ |
| 数据可视化查看 | ✅ (只读) | ✅ (可下载) | ✅ | ✅ |
| 故事墙浏览 | ✅ | ✅ | ✅ | ✅ |
| 问卷心声浏览 | ✅ | ✅ | ✅ | ✅ |
| **内容创建** |
| 问卷填写 | ❌ | ✅ | ❌ | ✅ |
| 发布故事 | ❌ | ✅ | ❌ | ✅ |
| 发表心声 | ❌ | ✅ | ❌ | ✅ |
| **内容管理** |
| 查看个人记录 | ❌ | ✅ | ❌ | ✅ |
| 编辑个人内容 | ❌ | ✅ | ❌ | ✅ |
| 删除个人内容 | ❌ | ✅ | ❌ | ✅ |
| **审核功能** |
| 问卷审核 | ❌ | ❌ | ✅ | ✅ |
| 故事审核 | ❌ | ❌ | ✅ | ✅ |
| 心声审核 | ❌ | ❌ | ✅ | ✅ |
| 审核历史查看 | ❌ | ❌ | ✅ | ✅ |
| **系统管理** |
| 用户管理 | ❌ | ❌ | ❌ | ✅ |
| 审核员管理 | ❌ | ❌ | ❌ | ✅ |
| 系统设置 | ❌ | ❌ | ❌ | ✅ |
| 日志查看 | ❌ | ❌ | ❌ | ✅ |
| API管理 | ❌ | ❌ | ❌ | ✅ |

---

## 登录入口设计

### 1. 普通用户登录
**路径**: `/login`
**特点**: 
- 支持快捷注册
- 支持匿名模式
- 友好的用户界面

### 2. 审核员登录
**路径**: `/reviewer/login`
**特点**:
- 独立的登录界面
- 严格的身份验证
- 直接跳转到审核工作台

### 3. 管理员登录
**路径**: `/admin/login`
**特点**:
- 最高安全级别
- 多重身份验证
- 详细的登录日志

---

## 菜单导航设计

### 普通浏览用户导航
```
首页 | 数据可视化 | 故事墙 | 问卷心声 | [登录/注册]
```

### 注册用户导航
```
首页 | 问卷调查 | 数据可视化 | 故事墙 | 问卷心声 | 个人中心 | [退出]
```

### 审核员导航
```
审核工作台 | 问卷审核 | 故事审核 | 心声审核 | 审核历史 | [退出]
```

### 管理员导航
```
管理控制台 | 用户管理 | 审核员管理 | 内容管理 | 系统设置 | 数据分析 | API管理 | [退出]
```

---

## 匿名用户系统设计

### 快捷注册流程
1. 用户点击"快速参与"
2. 输入昵称 (可选)
3. 系统生成匿名ID: `anon_${timestamp}_${random}`
4. 颁发临时令牌，有效期30天
5. 用户可正常使用所有注册用户功能

### 匿名令牌机制
- **令牌格式**: JWT，包含匿名ID和权限信息
- **存储方式**: localStorage + httpOnly Cookie
- **有效期**: 30天，可续期
- **权限**: 等同于注册用户

### 数据关联
- 匿名用户的所有提交都关联到匿名ID
- 支持后续绑定邮箱升级为正式用户
- 匿名数据可选择性公开或私有

---

## 权限控制实现

### 前端权限控制
- 路由守卫 (Route Guards)
- 组件级权限检查
- 菜单动态显示/隐藏
- 按钮级权限控制

### 后端权限控制
- JWT令牌验证
- 角色权限中间件
- API接口权限检查
- 数据访问权限控制

### 数据库权限设计
```sql
-- 用户表权限字段
users.role: 'guest', 'user', 'reviewer', 'admin', 'super_admin'
users.permissions: JSON字段，存储细粒度权限
users.is_anonymous: 是否为匿名用户
users.anonymous_id: 匿名用户ID
```

---

## 安全考虑

### 权限隔离
- 不同角色使用不同的登录入口
- 严格的权限边界检查
- 敏感操作需要二次确认

### 数据保护
- 匿名用户数据加密存储
- 审核员只能看到待审核内容
- 管理员操作全程记录

### 会话管理
- 不同角色使用不同的会话策略
- 审核员和管理员会话超时更短
- 支持强制下线功能
