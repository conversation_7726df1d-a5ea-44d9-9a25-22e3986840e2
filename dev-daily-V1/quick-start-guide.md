# 项目快速启动指南 - 2025-07-27

## 🚀 快速启动

### 1. 启动开发服务器
```bash
cd frontend
pnpm dev
```

### 2. 访问应用
- **主页**: http://localhost:5180
- **权限测试**: http://localhost:5180/test/permissions

## 🔑 测试账号

### 登录入口
- **普通用户登录**: `/login`
- **审核员登录**: `/reviewer/login`
- **管理员登录**: `/admin/login`

### 测试流程
1. **匿名用户体验**: 主页点击"快速参与问卷"
2. **权限系统测试**: 访问 `/test/permissions` 执行测试
3. **角色功能验证**: 分别测试各角色登录和功能

## 📋 核心功能导航

### 普通用户功能
- 🏠 **首页**: `/` - 平台介绍和快捷注册
- 📊 **数据可视化**: `/analytics` - 就业数据统计
- 📝 **问卷填写**: `/questionnaire` - 就业问卷调查
- 📖 **故事墙**: `/stories` - 就业故事分享
- 💬 **问卷心声**: `/voices` - 心声表达

### 审核员功能
- 🏢 **工作台**: `/reviewer/dashboard` - 审核概览
- 📋 **问卷审核**: `/reviewer/questionnaires` - 问卷内容审核
- 📚 **故事审核**: `/reviewer/stories` - 故事内容审核
- 💭 **心声审核**: `/reviewer/voices` - 心声内容审核
- 📈 **审核历史**: `/reviewer/history` - 历史记录查看

### 管理员功能
- 🎛️ **控制台**: `/admin` - 系统管理概览
- 👥 **用户管理**: `/admin/users` - 用户账号管理
- 👨‍💼 **审核员管理**: `/admin/reviewers` - 审核员配置
- 📄 **内容管理**: `/admin/content` - 内容批量管理
- ⚙️ **系统设置**: `/admin/system` - 系统参数配置
- 🔌 **API管理**: `/admin/api-data` - 接口和数据管理
- 📋 **系统日志**: `/admin/logs` - 操作日志查看

## 🎨 界面主题

### 角色主题区分
- **普通用户**: 白色主题 - 简洁友好
- **审核员**: 蓝色主题 (#1890ff) - 专业工作台
- **管理员**: 橙色主题 (#fa8c16) - 权威控制台

### 响应式适配
- **桌面端**: 1200px+ 多列布局
- **平板端**: 768px-1200px 灵活布局  
- **手机端**: 768px以下单列布局

## 🔒 权限系统

### 4层用户角色
1. **普通浏览用户** (guest) - 浏览公开内容
2. **注册用户** (user) - 填写问卷，发布内容
3. **审核员** (reviewer) - 内容审核，质量控制
4. **管理员** (admin) - 系统管理，全局控制

### 权限验证
- **路由级**: 未授权访问自动重定向
- **组件级**: 无权限组件自动隐藏
- **数据级**: 用户只能访问自己的数据

## 🧪 测试功能

### 权限测试工具
- **地址**: `/test/permissions`
- **功能**: 自动化权限验证
- **测试项**: 路由权限、功能权限、组件权限

### 测试步骤
1. 访问权限测试页面
2. 点击"执行权限测试"
3. 查看测试结果统计
4. 验证权限组件展示

## 📱 移动端体验

### 手机访问
- 在手机浏览器中访问 http://localhost:5180
- 测试触摸操作和滑动效果
- 验证响应式布局适配

### 平板访问
- 在平板设备上测试中等屏幕适配
- 验证导航菜单和布局调整

## 🔧 开发工具

### 浏览器开发者工具
- **F12** 打开开发者工具
- **Network** 查看网络请求
- **Console** 查看控制台日志
- **Elements** 检查页面元素

### 响应式测试
- **Ctrl+Shift+M** 切换设备模拟
- 测试不同屏幕尺寸效果
- 验证触摸操作体验

## 🎯 验收重点

### 必测功能
1. ✅ 权限系统自动化测试
2. ✅ 匿名用户快捷注册
3. ✅ 各角色功能完整性
4. ✅ 界面主题切换效果
5. ✅ 响应式设计适配

### 性能检查
- 页面加载速度 < 3秒
- 路由切换流畅无卡顿
- 内存使用合理

### 用户体验
- 操作流程简单直观
- 错误提示友好明确
- 界面美观专业

## 🚨 常见问题

### 启动问题
**Q**: 项目启动失败？
**A**: 检查 Node.js 版本，运行 `pnpm install` 重新安装依赖

**Q**: 端口被占用？
**A**: 修改 vite.config.ts 中的端口配置，或关闭占用端口的程序

### 功能问题
**Q**: 权限测试失败？
**A**: 检查用户登录状态，确保权限配置正确

**Q**: 页面显示异常？
**A**: 清除浏览器缓存，刷新页面重试

## 📞 技术支持

### 开发团队联系方式
- **项目负责人**: [联系方式]
- **技术支持**: [联系方式]
- **问题反馈**: [联系方式]

### 文档资源
- **项目文档**: `docs/` 目录
- **API文档**: `docs/api/` 目录
- **更新日志**: `dev-daily-V1/` 目录

---

**最后更新**: 2025年7月27日
**项目版本**: v1.0-权限系统完整版
**状态**: ✅ 准备验收
