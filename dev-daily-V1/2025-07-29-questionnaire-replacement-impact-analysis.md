# 问卷系统替换影响分析与适配方案

**日期**: 2025-07-29  
**目标**: 分析替换问卷系统对其他功能的影响并提供完整适配方案  
**项目状态**: 开发调试阶段，可快速替换，无关键数据需保留  

## 🎯 影响范围分析

### 📊 数据结构影响

#### 当前数据结构
```typescript
// 当前固定结构
interface QuestionnaireFormData {
  personalInfo: PersonalInfo;      // 个人信息
  educationInfo: EducationInfo;    // 教育背景
  employmentInfo: EmploymentInfo;  // 就业意向
  jobSearchInfo: JobSearchInfo;    // 求职过程
  employmentStatus: EmploymentStatus; // 就业状态
}
```

#### 新数据结构
```typescript
// 通用灵活结构
interface QuestionnaireResponse {
  questionnaireId: string;
  sectionResponses: SectionResponse[];
  metadata: ResponseMetadata;
}
```

### 🔗 影响的系统组件

#### 1. **前端页面组件** (7个主要影响)
| 组件 | 路径 | 影响程度 | 修改类型 |
|------|------|----------|----------|
| QuestionnaireForm | `frontend/src/components/forms/` | 🔴 完全替换 | 核心组件重写 |
| DashboardPage | `frontend/src/pages/admin/` | 🟡 适配修改 | 数据展示适配 |
| QuestionnaireReviewPage | `frontend/src/pages/reviewer/` | 🟡 适配修改 | 审核界面适配 |
| AnalyticsPage | `frontend/src/pages/analytics/` | 🟡 适配修改 | 统计图表适配 |
| ContentManagementPage | `frontend/src/pages/admin/` | 🟡 适配修改 | 内容管理适配 |
| ApiDataPage | `frontend/src/pages/admin/` | 🟢 轻微修改 | API文档更新 |
| QuestionnaireLayout | `frontend/src/components/layout/` | 🟢 保持不变 | 无需修改 |

#### 2. **后端API接口** (5个主要影响)
| 接口 | 路径 | 影响程度 | 修改类型 |
|------|------|----------|----------|
| POST /questionnaire | `backend/src/routes/questionnaire.ts` | 🔴 重构 | 数据结构适配 |
| GET /questionnaire | `backend/src/routes/questionnaire.ts` | 🟡 适配 | 响应格式调整 |
| GET /questionnaire/:id | `backend/src/routes/questionnaire.ts` | 🟡 适配 | 详情格式调整 |
| UUID问卷接口 | `backend/src/routes/uuid.ts` | 🟡 适配 | 数据映射调整 |
| 统计分析接口 | `backend/src/routes/analytics.ts` | 🟡 适配 | 统计逻辑调整 |

#### 3. **数据库表结构** (3个主要影响)
| 表名 | 影响程度 | 修改类型 | 说明 |
|------|----------|----------|------|
| questionnaire_responses | 🔴 重构 | 表结构重设计 | 从固定字段改为灵活结构 |
| user_content_mappings | 🟢 保持 | 无需修改 | UUID映射保持不变 |
| analytics_summary | 🟡 适配 | 统计逻辑调整 | 统计算法需要适配 |

#### 4. **服务层组件** (4个主要影响)
| 服务 | 路径 | 影响程度 | 修改类型 |
|------|------|----------|----------|
| QuestionnaireService | `frontend/src/services/questionnaire.ts` | 🟡 适配 | API调用适配 |
| QuestionnaireStore | `frontend/src/stores/questionnaireStore.ts` | 🟡 适配 | 状态管理适配 |
| UuidQuestionnaireService | `frontend/src/services/uuidQuestionnaireService.ts` | 🟡 适配 | 数据转换适配 |
| AnalyticsService | `frontend/src/services/analytics.ts` | 🟡 适配 | 统计数据适配 |

## 🛠️ 完整适配修改方案

### Phase 1: 数据库结构重设计 (1天)

#### 1.1 新表结构设计
```sql
-- 新的问卷响应表
CREATE TABLE questionnaire_responses_v2 (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    questionnaire_id TEXT NOT NULL,
    user_id TEXT,
    response_data TEXT NOT NULL, -- JSON格式存储完整响应
    metadata TEXT NOT NULL,      -- JSON格式存储元数据
    status TEXT NOT NULL DEFAULT 'pending',
    created_at TEXT NOT NULL DEFAULT (datetime('now')),
    updated_at TEXT NOT NULL DEFAULT (datetime('now'))
);

-- 问卷配置表
CREATE TABLE questionnaire_configs (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    config_data TEXT NOT NULL, -- JSON格式存储问卷配置
    status TEXT NOT NULL DEFAULT 'active',
    created_at TEXT NOT NULL DEFAULT (datetime('now')),
    updated_at TEXT NOT NULL DEFAULT (datetime('now'))
);

-- 问卷统计表
CREATE TABLE questionnaire_statistics (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    questionnaire_id TEXT NOT NULL,
    question_id TEXT NOT NULL,
    statistics_data TEXT NOT NULL, -- JSON格式存储统计数据
    last_updated TEXT NOT NULL DEFAULT (datetime('now')),
    FOREIGN KEY (questionnaire_id) REFERENCES questionnaire_configs(id)
);
```

#### 1.2 数据迁移脚本
```sql
-- 迁移现有数据到新结构
INSERT INTO questionnaire_responses_v2 (id, questionnaire_id, user_id, response_data, metadata, status, created_at, updated_at)
SELECT 
    id,
    'employment-survey-v1' as questionnaire_id,
    user_id,
    json_object(
        'sections', json_array(
            json_object('sectionId', 'personal', 'responses', personal_info),
            json_object('sectionId', 'education', 'responses', education_info),
            json_object('sectionId', 'employment', 'responses', employment_info),
            json_object('sectionId', 'jobSearch', 'responses', job_search_info),
            json_object('sectionId', 'status', 'responses', employment_status)
        )
    ) as response_data,
    json_object(
        'responseId', id,
        'isAnonymous', CASE WHEN user_id IS NULL THEN 1 ELSE 0 END,
        'submittedAt', created_at
    ) as metadata,
    status,
    created_at,
    updated_at
FROM questionnaire_responses;
```

### Phase 2: 后端API适配 (1天)

#### 2.1 问卷提交接口重构
```typescript
// backend/src/routes/questionnaire.ts
questionnaire.post('/', optionalAuthMiddleware, async (c) => {
  try {
    const body = await c.req.json() as QuestionnaireSubmissionRequest;
    const { questionnaireId, sectionResponses, metadata } = body;

    // 验证问卷配置
    const config = await getQuestionnaireConfig(questionnaireId);
    if (!config) {
      return c.json({ success: false, message: '问卷配置不存在' }, 400);
    }

    // 验证响应数据
    const validationResult = await validateQuestionnaireResponse(
      sectionResponses, 
      config
    );
    if (!validationResult.valid) {
      return c.json({ 
        success: false, 
        message: '数据验证失败',
        errors: validationResult.errors 
      }, 400);
    }

    // 保存响应数据
    const responseData = {
      questionnaireId,
      sectionResponses,
      metadata: {
        ...metadata,
        responseId: generateId(),
        submittedAt: new Date().toISOString()
      }
    };

    const result = await db.execute(
      `INSERT INTO questionnaire_responses_v2 
       (questionnaire_id, user_id, response_data, metadata, status) 
       VALUES (?, ?, ?, ?, ?)`,
      [
        questionnaireId,
        user?.id || null,
        JSON.stringify(responseData),
        JSON.stringify(responseData.metadata),
        'pending'
      ]
    );

    return c.json({
      success: true,
      data: { responseId: responseData.metadata.responseId },
      message: '问卷提交成功'
    });
  } catch (error) {
    return c.json({ success: false, message: '提交失败' }, 500);
  }
});
```

#### 2.2 问卷查询接口适配
```typescript
// 获取问卷列表适配
questionnaire.get('/', authMiddleware, async (c) => {
  try {
    const questionnaires = await db.query<any>(
      'SELECT * FROM questionnaire_responses_v2 WHERE 1=1',
      []
    );

    const formattedData = questionnaires.map(q => ({
      id: q.id,
      questionnaireId: q.questionnaire_id,
      userId: q.user_id,
      responseData: JSON.parse(q.response_data),
      metadata: JSON.parse(q.metadata),
      status: q.status,
      createdAt: q.created_at,
      updatedAt: q.updated_at
    }));

    return c.json({
      success: true,
      data: formattedData,
      message: '获取问卷列表成功'
    });
  } catch (error) {
    return c.json({ success: false, message: '获取失败' }, 500);
  }
});
```

### Phase 3: 前端组件替换 (1.5天)

#### 3.1 问卷配置文件创建
```json
// frontend/src/config/employment-survey-config.json
{
  "id": "employment-survey-v1",
  "title": "大学生就业调研问卷",
  "description": "了解大学生就业现状和期望",
  "sections": [
    {
      "id": "personal",
      "title": "个人信息",
      "questions": [
        {
          "id": "name",
          "type": "text",
          "title": "姓名",
          "required": true,
          "validation": [
            { "type": "required", "message": "请输入姓名" },
            { "type": "minLength", "value": 2, "message": "姓名至少2个字符" }
          ]
        },
        {
          "id": "gender",
          "type": "radio",
          "title": "性别",
          "required": true,
          "options": [
            { "value": "male", "label": "男" },
            { "value": "female", "label": "女" },
            { "value": "other", "label": "其他" }
          ]
        }
        // ... 更多问题配置
      ]
    }
    // ... 更多节配置
  ],
  "config": {
    "allowAnonymous": true,
    "showStatistics": true,
    "autoSave": true,
    "theme": "employment-survey"
  }
}
```

#### 3.2 问卷组件替换
```typescript
// frontend/src/pages/public/QuestionnairePage.tsx
import { QuestionnaireEngine } from '../../../questionnaire-system/components/QuestionnaireEngine';
import employmentSurveyConfig from '../../config/employment-survey-config.json';

export const QuestionnairePage: React.FC = () => {
  const handleSubmit = async (data: QuestionnaireResponse) => {
    try {
      const result = await QuestionnaireService.submitQuestionnaire(data);
      message.success('问卷提交成功！');
      navigate('/success');
      return result;
    } catch (error) {
      message.error('提交失败，请重试');
      throw error;
    }
  };

  return (
    <div className={styles.container}>
      <QuestionnaireEngine
        questionnaire={employmentSurveyConfig}
        onSubmit={handleSubmit}
        onProgress={(progress) => console.log('进度:', progress)}
        config={{
          showStatistics: true,
          autoSave: true,
          theme: 'employment-survey'
        }}
      />
    </div>
  );
};
```

### Phase 4: 管理页面适配 (1天)

#### 4.1 管理员仪表盘适配
```typescript
// frontend/src/pages/admin/DashboardPage.tsx
export const DashboardPage: React.FC = () => {
  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    approved: 0,
    rejected: 0
  });

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await QuestionnaireService.getQuestionnaires({
          page: 1,
          pageSize: 1000 // 获取所有数据用于统计
        });
        
        const newStats = response.data.reduce((acc, q) => {
          acc.total++;
          acc[q.status]++;
          return acc;
        }, { total: 0, pending: 0, approved: 0, rejected: 0 });
        
        setStats(newStats);
      } catch (error) {
        console.error('获取统计数据失败:', error);
      }
    };

    fetchStats();
  }, []);

  // 表格列配置适配
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 100
    },
    {
      title: '问卷类型',
      dataIndex: 'questionnaireId',
      key: 'questionnaireId',
      render: (id: string) => {
        const typeMap = {
          'employment-survey-v1': '就业调研'
        };
        return typeMap[id] || id;
      }
    },
    {
      title: '提交者',
      key: 'submitter',
      render: (_, record) => {
        const metadata = record.metadata;
        return metadata.isAnonymous ? '匿名用户' : record.userId;
      }
    },
    {
      title: '提交时间',
      key: 'submittedAt',
      render: (_, record) => {
        return new Date(record.metadata.submittedAt).toLocaleString();
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => getStatusTag(status)
    }
  ];

  return (
    <AdminLayout>
      {/* 统计卡片保持不变 */}
      <Row gutter={[16, 16]} className={styles.statsRow}>
        {/* ... 统计卡片 */}
      </Row>

      {/* 问卷列表适配新数据结构 */}
      <Card title="问卷管理">
        <Table
          columns={columns}
          dataSource={questionnaires}
          rowKey="id"
          loading={isLoading}
        />
      </Card>
    </AdminLayout>
  );
};
```

#### 4.2 审核页面适配
```typescript
// frontend/src/pages/reviewer/QuestionnaireReviewPage.tsx
const handleReview = (record: any) => {
  setSelectedSubmission({
    id: record.id,
    questionnaireId: record.questionnaireId,
    responseData: record.responseData,
    metadata: record.metadata,
    status: record.status
  });
  setReviewModalVisible(true);
};

// 审核详情渲染适配
const renderQuestionnaireDetails = (responseData: any) => {
  return responseData.sectionResponses.map((section: any) => (
    <Card key={section.sectionId} title={section.title} style={{ marginBottom: 16 }}>
      <Descriptions column={2} size="small">
        {section.questionResponses.map((response: any) => (
          <Descriptions.Item 
            key={response.questionId} 
            label={response.questionTitle}
          >
            {formatResponseValue(response.value, response.questionType)}
          </Descriptions.Item>
        ))}
      </Descriptions>
    </Card>
  ));
};
```

### Phase 5: 分析页面适配 (0.5天)

#### 5.1 统计数据适配
```typescript
// frontend/src/pages/analytics/AnalyticsPage.tsx
const processAnalyticsData = (questionnaires: any[]) => {
  const processedData = {
    educationDistribution: [],
    salaryExpectation: [],
    employmentStatus: [],
    regionDistribution: [],
    ageDistribution: []
  };

  questionnaires.forEach(q => {
    const responses = q.responseData.sectionResponses;
    
    // 提取教育背景数据
    const educationSection = responses.find(s => s.sectionId === 'education');
    if (educationSection) {
      const degreeResponse = educationSection.questionResponses.find(r => r.questionId === 'degree');
      if (degreeResponse) {
        // 处理学历分布数据
        processedData.educationDistribution.push(degreeResponse.value);
      }
    }

    // 提取就业状态数据
    const statusSection = responses.find(s => s.sectionId === 'status');
    if (statusSection) {
      const statusResponse = statusSection.questionResponses.find(r => r.questionId === 'currentStatus');
      if (statusResponse) {
        // 处理就业状态数据
        processedData.employmentStatus.push(statusResponse.value);
      }
    }

    // ... 处理其他数据
  });

  return processedData;
};
```

### Phase 6: 服务层适配 (0.5天)

#### 6.1 QuestionnaireService适配
```typescript
// frontend/src/services/questionnaire.ts
export class QuestionnaireService {
  // 提交问卷适配新格式
  static async submitQuestionnaire(data: QuestionnaireResponse): Promise<any> {
    return ApiService.post<any>('/questionnaire', data);
  }

  // 获取问卷列表适配
  static async getQuestionnaires(params: any): Promise<any> {
    const response = await ApiService.get<any>('/questionnaire', { params });
    
    // 数据格式转换适配
    const formattedData = response.data.map((item: any) => ({
      id: item.id,
      questionnaireId: item.questionnaire_id,
      userId: item.user_id,
      responseData: JSON.parse(item.response_data),
      metadata: JSON.parse(item.metadata),
      status: item.status,
      createdAt: item.created_at,
      updatedAt: item.updated_at
    }));

    return {
      ...response,
      data: formattedData
    };
  }

  // 数据格式转换工具
  static convertLegacyData(legacyData: QuestionnaireFormData): QuestionnaireResponse {
    return {
      questionnaireId: 'employment-survey-v1',
      sectionResponses: [
        {
          sectionId: 'personal',
          questionResponses: Object.entries(legacyData.personalInfo).map(([key, value]) => ({
            questionId: key,
            value: value,
            answeredAt: Date.now()
          }))
        },
        // ... 转换其他节
      ],
      metadata: {
        responseId: generateId(),
        isAnonymous: true,
        startedAt: Date.now(),
        completedAt: Date.now()
      }
    };
  }
}
```

## 📋 实施时间表

### 🗓️ 详细计划 (总计4天)

| 阶段 | 时间 | 任务 | 负责人 | 验收标准 |
|------|------|------|--------|----------|
| **Day 1** | 上午 | 数据库结构设计和迁移 | 后端 | 新表创建成功，数据迁移完成 |
| **Day 1** | 下午 | 后端API接口重构 | 后端 | 新API接口测试通过 |
| **Day 2** | 上午 | 问卷配置文件创建 | 前端 | 配置文件验证通过 |
| **Day 2** | 下午 | 问卷组件替换 | 前端 | 新问卷组件功能正常 |
| **Day 3** | 上午 | 管理页面适配 | 前端 | 管理功能正常工作 |
| **Day 3** | 下午 | 审核页面适配 | 前端 | 审核流程正常 |
| **Day 4** | 上午 | 分析页面适配 | 前端 | 统计图表正常显示 |
| **Day 4** | 下午 | 全面测试和优化 | 全员 | 所有功能测试通过 |

## ✅ 验收标准

### 🎯 功能验收
- [ ] 问卷填写流程完整可用
- [ ] 管理员审核功能正常
- [ ] 数据统计分析正确
- [ ] 所有API接口响应正常
- [ ] 数据库操作无错误

### 🔧 技术验收
- [ ] 新旧数据格式兼容
- [ ] 性能无明显下降
- [ ] 错误处理完善
- [ ] 代码质量符合标准
- [ ] 文档更新完整

### 🎨 用户体验验收
- [ ] 界面交互流畅
- [ ] 响应式设计正常
- [ ] 错误提示友好
- [ ] 加载速度合理
- [ ] 功能易于使用

---

**总结**: 替换影响范围明确，适配方案完整可行。由于项目处于开发调试阶段，可以快速进行功能替换，预计4天内完成所有适配工作。
