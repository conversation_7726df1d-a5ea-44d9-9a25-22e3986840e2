name: Deploy to Cloudflare

on:
  push:
    branches:
      - main
      - develop
  pull_request:
    branches:
      - main

jobs:
  # 前端部署到 Cloudflare Pages
  deploy-frontend:
    runs-on: ubuntu-latest
    name: Deploy Frontend to Cloudflare Pages
    
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json
          
      - name: Install dependencies
        working-directory: ./frontend
        run: npm ci
        
      - name: Run tests
        working-directory: ./frontend
        run: npm run test:ci || true
        
      - name: Build application
        working-directory: ./frontend
        run: npm run build
        env:
          VITE_API_BASE_URL: ${{ secrets.VITE_API_BASE_URL }}
          VITE_APP_ENV: production
          
      - name: Deploy to Cloudflare Pages
        uses: cloudflare/pages-action@v1
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          projectName: college-employment-survey
          directory: frontend/dist
          gitHubToken: ${{ secrets.GITHUB_TOKEN }}
          
  # 后端部署到 Cloudflare Workers
  deploy-backend:
    runs-on: ubuntu-latest
    name: Deploy Backend to Cloudflare Workers
    
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: backend/package-lock.json
          
      - name: Install dependencies
        working-directory: ./backend
        run: npm ci
        
      - name: Run tests
        working-directory: ./backend
        run: npm run test || true
        
      - name: Deploy to Cloudflare Workers
        working-directory: ./backend
        run: npx wrangler deploy
        env:
          CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          CLOUDFLARE_ACCOUNT_ID: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          
  # 运行端到端测试
  e2e-tests:
    runs-on: ubuntu-latest
    name: End-to-End Tests
    needs: [deploy-frontend, deploy-backend]
    if: github.ref == 'refs/heads/main'
    
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          
      - name: Install Playwright
        run: |
          npm install -g @playwright/test
          npx playwright install
          
      - name: Run E2E tests
        run: npx playwright test
        env:
          BASE_URL: ${{ secrets.PRODUCTION_URL }}
          
      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: playwright-report
          path: playwright-report/
          retention-days: 30
