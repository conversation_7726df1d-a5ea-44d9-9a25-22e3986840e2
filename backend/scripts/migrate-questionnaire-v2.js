#!/usr/bin/env node

/**
 * 问卷系统V2迁移脚本
 * 将现有问卷系统升级为通用问卷系统
 */

import { execSync } from 'child_process';
import { readFileSync, existsSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function execCommand(command, options = {}) {
  try {
    log(`执行命令: ${command}`, 'cyan');
    const result = execSync(command, { 
      cwd: projectRoot,
      encoding: 'utf8',
      stdio: 'inherit',
      ...options 
    });
    return result;
  } catch (error) {
    log(`命令执行失败: ${error.message}`, 'red');
    throw error;
  }
}

async function backupCurrentData() {
  log('📦 备份当前数据...', 'blue');
  
  try {
    // 导出当前问卷数据
    const backupCommand = `wrangler d1 execute employment-survey-db --command="SELECT * FROM questionnaire_responses;" --local --output=json > backup_questionnaire_$(date +%Y%m%d_%H%M%S).json`;
    execCommand(backupCommand);
    log('✅ 数据备份完成', 'green');
  } catch (error) {
    log(`⚠️  备份失败，但继续执行迁移: ${error.message}`, 'yellow');
  }
}

async function runV2Migrations() {
  log('🔄 执行问卷系统V2迁移...', 'blue');
  
  const migrationsDir = join(projectRoot, 'migrations');
  const migrationFiles = [
    '0002_questionnaire_v2.sql',
    '0003_migrate_questionnaire_data.sql'
  ];
  
  for (const file of migrationFiles) {
    const filePath = join(migrationsDir, file);
    if (existsSync(filePath)) {
      try {
        log(`执行迁移: ${file}`, 'cyan');
        execCommand(`wrangler d1 execute employment-survey-db --file=${filePath} --local`);
        log(`✅ ${file} 执行成功`, 'green');
      } catch (error) {
        log(`❌ ${file} 执行失败: ${error.message}`, 'red');
        throw error;
      }
    } else {
      log(`❌ 迁移文件不存在: ${file}`, 'red');
      throw new Error(`Migration file not found: ${file}`);
    }
  }
}

async function verifyMigration() {
  log('🔍 验证迁移结果...', 'blue');
  
  try {
    // 检查新表是否创建成功
    log('检查新表结构...', 'cyan');
    execCommand('wrangler d1 execute employment-survey-db --command="SELECT name FROM sqlite_master WHERE type=\'table\' AND name LIKE \'questionnaire_%\';" --local');
    
    // 检查问卷配置是否插入成功
    log('检查问卷配置...', 'cyan');
    execCommand('wrangler d1 execute employment-survey-db --command="SELECT id, title FROM questionnaire_configs;" --local');
    
    // 检查数据迁移结果
    log('检查数据迁移结果...', 'cyan');
    const oldCountResult = execCommand('wrangler d1 execute employment-survey-db --command="SELECT COUNT(*) as old_count FROM questionnaire_responses;" --local --output=json', { stdio: 'pipe' });
    const newCountResult = execCommand('wrangler d1 execute employment-survey-db --command="SELECT COUNT(*) as new_count FROM questionnaire_responses_v2;" --local --output=json', { stdio: 'pipe' });
    
    log('✅ 迁移验证成功', 'green');
    log('数据迁移统计:', 'blue');
    log(`原表记录数: ${oldCountResult}`, 'cyan');
    log(`新表记录数: ${newCountResult}`, 'cyan');
    
  } catch (error) {
    log(`❌ 迁移验证失败: ${error.message}`, 'red');
    throw error;
  }
}

async function updateApiRoutes() {
  log('📝 准备API路由更新说明...', 'blue');
  
  const updateInstructions = `
📋 API路由更新说明:

需要手动更新以下文件:
1. backend/src/routes/questionnaire.ts - 更新API接口逻辑
2. frontend/src/services/questionnaire.ts - 更新前端服务调用
3. frontend/src/types/questionnaire.ts - 更新类型定义

主要变更:
- 问卷提交数据结构从固定字段改为灵活的sectionResponses结构
- 响应数据包含完整的问题和答案信息
- 支持实时统计和条件逻辑功能

下一步执行:
1. 运行 'node scripts/update-api-routes.js' 更新API路由
2. 运行 'node scripts/update-frontend-types.js' 更新前端类型
3. 重启开发服务器测试新功能
`;

  log(updateInstructions, 'yellow');
}

async function main() {
  log('🚀 开始问卷系统V2迁移...', 'magenta');
  log('', 'reset');
  log('⚠️  注意: 此操作将修改数据库结构，请确保已备份重要数据', 'yellow');
  log('', 'reset');
  
  try {
    await backupCurrentData();
    await runV2Migrations();
    await verifyMigration();
    await updateApiRoutes();
    
    log('', 'reset');
    log('🎉 问卷系统V2迁移完成！', 'green');
    log('', 'reset');
    log('✅ 完成的工作:', 'blue');
    log('  - 创建了新的通用问卷表结构', 'green');
    log('  - 迁移了现有问卷数据到新格式', 'green');
    log('  - 创建了问卷配置和统计表', 'green');
    log('  - 初始化了就业调研问卷配置', 'green');
    log('', 'reset');
    log('🔄 下一步:', 'blue');
    log('  1. 更新API路由和前端组件', 'cyan');
    log('  2. 集成通用问卷引擎', 'cyan');
    log('  3. 测试新功能', 'cyan');
    
  } catch (error) {
    log('❌ 迁移失败', 'red');
    log(`错误详情: ${error.message}`, 'red');
    log('', 'reset');
    log('🔧 故障排除:', 'yellow');
    log('  1. 检查数据库连接', 'cyan');
    log('  2. 确认迁移文件存在', 'cyan');
    log('  3. 查看详细错误日志', 'cyan');
    process.exit(1);
  }
}

// 检查命令行参数
const args = process.argv.slice(2);
if (args.includes('--help') || args.includes('-h')) {
  log('问卷系统V2迁移脚本', 'blue');
  log('', 'reset');
  log('用法:', 'cyan');
  log('  node scripts/migrate-questionnaire-v2.js', 'cyan');
  log('', 'reset');
  log('功能:', 'cyan');
  log('  - 备份现有问卷数据', 'cyan');
  log('  - 创建新的通用问卷表结构', 'cyan');
  log('  - 迁移数据到新格式', 'cyan');
  log('  - 验证迁移结果', 'cyan');
  log('', 'reset');
  log('选项:', 'cyan');
  log('  --help, -h    显示帮助信息', 'cyan');
  process.exit(0);
}

main().catch(console.error);
