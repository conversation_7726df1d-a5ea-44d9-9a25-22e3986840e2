{"name": "backend", "version": "1.0.0", "description": "大学生就业问卷调查平台 V1 - 后端API服务", "main": "src/index.ts", "type": "module", "scripts": {"dev": "wrangler dev", "dev:local": "tsx src/index.ts", "build": "tsc", "deploy": "wrangler deploy", "test": "echo \"No tests specified\" && exit 0", "lint": "echo \"No linting configured\" && exit 0", "lint:fix": "echo \"No linting configured\" && exit 0", "type-check": "tsc --noEmit", "clean": "rm -rf dist .wrangler", "db:create": "wrangler d1 create employment-survey-db", "db:migrate": "wrangler d1 migrations apply employment-survey-db", "db:seed": "echo \"Database seeding not implemented\" && exit 0"}, "keywords": ["employment-survey", "questionnaire", "cloudflare-workers", "hono", "typescript"], "author": "V1 Development Team", "license": "MIT", "packageManager": "pnpm@10.8.0", "dependencies": {"@hono/node-server": "^1.12.2", "hono": "^4.5.8"}, "devDependencies": {"@cloudflare/workers-types": "^4.20240909.0", "@types/node": "^22.5.4", "tsx": "^4.19.0", "typescript": "^5.5.4", "wrangler": "^3.78.2"}}