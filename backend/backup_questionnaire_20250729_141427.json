
wrangler d1 execute <database>

Execute a command or SQL file

POSITIONALS
  database  The name or binding of the DB  [string] [required]

GLOBAL FLAGS
  -c, --config   Path to Wrangler configuration file  [string]
      --cwd      Run as if Wrangler was started in the specified directory instead of the current working directory  [string]
  -e, --env      Environment to use for operations, and for selecting .env and .dev.vars files  [string]
  -h, --help     Show help  [boolean]
  -v, --version  Show version number  [boolean]

OPTIONS
  -y, --yes         Answer "yes" to any prompts  [boolean]
      --local       Execute commands/files against a local DB for use with wrangler dev  [boolean]
      --remote      Execute commands/files against a remote DB for use with wrangler dev  [boolean]
      --file        A .sql file to ingest  [string]
      --command     A single SQL statement to execute  [string]
      --persist-to  Specify directory to use for local persistence (for --local)  [string]
      --json        Return output as clean JSON  [boolean] [default: false]
      --preview     Execute commands/files against a preview D1 DB  [boolean] [default: false]
