-- Migration: 005_create_content_tables
-- Description: 创建内容管理表 (故事墙、问卷心声)
-- Created: 2024-01-27

-- 故事墙表
CREATE TABLE IF NOT EXISTS stories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID,
    
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    category ENUM('job_search', 'career_change', 'success', 'challenge', 'advice') DEFAULT 'job_search',
    tags JSON,
    
    -- 互动数据
    likes_count INT DEFAULT 0,
    views_count INT DEFAULT 0,
    
    -- 状态管理
    status ENUM('draft', 'published', 'hidden', 'deleted') DEFAULT 'draft',
    is_anonymous BOOLEAN DEFAULT true,
    
    -- 审核
    moderation_status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    moderator_id UUID,
    moderation_notes TEXT,
    moderated_at TIMESTAMP,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    published_at TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (moderator_id) REFERENCES users(id) ON DELETE SET NULL
);

-- 问卷心声表
CREATE TABLE IF NOT EXISTS voices (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID,
    
    content TEXT NOT NULL,
    mood ENUM('positive', 'neutral', 'negative') DEFAULT 'neutral',
    
    -- 互动数据
    likes_count INT DEFAULT 0,
    
    -- 状态管理
    status ENUM('published', 'hidden', 'deleted') DEFAULT 'published',
    is_anonymous BOOLEAN DEFAULT true,
    
    -- 审核
    moderation_status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    moderator_id UUID,
    moderated_at TIMESTAMP,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (moderator_id) REFERENCES users(id) ON DELETE SET NULL
);

-- 创建索引
-- 故事墙索引
CREATE INDEX idx_stories_user_id ON stories(user_id);
CREATE INDEX idx_stories_category ON stories(category);
CREATE INDEX idx_stories_status ON stories(status);
CREATE INDEX idx_stories_moderation_status ON stories(moderation_status);
CREATE INDEX idx_stories_published_at ON stories(published_at);
CREATE INDEX idx_stories_likes_count ON stories(likes_count DESC);
CREATE INDEX idx_stories_created_at ON stories(created_at DESC);

-- 问卷心声索引
CREATE INDEX idx_voices_user_id ON voices(user_id);
CREATE INDEX idx_voices_mood ON voices(mood);
CREATE INDEX idx_voices_status ON voices(status);
CREATE INDEX idx_voices_moderation_status ON voices(moderation_status);
CREATE INDEX idx_voices_created_at ON voices(created_at DESC);
CREATE INDEX idx_voices_likes_count ON voices(likes_count DESC);

-- 全文搜索索引
CREATE FULLTEXT INDEX idx_stories_content ON stories(title, content);
CREATE FULLTEXT INDEX idx_voices_content ON voices(content);
