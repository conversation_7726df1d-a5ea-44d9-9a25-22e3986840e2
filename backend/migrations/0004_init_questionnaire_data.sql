-- Migration: 0004_init_questionnaire_data
-- Description: 初始化问卷系统基础数据
-- Created: 2025-07-29

-- 插入示例问卷响应数据 (用于测试)
INSERT OR IGNORE INTO questionnaire_responses_v2 (
    id,
    questionnaire_id,
    user_id,
    response_data,
    metadata,
    status,
    created_at,
    updated_at
) VALUES (
    'sample-response-001',
    'employment-survey-v1',
    NULL, -- 匿名提交
    json('{
        "questionnaireId": "employment-survey-v1",
        "sectionResponses": [
            {
                "sectionId": "personal",
                "sectionTitle": "个人信息",
                "questionResponses": [
                    {
                        "questionId": "name",
                        "questionTitle": "姓名",
                        "questionType": "text",
                        "value": "张三",
                        "answeredAt": "2025-07-29T06:00:00.000Z"
                    },
                    {
                        "questionId": "gender",
                        "questionTitle": "性别",
                        "questionType": "radio",
                        "value": "male",
                        "answeredAt": "2025-07-29T06:00:00.000Z"
                    },
                    {
                        "questionId": "age",
                        "questionTitle": "年龄",
                        "questionType": "number",
                        "value": 23,
                        "answeredAt": "2025-07-29T06:00:00.000Z"
                    },
                    {
                        "questionId": "phone",
                        "questionTitle": "联系电话",
                        "questionType": "text",
                        "value": "13800138000",
                        "answeredAt": "2025-07-29T06:00:00.000Z"
                    },
                    {
                        "questionId": "email",
                        "questionTitle": "邮箱地址",
                        "questionType": "email",
                        "value": "<EMAIL>",
                        "answeredAt": "2025-07-29T06:00:00.000Z"
                    }
                ],
                "completedAt": "2025-07-29T06:00:00.000Z"
            },
            {
                "sectionId": "education",
                "sectionTitle": "教育背景",
                "questionResponses": [
                    {
                        "questionId": "degree",
                        "questionTitle": "学历层次",
                        "questionType": "select",
                        "value": "bachelor",
                        "answeredAt": "2025-07-29T06:01:00.000Z"
                    },
                    {
                        "questionId": "major",
                        "questionTitle": "专业",
                        "questionType": "text",
                        "value": "计算机科学与技术",
                        "answeredAt": "2025-07-29T06:01:00.000Z"
                    },
                    {
                        "questionId": "university",
                        "questionTitle": "毕业院校",
                        "questionType": "text",
                        "value": "北京大学",
                        "answeredAt": "2025-07-29T06:01:00.000Z"
                    },
                    {
                        "questionId": "graduationYear",
                        "questionTitle": "毕业年份",
                        "questionType": "number",
                        "value": 2024,
                        "answeredAt": "2025-07-29T06:01:00.000Z"
                    }
                ],
                "completedAt": "2025-07-29T06:01:00.000Z"
            },
            {
                "sectionId": "employment",
                "sectionTitle": "就业意向",
                "questionResponses": [
                    {
                        "questionId": "preferredIndustry",
                        "questionTitle": "期望行业",
                        "questionType": "checkbox",
                        "value": ["tech", "finance"],
                        "answeredAt": "2025-07-29T06:02:00.000Z"
                    },
                    {
                        "questionId": "preferredLocation",
                        "questionTitle": "期望工作地点",
                        "questionType": "text",
                        "value": "北京",
                        "answeredAt": "2025-07-29T06:02:00.000Z"
                    },
                    {
                        "questionId": "salaryExpectation",
                        "questionTitle": "薪资期望",
                        "questionType": "select",
                        "value": "12k_20k",
                        "answeredAt": "2025-07-29T06:02:00.000Z"
                    }
                ],
                "completedAt": "2025-07-29T06:02:00.000Z"
            }
        ]
    }'),
    json('{
        "responseId": "sample-response-001",
        "isAnonymous": true,
        "startedAt": "2025-07-29T06:00:00.000Z",
        "completedAt": "2025-07-29T06:02:00.000Z",
        "submittedAt": "2025-07-29T06:02:00.000Z",
        "userAgent": "Mozilla/5.0 (sample)",
        "ipAddress": "127.0.0.1",
        "source": "web",
        "version": "1.0.0"
    }'),
    'approved',
    '2025-07-29 06:02:00',
    '2025-07-29 06:02:00'
);

-- 初始化统计数据
INSERT OR IGNORE INTO questionnaire_statistics (questionnaire_id, question_id, statistics_data) VALUES
(
    'employment-survey-v1',
    'personal.gender',
    json('{
        "questionId": "personal.gender",
        "questionType": "radio",
        "totalResponses": 1,
        "distribution": {
            "male": 1,
            "female": 0,
            "other": 0
        },
        "lastUpdated": "2025-07-29T06:02:00.000Z"
    }')
),
(
    'employment-survey-v1',
    'education.degree',
    json('{
        "questionId": "education.degree",
        "questionType": "select",
        "totalResponses": 1,
        "distribution": {
            "bachelor": 1,
            "master": 0,
            "phd": 0,
            "associate": 0,
            "high_school": 0
        },
        "lastUpdated": "2025-07-29T06:02:00.000Z"
    }')
),
(
    'employment-survey-v1',
    'employment.salaryExpectation',
    json('{
        "questionId": "employment.salaryExpectation",
        "questionType": "select",
        "totalResponses": 1,
        "distribution": {
            "below_3k": 0,
            "3k_5k": 0,
            "5k_8k": 0,
            "8k_12k": 0,
            "12k_20k": 1,
            "above_20k": 0
        },
        "lastUpdated": "2025-07-29T06:02:00.000Z"
    }')
);
