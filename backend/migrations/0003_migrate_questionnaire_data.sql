-- Migration: 0003_migrate_questionnaire_data
-- Description: 将现有问卷数据迁移到新的通用结构 (如果存在的话)
-- Created: 2025-07-29

-- 检查是否存在原始表，如果存在则迁移数据
-- 如果不存在，则跳过迁移，直接初始化统计数据

-- 迁移现有问卷数据到新表结构 (仅当原表存在时)
INSERT INTO questionnaire_responses_v2 (
    id,
    questionnaire_id,
    user_id,
    response_data,
    metadata,
    status,
    created_at,
    updated_at
)
SELECT
    id,
    'employment-survey-v1' as questionnaire_id,
    user_id,
    -- 构建新的响应数据结构
    json_object(
        'questionnaireId', 'employment-survey-v1',
        'sectionResponses', json_array(
            -- 个人信息节
            json_object(
                'sectionId', 'personal',
                'sectionTitle', '个人信息',
                'questionResponses', json_array(
                    json_object(
                        'questionId', 'name',
                        'questionTitle', '姓名',
                        'questionType', 'text',
                        'value', json_extract(personal_info, '$.name'),
                        'answeredAt', created_at
                    ),
                    json_object(
                        'questionId', 'gender',
                        'questionTitle', '性别',
                        'questionType', 'radio',
                        'value', json_extract(personal_info, '$.gender'),
                        'answeredAt', created_at
                    ),
                    json_object(
                        'questionId', 'age',
                        'questionTitle', '年龄',
                        'questionType', 'number',
                        'value', json_extract(personal_info, '$.age'),
                        'answeredAt', created_at
                    ),
                    json_object(
                        'questionId', 'phone',
                        'questionTitle', '联系电话',
                        'questionType', 'text',
                        'value', json_extract(personal_info, '$.phone'),
                        'answeredAt', created_at
                    ),
                    json_object(
                        'questionId', 'email',
                        'questionTitle', '邮箱地址',
                        'questionType', 'email',
                        'value', json_extract(personal_info, '$.email'),
                        'answeredAt', created_at
                    )
                ),
                'completedAt', created_at
            ),
            -- 教育背景节
            json_object(
                'sectionId', 'education',
                'sectionTitle', '教育背景',
                'questionResponses', json_array(
                    json_object(
                        'questionId', 'degree',
                        'questionTitle', '学历层次',
                        'questionType', 'select',
                        'value', json_extract(education_info, '$.degree'),
                        'answeredAt', created_at
                    ),
                    json_object(
                        'questionId', 'major',
                        'questionTitle', '专业',
                        'questionType', 'text',
                        'value', json_extract(education_info, '$.major'),
                        'answeredAt', created_at
                    ),
                    json_object(
                        'questionId', 'university',
                        'questionTitle', '毕业院校',
                        'questionType', 'text',
                        'value', json_extract(education_info, '$.university'),
                        'answeredAt', created_at
                    ),
                    json_object(
                        'questionId', 'graduationYear',
                        'questionTitle', '毕业年份',
                        'questionType', 'number',
                        'value', json_extract(education_info, '$.graduationYear'),
                        'answeredAt', created_at
                    )
                ),
                'completedAt', created_at
            ),
            -- 就业意向节
            json_object(
                'sectionId', 'employment',
                'sectionTitle', '就业意向',
                'questionResponses', json_array(
                    json_object(
                        'questionId', 'preferredIndustry',
                        'questionTitle', '期望行业',
                        'questionType', 'checkbox',
                        'value', json_extract(employment_info, '$.preferredIndustry'),
                        'answeredAt', created_at
                    ),
                    json_object(
                        'questionId', 'preferredLocation',
                        'questionTitle', '期望工作地点',
                        'questionType', 'text',
                        'value', json_extract(employment_info, '$.preferredLocation'),
                        'answeredAt', created_at
                    ),
                    json_object(
                        'questionId', 'salaryExpectation',
                        'questionTitle', '薪资期望',
                        'questionType', 'select',
                        'value', json_extract(employment_info, '$.salaryExpectation'),
                        'answeredAt', created_at
                    )
                ),
                'completedAt', created_at
            )
        )
    ) as response_data,
    -- 构建元数据
    json_object(
        'responseId', id,
        'isAnonymous', CASE WHEN user_id IS NULL THEN 1 ELSE 0 END,
        'startedAt', created_at,
        'completedAt', created_at,
        'submittedAt', created_at,
        'userAgent', 'migrated-data',
        'ipAddress', '127.0.0.1',
        'migrationSource', 'questionnaire_responses',
        'migrationDate', datetime('now')
    ) as metadata,
    status,
    created_at,
    updated_at
FROM questionnaire_responses
WHERE personal_info IS NOT NULL 
  AND education_info IS NOT NULL 
  AND employment_info IS NOT NULL;

-- 创建统计数据的初始化
INSERT INTO questionnaire_statistics (questionnaire_id, question_id, statistics_data)
SELECT 
    'employment-survey-v1' as questionnaire_id,
    'personal.gender' as question_id,
    json_object(
        'questionId', 'personal.gender',
        'questionType', 'radio',
        'totalResponses', COUNT(*),
        'distribution', json_group_object(
            json_extract(personal_info, '$.gender'),
            COUNT(*)
        ),
        'lastUpdated', datetime('now')
    ) as statistics_data
FROM questionnaire_responses
WHERE json_extract(personal_info, '$.gender') IS NOT NULL
GROUP BY json_extract(personal_info, '$.gender')

UNION ALL

SELECT 
    'employment-survey-v1' as questionnaire_id,
    'education.degree' as question_id,
    json_object(
        'questionId', 'education.degree',
        'questionType', 'select',
        'totalResponses', COUNT(*),
        'distribution', json_group_object(
            json_extract(education_info, '$.degree'),
            COUNT(*)
        ),
        'lastUpdated', datetime('now')
    ) as statistics_data
FROM questionnaire_responses
WHERE json_extract(education_info, '$.degree') IS NOT NULL
GROUP BY json_extract(education_info, '$.degree')

UNION ALL

SELECT 
    'employment-survey-v1' as questionnaire_id,
    'employment.salaryExpectation' as question_id,
    json_object(
        'questionId', 'employment.salaryExpectation',
        'questionType', 'select',
        'totalResponses', COUNT(*),
        'distribution', json_group_object(
            json_extract(employment_info, '$.salaryExpectation'),
            COUNT(*)
        ),
        'lastUpdated', datetime('now')
    ) as statistics_data
FROM questionnaire_responses
WHERE json_extract(employment_info, '$.salaryExpectation') IS NOT NULL
GROUP BY json_extract(employment_info, '$.salaryExpectation');

-- 验证迁移结果
-- 这个查询可以用来验证数据迁移是否成功
/*
SELECT 
    'Original table count' as source,
    COUNT(*) as count
FROM questionnaire_responses

UNION ALL

SELECT 
    'New table count' as source,
    COUNT(*) as count
FROM questionnaire_responses_v2

UNION ALL

SELECT 
    'Statistics count' as source,
    COUNT(*) as count
FROM questionnaire_statistics;
*/
