-- Migration: 0002_questionnaire_v2
-- Description: 创建通用问卷系统表结构
-- Created: 2025-07-29

-- 问卷配置表
CREATE TABLE IF NOT EXISTS questionnaire_configs (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    config_data TEXT NOT NULL, -- JSON格式存储问卷配置
    status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'draft')),
    version TEXT NOT NULL DEFAULT '1.0.0',
    created_at TEXT NOT NULL DEFAULT (datetime('now')),
    updated_at TEXT NOT NULL DEFAULT (datetime('now'))
);

-- 新的问卷响应表 (通用结构)
CREATE TABLE IF NOT EXISTS questionnaire_responses_v2 (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    questionnaire_id TEXT NOT NULL,
    user_id TEXT,
    response_data TEXT NOT NULL, -- JSON格式存储完整响应
    metadata TEXT NOT NULL,      -- JSON格式存储元数据
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
    reviewer_id TEXT,
    review_comment TEXT,
    reviewed_at TEXT,
    created_at TEXT NOT NULL DEFAULT (datetime('now')),
    updated_at TEXT NOT NULL DEFAULT (datetime('now')),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (reviewer_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (questionnaire_id) REFERENCES questionnaire_configs(id)
);

-- 问卷统计表
CREATE TABLE IF NOT EXISTS questionnaire_statistics (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    questionnaire_id TEXT NOT NULL,
    question_id TEXT NOT NULL,
    statistics_data TEXT NOT NULL, -- JSON格式存储统计数据
    last_updated TEXT NOT NULL DEFAULT (datetime('now')),
    FOREIGN KEY (questionnaire_id) REFERENCES questionnaire_configs(id)
);

-- 问卷草稿表 (自动保存功能)
CREATE TABLE IF NOT EXISTS questionnaire_drafts (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    questionnaire_id TEXT NOT NULL,
    user_id TEXT,
    session_id TEXT,
    draft_data TEXT NOT NULL, -- JSON格式存储草稿数据
    last_saved TEXT NOT NULL DEFAULT (datetime('now')),
    expires_at TEXT NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (questionnaire_id) REFERENCES questionnaire_configs(id)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_questionnaire_responses_v2_questionnaire_id ON questionnaire_responses_v2(questionnaire_id);
CREATE INDEX IF NOT EXISTS idx_questionnaire_responses_v2_user_id ON questionnaire_responses_v2(user_id);
CREATE INDEX IF NOT EXISTS idx_questionnaire_responses_v2_status ON questionnaire_responses_v2(status);
CREATE INDEX IF NOT EXISTS idx_questionnaire_responses_v2_created_at ON questionnaire_responses_v2(created_at);

CREATE INDEX IF NOT EXISTS idx_questionnaire_statistics_questionnaire_id ON questionnaire_statistics(questionnaire_id);
CREATE INDEX IF NOT EXISTS idx_questionnaire_statistics_question_id ON questionnaire_statistics(question_id);

CREATE INDEX IF NOT EXISTS idx_questionnaire_drafts_questionnaire_id ON questionnaire_drafts(questionnaire_id);
CREATE INDEX IF NOT EXISTS idx_questionnaire_drafts_user_id ON questionnaire_drafts(user_id);
CREATE INDEX IF NOT EXISTS idx_questionnaire_drafts_session_id ON questionnaire_drafts(session_id);

-- 插入默认的就业调研问卷配置
INSERT OR REPLACE INTO questionnaire_configs (id, title, description, config_data, status, version) VALUES (
    'employment-survey-v1',
    '大学生就业调研问卷',
    '了解大学生就业现状、求职经历和职业期望',
    json('{
        "id": "employment-survey-v1",
        "title": "大学生就业调研问卷",
        "description": "了解大学生就业现状、求职经历和职业期望",
        "sections": [
            {
                "id": "personal",
                "title": "个人信息",
                "description": "请填写您的基本信息",
                "questions": [
                    {
                        "id": "name",
                        "type": "text",
                        "title": "姓名",
                        "required": true,
                        "validation": [
                            {"type": "required", "message": "请输入姓名"},
                            {"type": "minLength", "value": 2, "message": "姓名至少2个字符"}
                        ]
                    },
                    {
                        "id": "gender",
                        "type": "radio",
                        "title": "性别",
                        "required": true,
                        "options": [
                            {"value": "male", "label": "男"},
                            {"value": "female", "label": "女"},
                            {"value": "other", "label": "其他"}
                        ]
                    },
                    {
                        "id": "age",
                        "type": "number",
                        "title": "年龄",
                        "required": true,
                        "validation": [
                            {"type": "required", "message": "请输入年龄"},
                            {"type": "min", "value": 16, "message": "年龄不能小于16岁"},
                            {"type": "max", "value": 60, "message": "年龄不能大于60岁"}
                        ]
                    },
                    {
                        "id": "phone",
                        "type": "text",
                        "title": "联系电话",
                        "required": true,
                        "validation": [
                            {"type": "required", "message": "请输入联系电话"},
                            {"type": "pattern", "value": "^1[3-9]\\\\d{9}$", "message": "请输入正确的手机号码"}
                        ]
                    },
                    {
                        "id": "email",
                        "type": "email",
                        "title": "邮箱地址",
                        "required": true,
                        "validation": [
                            {"type": "required", "message": "请输入邮箱地址"},
                            {"type": "email", "message": "请输入正确的邮箱格式"}
                        ]
                    }
                ]
            },
            {
                "id": "education",
                "title": "教育背景",
                "description": "请填写您的教育相关信息",
                "questions": [
                    {
                        "id": "degree",
                        "type": "select",
                        "title": "学历层次",
                        "required": true,
                        "options": [
                            {"value": "high_school", "label": "高中/中专"},
                            {"value": "associate", "label": "大专"},
                            {"value": "bachelor", "label": "本科"},
                            {"value": "master", "label": "硕士"},
                            {"value": "phd", "label": "博士"}
                        ]
                    },
                    {
                        "id": "major",
                        "type": "text",
                        "title": "专业",
                        "required": true,
                        "validation": [
                            {"type": "required", "message": "请输入专业"}
                        ]
                    },
                    {
                        "id": "university",
                        "type": "text",
                        "title": "毕业院校",
                        "required": true,
                        "validation": [
                            {"type": "required", "message": "请输入毕业院校"}
                        ]
                    },
                    {
                        "id": "graduationYear",
                        "type": "number",
                        "title": "毕业年份",
                        "required": true,
                        "validation": [
                            {"type": "required", "message": "请输入毕业年份"},
                            {"type": "min", "value": 2000, "message": "毕业年份不能早于2000年"},
                            {"type": "max", "value": 2030, "message": "毕业年份不能晚于2030年"}
                        ]
                    }
                ]
            },
            {
                "id": "employment",
                "title": "就业意向",
                "description": "请填写您的就业期望和意向",
                "questions": [
                    {
                        "id": "preferredIndustry",
                        "type": "checkbox",
                        "title": "期望行业 (可多选)",
                        "required": true,
                        "options": [
                            {"value": "tech", "label": "科技/互联网"},
                            {"value": "finance", "label": "金融"},
                            {"value": "education", "label": "教育"},
                            {"value": "healthcare", "label": "医疗健康"},
                            {"value": "manufacturing", "label": "制造业"},
                            {"value": "government", "label": "政府/公共服务"},
                            {"value": "other", "label": "其他"}
                        ]
                    },
                    {
                        "id": "preferredLocation",
                        "type": "text",
                        "title": "期望工作地点",
                        "required": true,
                        "validation": [
                            {"type": "required", "message": "请输入期望工作地点"}
                        ]
                    },
                    {
                        "id": "salaryExpectation",
                        "type": "select",
                        "title": "薪资期望 (月薪)",
                        "required": true,
                        "options": [
                            {"value": "below_3k", "label": "3000元以下"},
                            {"value": "3k_5k", "label": "3000-5000元"},
                            {"value": "5k_8k", "label": "5000-8000元"},
                            {"value": "8k_12k", "label": "8000-12000元"},
                            {"value": "12k_20k", "label": "12000-20000元"},
                            {"value": "above_20k", "label": "20000元以上"}
                        ]
                    }
                ]
            }
        ],
        "config": {
            "allowAnonymous": true,
            "showStatistics": true,
            "autoSave": true,
            "theme": "employment-survey",
            "submitButtonText": "提交问卷",
            "progressType": "steps"
        }
    }'),
    'active',
    '1.0.0'
);
