name = "employment-survey-api"
main = "src/index.ts"
compatibility_date = "2024-07-01"
compatibility_flags = ["nodejs_compat"]

[env.development]
name = "employment-survey-api-dev"

[env.production]
name = "employment-survey-api-prod"

# D1 数据库配置
[[d1_databases]]
binding = "DB"
database_name = "employment-survey-db"
database_id = "312584b2-4160-4c1c-9cb9-035b19cd0a33"

# 环境变量
[vars]
ENVIRONMENT = "development"
JWT_SECRET = "your-jwt-secret-key-change-in-production"
CORS_ORIGIN = "http://localhost:5173"

# 生产环境变量
[env.production.vars]
ENVIRONMENT = "production"
CORS_ORIGIN = "https://your-domain.com"
