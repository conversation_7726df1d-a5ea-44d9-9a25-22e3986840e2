import { Hono } from 'hono';
import { serve } from '@hono/node-server';
import type { Env } from './types';
import { corsMiddleware } from './middleware/cors';
import { createAuthRoutes } from './routes/auth';
import { createQuestionnaireRoutes } from './routes/questionnaire';
import { createUUIDRoutes } from './routes/uuid';
import { dataGenerator } from './routes/dataGenerator';

// 创建Hono应用
const app = new Hono<{ Bindings: Env }>();

// 全局中间件
app.use('*', corsMiddleware);

// 健康检查端点
app.get('/health', (c) => {
  return c.json({
    success: true,
    data: {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      environment: c.env?.ENVIRONMENT || 'development',
    },
    message: 'API服务运行正常'
  });
});

// API路由前缀
app.route('/api', createApiRoutes());

// 404处理
app.notFound((c) => {
  return c.json({
    success: false,
    error: 'Not Found',
    message: '请求的资源不存在'
  }, 404);
});

// 错误处理
app.onError((err, c) => {
  console.error('Application error:', err);

  return c.json({
    success: false,
    error: 'Internal Server Error',
    message: c.env?.ENVIRONMENT === 'development' ? err.message : '服务器内部错误'
  }, 500);
});

// 创建API路由
function createApiRoutes() {
  const api = new Hono<{ Bindings: Env }>();

  // 认证路由
  api.route('/auth', createAuthRoutes());

  // UUID用户管理路由
  api.route('/uuid', createUUIDRoutes());

  // 问卷路由
  api.route('/questionnaire', createQuestionnaireRoutes());

  // 审核路由
  api.route('/review', createReviewRoutes());

  // 分析路由
  api.route('/analytics', createAnalyticsRoutes());

  // 数据生成器路由（管理员专用）
  api.route('/admin/data-generator', dataGenerator);

  return api;
}

// 认证路由现在从单独文件导入

// 问卷路由现在从单独文件导入

// 审核路由（占位符）
function createReviewRoutes() {
  const review = new Hono<{ Bindings: Env }>();
  
  review.get('/pending', (c) => {
    return c.json({
      success: true,
      data: { message: '待审核列表功能待实现' },
      message: '待审核列表接口'
    });
  });

  return review;
}

// 分析路由（占位符）
function createAnalyticsRoutes() {
  const analytics = new Hono<{ Bindings: Env }>();
  
  analytics.get('/summary', (c) => {
    return c.json({
      success: true,
      data: { message: '数据分析功能待实现' },
      message: '数据分析接口'
    });
  });

  return analytics;
}

// 本地开发服务器
// 模拟环境变量
const mockEnv: Env = {
  ENVIRONMENT: process.env.ENVIRONMENT || 'development',
  JWT_SECRET: process.env.JWT_SECRET || 'your-super-secret-jwt-key-for-development-only',
  CORS_ORIGIN: process.env.CORS_ORIGIN || 'http://localhost:5174,http://localhost:5173',
  DATABASE_URL: process.env.DATABASE_URL || 'file:./dev.db'
};

// 创建带环境变量的应用实例
const appWithEnv = new Hono();

appWithEnv.use('*', async (c, next) => {
  // 注入环境变量
  c.env = mockEnv;
  return next();
});

appWithEnv.route('/', app);

const port = process.env.PORT ? parseInt(process.env.PORT) : 8787;
console.log(`🚀 后端服务器启动在 http://localhost:${port}`);

serve({
  fetch: appWithEnv.fetch,
  port
});

export default app;
