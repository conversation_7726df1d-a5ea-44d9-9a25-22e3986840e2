import type { Context, Next } from 'hono';
import type { Env } from '../types';

export async function corsMiddleware(c: Context<{ Bindings: Env }>, next: Next) {
  const env = c.env;
  const origin = c.req.header('Origin');

  // 如果没有环境变量，使用默认值
  const corsOrigin = env?.CORS_ORIGIN || 'http://localhost:5173,http://localhost:5174,http://localhost:5175,http://localhost:5176,http://localhost:5177';
  const allowedOrigins = corsOrigin.split(',').map(o => o.trim());

  // 设置CORS头
  if (origin && allowedOrigins.includes(origin)) {
    c.header('Access-Control-Allow-Origin', origin);
  } else if (allowedOrigins.includes('*')) {
    c.header('Access-Control-Allow-Origin', '*');
  } else {
    // 开发环境允许所有本地端口
    c.header('Access-Control-Allow-Origin', '*');
  }

  c.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  c.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Request-Time');
  c.header('Access-Control-Allow-Credentials', 'true');
  c.header('Access-Control-Max-Age', '86400');

  // 处理预检请求
  if (c.req.method === 'OPTIONS') {
    return c.text('', 204);
  }

  return next();
}
