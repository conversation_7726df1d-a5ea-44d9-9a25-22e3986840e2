import type { Env } from '../types';

// 内存数据库模拟（用于本地开发）
class MockDatabase {
  private data: Map<string, any[]> = new Map();

  constructor() {
    // 初始化表
    this.data.set('users', []);
    this.data.set('questionnaires', []);
    this.data.set('reviews', []);
    this.data.set('user_sessions', []);
    this.data.set('user_content_mappings', []);
    this.data.set('auth_logs', []);
    this.data.set('questionnaire_configs', []);
    this.data.set('questionnaire_responses_v2', []);
    this.data.set('questionnaire_statistics', []);

    // 预加载管理员用户数据
    this.data.set('universal_users', [
      {
        uuid: 'super-550e8400-e29b-41d4-a716-************',
        userType: 'super_admin',
        username: 'superadmin',
        password_hash: '5cb96cf6e18e63ba9e269a78eada4fa6:847fa5475ea0d47bed0a390fcd50db859ce38911db93a71d9df8aca49860f23d',
        display_name: '超级管理员',
        role: 'super_admin',
        permissions: '["all_permissions"]',
        profile: '{"language": "zh-CN", "timezone": "Asia/Shanghai"}',
        metadata: '{"loginCount": 0}',
        status: 'active',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        last_active_at: new Date().toISOString()
      },
      {
        uuid: 'admin-550e8400-e29b-41d4-a716-************',
        userType: 'admin',
        username: 'admin1',
        password_hash: '5cb96cf6e18e63ba9e269a78eada4fa6:847fa5475ea0d47bed0a390fcd50db859ce38911db93a71d9df8aca49860f23d',
        display_name: '管理员',
        role: 'admin',
        permissions: '["browse_content", "project_management", "create_reviewer", "manage_users", "view_all_content", "system_settings", "view_all_stats", "review_content", "approve_content", "reject_content"]',
        profile: '{"language": "zh-CN", "timezone": "Asia/Shanghai"}',
        metadata: '{"loginCount": 0}',
        status: 'active',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        last_active_at: new Date().toISOString()
      },
      {
        uuid: 'rev-550e8400-e29b-41d4-a716-************',
        userType: 'reviewer',
        username: 'reviewerA',
        password_hash: '5cb96cf6e18e63ba9e269a78eada4fa6:847fa5475ea0d47bed0a390fcd50db859ce38911db93a71d9df8aca49860f23d',
        display_name: '审核员A',
        role: 'reviewer',
        permissions: '["browse_content", "review_content", "approve_content", "reject_content", "view_review_stats", "manage_review_queue"]',
        profile: '{"language": "zh-CN", "timezone": "Asia/Shanghai"}',
        metadata: '{"loginCount": 0}',
        status: 'active',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        last_active_at: new Date().toISOString()
      }
    ]);

    // 预加载问卷配置数据
    this.data.set('questionnaire_configs', [
      {
        id: 'employment-survey-v1',
        title: '大学生就业调研问卷',
        description: '了解大学生就业现状、求职经历和职业期望，为改善就业服务提供数据支持',
        version: '1.0.0',
        status: 'active',
        config_data: JSON.stringify({
          id: 'employment-survey-v1',
          title: '大学生就业调研问卷',
          description: '了解大学生就业现状、求职经历和职业期望，为改善就业服务提供数据支持',
          sections: [
            {
              id: 'personal',
              title: '个人信息',
              description: '请填写您的基本信息，所有信息将严格保密',
              questions: [
                {
                  id: 'name',
                  type: 'text',
                  title: '姓名',
                  required: true,
                  placeholder: '请输入您的真实姓名'
                },
                {
                  id: 'age',
                  type: 'number',
                  title: '年龄',
                  required: true,
                  placeholder: '请输入您的年龄',
                  min: 16,
                  max: 60
                },
                {
                  id: 'gender',
                  type: 'radio',
                  title: '性别',
                  required: true,
                  options: [
                    { value: 'male', label: '男' },
                    { value: 'female', label: '女' }
                  ]
                }
              ]
            },
            {
              id: 'education',
              title: '教育背景',
              description: '请填写您的教育经历和专业信息',
              questions: [
                {
                  id: 'university',
                  type: 'text',
                  title: '毕业院校',
                  required: true,
                  placeholder: '请输入您的毕业院校'
                },
                {
                  id: 'major',
                  type: 'text',
                  title: '专业',
                  required: true,
                  placeholder: '请输入您的专业'
                },
                {
                  id: 'degree',
                  type: 'select',
                  title: '学历',
                  required: true,
                  options: [
                    { value: 'bachelor', label: '本科' },
                    { value: 'master', label: '硕士' },
                    { value: 'doctor', label: '博士' }
                  ]
                }
              ]
            },
            {
              id: 'employment',
              title: '就业意向',
              description: '请填写您的就业期望和意向',
              questions: [
                {
                  id: 'industry',
                  type: 'checkbox',
                  title: '期望行业（可多选）',
                  required: true,
                  options: [
                    { value: 'tech', label: '科技/互联网' },
                    { value: 'finance', label: '金融/银行/保险' },
                    { value: 'education', label: '教育/培训' },
                    { value: 'healthcare', label: '医疗/健康' },
                    { value: 'manufacturing', label: '制造业' },
                    { value: 'consulting', label: '咨询服务' }
                  ]
                },
                {
                  id: 'location',
                  type: 'text',
                  title: '期望工作地点',
                  required: true,
                  placeholder: '请输入期望的工作城市'
                },
                {
                  id: 'salary',
                  type: 'select',
                  title: '薪资期望',
                  required: true,
                  options: [
                    { value: '3000-5000', label: '3000-5000元' },
                    { value: '5000-8000', label: '5000-8000元' },
                    { value: '8000-12000', label: '8000-12000元' },
                    { value: '12000-20000', label: '12000-20000元' },
                    { value: '20000+', label: '20000元以上' }
                  ]
                },
                {
                  id: 'workType',
                  type: 'radio',
                  title: '工作类型偏好',
                  required: true,
                  options: [
                    { value: 'fulltime', label: '全职工作' },
                    { value: 'parttime', label: '兼职工作' },
                    { value: 'internship', label: '实习机会' },
                    { value: 'freelance', label: '自由职业' }
                  ]
                }
              ]
            }
          ],
          config: {
            allowAnonymous: true,
            showStatistics: true,
            autoSave: true,
            theme: 'employment-survey',
            submitButtonText: '提交问卷'
          }
        }),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        created_by: 'system'
      }
    ]);
  }

  prepare(sql: string) {
    return {
      bind: (...params: any[]) => ({
        all: async () => {
          // 简单的 SQL 解析和执行
          const results = this.executeSQL(sql, params);
          return { results };
        },
        first: async () => {
          const results = this.executeSQL(sql, params);
          return results.length > 0 ? results[0] : null;
        },
        run: async () => {
          const results = this.executeSQL(sql, params);
          const lastRowId = results.length > 0 && results[0].id ? results[0].id : Math.floor(Math.random() * 1000000);
          return {
            success: true,
            meta: {
              changes: results.length,
              last_row_id: lastRowId
            }
          };
        }
      })
    };
  }

  batch(statements: any[]) {
    return Promise.resolve(statements.map(() => ({ success: true })));
  }

  private executeSQL(sql: string, params: any[] = []): any[] {
    // 非常简单的 SQL 模拟
    const sqlLower = sql.toLowerCase();

    // 处理 universal_users 表
    if (sqlLower.includes('universal_users')) {
      if (sqlLower.includes('select')) {
        const users = this.data.get('universal_users') || [];
        if (sqlLower.includes('where identity_hash')) {
          console.log('Searching for identity_hash:', params[0]);
          console.log('Existing users:', users.map(u => ({ uuid: u.uuid, identity_hash: u.identity_hash })));
          const foundUser = users.find(u => u.identity_hash === params[0]);
          console.log('Found user:', foundUser ? foundUser.uuid : 'none');
          return foundUser ? [foundUser] : [];
        }
        if (sqlLower.includes('where username')) {
          console.log('Searching for username:', params[0]);
          console.log('Existing users:', users.map(u => ({ uuid: u.uuid, username: u.username, user_type: u.userType })));
          const foundUser = users.find(u => u.username === params[0]);
          console.log('Found user:', foundUser ? foundUser.uuid : 'none');
          return foundUser ? [foundUser] : [];
        }
        return users;
      }
      if (sqlLower.includes('insert')) {
        const users = this.data.get('universal_users') || [];
        const newUser = {
          uuid: params[0],
          user_type: params[1],
          identity_hash: params[2],
          display_name: params[3],
          role: params[4],
          permissions: params[5],
          profile: params[6],
          metadata: params[7],
          status: params[8],
          created_at: params[9],
          updated_at: params[10],
          last_active_at: params[11]
        };
        users.push(newUser);
        this.data.set('universal_users', users);
        return [newUser];
      }
      if (sqlLower.includes('update')) {
        const users = this.data.get('universal_users') || [];
        const userIndex = users.findIndex(u => u.uuid === params[1]);
        if (userIndex >= 0) {
          users[userIndex].last_active_at = params[0];
          this.data.set('universal_users', users);
        }
        return [];
      }
    }

    // 处理 user_sessions 表
    if (sqlLower.includes('user_sessions')) {
      if (sqlLower.includes('insert')) {
        const sessions = this.data.get('user_sessions') || [];
        const newSession = {
          session_id: params[0],
          user_uuid: params[1],
          session_token: params[2],
          device_fingerprint: params[3],
          ip_address: params[4],
          user_agent: params[5],
          device_info: params[6],
          expires_at: params[7],
          is_active: params[8],
          created_at: params[9],
          updated_at: params[10]
        };
        sessions.push(newSession);
        this.data.set('user_sessions', sessions);
        return [newSession];
      }
    }

    // 处理 auth_logs 表
    if (sqlLower.includes('auth_logs')) {
      if (sqlLower.includes('insert')) {
        const logs = this.data.get('auth_logs') || [];
        const newLog = {
          id: Math.floor(Math.random() * 1000000),
          user_uuid: params[0],
          user_type: params[1],
          action: params[2],
          ip_address: params[3],
          user_agent: params[4],
          device_fingerprint: params[5],
          success: params[6],
          error_message: params[7],
          metadata: params[8],
          created_at: params[9]
        };
        logs.push(newLog);
        this.data.set('auth_logs', logs);
        return [newLog];
      }
    }

    // 处理 questionnaire_responses 表
    if (sqlLower.includes('questionnaire_responses')) {
      if (sqlLower.includes('insert')) {
        const questionnaires = this.data.get('questionnaires') || [];
        const newQuestionnaire = {
          id: Math.floor(Math.random() * 1000000),
          user_id: params[0],
          personal_info: params[1],
          education_info: params[2],
          employment_info: params[3],
          job_search_info: params[4],
          employment_status: params[5],
          status: params[6],
          created_at: params[7],
          updated_at: params[8]
        };
        questionnaires.push(newQuestionnaire);
        this.data.set('questionnaires', questionnaires);
        return [newQuestionnaire];
      }
      if (sqlLower.includes('select')) {
        return this.data.get('questionnaires') || [];
      }
    }

    // 原有的 users 表处理
    if (sqlLower.includes('users') && !sqlLower.includes('universal_users')) {
      if (sqlLower.includes('select')) {
        return this.data.get('users') || [];
      }
      if (sqlLower.includes('insert')) {
        const users = this.data.get('users') || [];
        const newUser = {
          id: Math.floor(Math.random() * 1000000).toString(),
          username: params[0] || 'test',
          email: params[1] || '<EMAIL>',
          password_hash: params[2] || 'hash',
          role: params[3] || 'user',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
        users.push(newUser);
        this.data.set('users', users);
        return [newUser];
      }
    }

    // 处理 questionnaire_configs 表
    if (sqlLower.includes('questionnaire_configs')) {
      if (sqlLower.includes('select')) {
        const configs = this.data.get('questionnaire_configs') || [];
        if (sqlLower.includes('where id') && sqlLower.includes('status')) {
          // SELECT * FROM questionnaire_configs WHERE id = ? AND status = ?
          const foundConfig = configs.find(c => c.id === params[0] && c.status === params[1]);
          return foundConfig ? [foundConfig] : [];
        }
        if (sqlLower.includes('where id')) {
          // SELECT * FROM questionnaire_configs WHERE id = ?
          const foundConfig = configs.find(c => c.id === params[0]);
          return foundConfig ? [foundConfig] : [];
        }
        return configs;
      }
    }

    // 处理 questionnaire_responses_v2 表
    if (sqlLower.includes('questionnaire_responses_v2')) {
      if (sqlLower.includes('insert')) {
        const responses = this.data.get('questionnaire_responses_v2') || [];
        const newResponse = {
          id: params[0],
          questionnaire_id: params[1],
          user_id: params[2],
          response_data: params[3],
          metadata: params[4],
          status: params[5],
          created_at: params[6],
          updated_at: params[7]
        };
        responses.push(newResponse);
        this.data.set('questionnaire_responses_v2', responses);
        return [newResponse];
      }
      if (sqlLower.includes('select')) {
        return this.data.get('questionnaire_responses_v2') || [];
      }
    }

    // 处理 questionnaire_statistics 表
    if (sqlLower.includes('questionnaire_statistics')) {
      if (sqlLower.includes('select')) {
        const stats = this.data.get('questionnaire_statistics') || [];
        if (sqlLower.includes('where questionnaire_id') && sqlLower.includes('question_id')) {
          const foundStat = stats.find(s => s.questionnaire_id === params[0] && s.question_id === params[1]);
          return foundStat ? [foundStat] : [];
        }
        return stats;
      }
      if (sqlLower.includes('insert')) {
        const stats = this.data.get('questionnaire_statistics') || [];
        const newStat = {
          id: Math.floor(Math.random() * 1000000),
          questionnaire_id: params[0],
          question_id: params[1],
          statistics_data: params[2],
          updated_at: params[3]
        };
        stats.push(newStat);
        this.data.set('questionnaire_statistics', stats);
        return [newStat];
      }
      if (sqlLower.includes('update')) {
        const stats = this.data.get('questionnaire_statistics') || [];
        const statIndex = stats.findIndex(s => s.questionnaire_id === params[1] && s.question_id === params[2]);
        if (statIndex >= 0) {
          stats[statIndex].statistics_data = params[0];
          stats[statIndex].updated_at = params[3];
          this.data.set('questionnaire_statistics', stats);
        }
        return [];
      }
    }

    return [];
  }
}

export class DatabaseService {
  private db: D1Database | MockDatabase;

  constructor(db?: D1Database) {
    this.db = db || new MockDatabase();
  }

  // 执行查询
  async query<T = any>(sql: string, params: any[] = []): Promise<T[]> {
    try {
      const result = await this.db.prepare(sql).bind(...params).all();
      return result.results as T[];
    } catch (error) {
      console.error('Database query error:', error);
      throw new Error('Database query failed');
    }
  }

  // 执行单个查询
  async queryFirst<T = any>(sql: string, params: any[] = []): Promise<T | null> {
    try {
      const result = await this.db.prepare(sql).bind(...params).first();
      return result as T | null;
    } catch (error) {
      console.error('Database query error:', error);
      throw new Error('Database query failed');
    }
  }

  // 执行插入/更新/删除
  async execute(sql: string, params: any[] = []): Promise<D1Result> {
    try {
      const result = await this.db.prepare(sql).bind(...params).run();
      return result;
    } catch (error) {
      console.error('Database execute error:', error);
      throw new Error('Database operation failed');
    }
  }

  // 批量执行
  async batch(statements: { sql: string; params?: any[] }[]): Promise<D1Result[]> {
    try {
      const prepared = statements.map(stmt => 
        this.db.prepare(stmt.sql).bind(...(stmt.params || []))
      );
      const results = await this.db.batch(prepared);
      return results;
    } catch (error) {
      console.error('Database batch error:', error);
      throw new Error('Database batch operation failed');
    }
  }

  // 事务处理
  async transaction<T>(callback: (db: DatabaseService) => Promise<T>): Promise<T> {
    // D1 目前不支持显式事务，这里提供一个接口以备将来使用
    return await callback(this);
  }

  // 分页查询
  async paginate<T = any>(
    sql: string, 
    params: any[] = [], 
    page: number = 1, 
    pageSize: number = 10
  ): Promise<{
    items: T[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  }> {
    // 获取总数
    const countSql = `SELECT COUNT(*) as count FROM (${sql})`;
    const countResult = await this.queryFirst<{ count: number }>(countSql, params);
    const total = countResult?.count || 0;

    // 获取分页数据
    const offset = (page - 1) * pageSize;
    const paginatedSql = `${sql} LIMIT ? OFFSET ?`;
    const items = await this.query<T>(paginatedSql, [...params, pageSize, offset]);

    return {
      items,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    };
  }
}

// 创建数据库服务实例
export function createDatabaseService(env?: Env): DatabaseService {
  return new DatabaseService(env?.DB);
}
