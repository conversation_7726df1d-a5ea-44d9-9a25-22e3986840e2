import { Hono } from 'hono';
import { serve } from '@hono/node-server';
import { createQuestionnaireRoutes } from './routes/questionnaire';

// 创建简单的Hono应用
const app = new Hono();

// 简单的健康检查端点
app.get('/health', (c) => {
  return c.json({
    success: true,
    message: 'Server is running',
    timestamp: new Date().toISOString()
  });
});

// CORS 中间件
app.use('*', async (c, next) => {
  c.header('Access-Control-Allow-Origin', '*');
  c.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  c.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Request-Time');
  c.header('Access-Control-Allow-Credentials', 'true');

  if (c.req.method === 'OPTIONS') {
    return c.text('', 204);
  }

  return next();
});

// API路由
app.get('/api/health', (c) => {
  return c.json({
    success: true,
    message: 'API is working',
    timestamp: new Date().toISOString()
  });
});

// 模拟认证端点
app.post('/api/auth/login', async (c) => {
  const body = await c.req.json();

  // 简单的模拟登录
  if (body.username && body.password) {
    return c.json({
      success: true,
      data: {
        user: {
          id: '1',
          username: body.username,
          email: body.username + '@example.com',
          role: 'user',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        token: 'mock-jwt-token-' + Date.now()
      },
      message: '登录成功'
    });
  } else {
    return c.json({
      success: false,
      error: 'Invalid credentials',
      message: '用户名或密码错误'
    }, 401);
  }
});

// 模拟注册端点
app.post('/api/auth/register', async (c) => {
  const body = await c.req.json();

  return c.json({
    success: true,
    data: {
      user: {
        id: '2',
        username: body.username,
        email: body.email,
        role: body.role || 'user',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    },
    message: '注册成功'
  });
});

// 模拟环境变量
const mockEnv = {
  DB: null, // 暂时不使用数据库
  ENVIRONMENT: 'development',
  JWT_SECRET: 'test-secret',
  CORS_ORIGIN: 'http://localhost:5173'
};

// 注释掉问卷路由，先使用模拟端点
// app.route('/api/questionnaire', createQuestionnaireRoutes());

// 模拟问卷配置端点
app.get('/api/questionnaire/config/:id', async (c) => {
  const questionnaireId = c.req.param('id');

  if (questionnaireId === 'employment-survey-v1') {
    return c.json({
      success: true,
      data: {
        id: 'employment-survey-v1',
        title: '大学生就业调研问卷',
        description: '了解大学生就业现状、求职经历和职业期望',
        config: {
          id: 'employment-survey-v1',
          title: '大学生就业调研问卷',
          description: '了解大学生就业现状、求职经历和职业期望',
          sections: [
            {
              id: 'personal',
              title: '个人信息',
              description: '请填写您的基本信息',
              questions: [
                {
                  id: 'name',
                  type: 'text',
                  title: '姓名',
                  required: true,
                  validation: [
                    { type: 'required', message: '请输入姓名' },
                    { type: 'minLength', value: 2, message: '姓名至少2个字符' }
                  ]
                },
                {
                  id: 'gender',
                  type: 'radio',
                  title: '性别',
                  required: true,
                  options: [
                    { value: 'male', label: '男' },
                    { value: 'female', label: '女' },
                    { value: 'other', label: '其他' }
                  ]
                },
                {
                  id: 'age',
                  type: 'number',
                  title: '年龄',
                  required: true,
                  validation: [
                    { type: 'required', message: '请输入年龄' },
                    { type: 'min', value: 16, message: '年龄不能小于16岁' },
                    { type: 'max', value: 60, message: '年龄不能大于60岁' }
                  ]
                }
              ]
            }
          ],
          config: {
            allowAnonymous: true,
            showStatistics: true,
            autoSave: true,
            theme: 'employment-survey'
          }
        },
        version: '1.0.0',
        status: 'active'
      },
      message: '获取问卷配置成功'
    });
  }

  return c.json({
    success: false,
    error: 'Not Found',
    message: '问卷配置不存在'
  }, 404);
});

// 模拟问卷统计端点
app.get('/api/questionnaire/statistics/:id', async (c) => {
  const questionnaireId = c.req.param('id');

  return c.json({
    success: true,
    data: [
      {
        questionId: 'personal.gender',
        data: {
          questionId: 'personal.gender',
          questionType: 'radio',
          totalResponses: 10,
          distribution: {
            male: 6,
            female: 4,
            other: 0
          },
          lastUpdated: new Date().toISOString()
        },
        lastUpdated: new Date().toISOString()
      }
    ],
    message: '获取统计数据成功'
  });
});

// 404处理
app.notFound((c) => {
  return c.json({
    success: false,
    error: 'Not Found',
    message: '请求的资源不存在'
  }, 404);
});

// 错误处理
app.onError((err, c) => {
  console.error('Application error:', err);
  
  return c.json({
    success: false,
    error: 'Internal Server Error',
    message: err.message
  }, 500);
});

const port = 8788;
console.log(`🚀 简单后端服务器启动在 http://localhost:${port}`);

serve({
  fetch: app.fetch,
  port
});

export default app;
