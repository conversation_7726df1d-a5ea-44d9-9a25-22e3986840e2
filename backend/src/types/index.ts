// 环境变量类型
export interface Env {
  DB: D1Database;
  JWT_SECRET: string;
  ENVIRONMENT: 'development' | 'production';
  CORS_ORIGIN: string;
}

// 用户相关类型
export interface User {
  id: string;
  username: string;
  email: string;
  password_hash: string;
  role: UserRole;
  created_at: string;
  updated_at: string;
}

export type UserRole = 'user' | 'reviewer' | 'admin' | 'super_admin';

export interface CreateUserRequest {
  username: string;
  email: string;
  password: string;
  role?: UserRole;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  user: Omit<User, 'password_hash'>;
  token: string;
}

// 问卷相关类型
export interface QuestionnaireResponse {
  id: string;
  user_id: string;
  personal_info: string; // JSON string
  education_info: string; // JSON string
  employment_info: string; // JSON string
  job_search_info: string; // JSON string
  employment_status: string; // JSON string
  status: 'pending' | 'approved' | 'rejected';
  created_at: string;
  updated_at: string;
}

export interface CreateQuestionnaireRequest {
  personalInfo: PersonalInfo;
  educationInfo: EducationInfo;
  employmentInfo: EmploymentInfo;
  jobSearchInfo: JobSearchInfo;
  employmentStatus: EmploymentStatus;
}

export interface PersonalInfo {
  name: string;
  gender: 'male' | 'female' | 'other';
  age: number;
  phone: string;
  email: string;
}

export interface EducationInfo {
  university: string;
  major: string;
  degree: 'bachelor' | 'master' | 'doctor';
  graduationYear: number;
  gpa?: number;
}

export interface EmploymentInfo {
  preferredIndustry: string[];
  preferredPosition: string;
  expectedSalary: number;
  preferredLocation: string[];
  workExperience: string;
}

export interface JobSearchInfo {
  searchChannels: string[];
  interviewCount: number;
  offerCount: number;
  searchDuration: number;
}

export interface EmploymentStatus {
  currentStatus: 'employed' | 'unemployed' | 'continuing_education' | 'other';
  currentCompany?: string;
  currentPosition?: string;
  currentSalary?: number;
  satisfactionLevel?: number;
}

// 审核相关类型
export interface Review {
  id: string;
  questionnaire_id: string;
  reviewer_id: string;
  status: 'approved' | 'rejected';
  comment: string;
  created_at: string;
}

export interface CreateReviewRequest {
  questionnaireId: string;
  status: 'approved' | 'rejected';
  comment: string;
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message: string;
}

export interface ApiError {
  success: false;
  error: string;
  message: string;
  code?: number;
}

// 分页类型
export interface PaginationParams {
  page: number;
  pageSize: number;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// JWT载荷类型
export interface JWTPayload {
  userId: string;
  username: string;
  role: UserRole;
  iat: number;
  exp: number;
}

// 中间件上下文类型
export interface AuthContext {
  user: Omit<User, 'password_hash'>;
}
