import { Hono } from 'hono';
import type { Env, AuthContext } from '../types';
import { createDatabaseService } from '../db';
import { authMiddleware, optionalAuthMiddleware } from '../middleware/auth';

// 新的问卷系统类型定义
interface QuestionnaireSubmissionRequest {
  questionnaireId: string;
  sectionResponses: SectionResponse[];
  metadata?: ResponseMetadata;
}

interface SectionResponse {
  sectionId: string;
  sectionTitle?: string;
  questionResponses: QuestionResponse[];
  completedAt?: string;
}

interface QuestionResponse {
  questionId: string;
  questionTitle?: string;
  questionType?: string;
  value: any;
  answeredAt: string;
}

interface ResponseMetadata {
  responseId?: string;
  isAnonymous?: boolean;
  startedAt?: string;
  completedAt?: string;
  submittedAt?: string;
  userAgent?: string;
  ipAddress?: string;
  source?: string;
  version?: string;
}

// 统计更新函数
async function updateQuestionnaireStatistics(db: any, questionnaireId: string, sectionResponses: SectionResponse[]) {
  for (const section of sectionResponses) {
    for (const response of section.questionResponses) {
      const questionKey = `${section.sectionId}.${response.questionId}`;

      // 获取现有统计数据
      const existingStats = await db.queryFirst<any>(
        'SELECT statistics_data FROM questionnaire_statistics WHERE questionnaire_id = ? AND question_id = ?',
        [questionnaireId, questionKey]
      );

      let statsData;
      if (existingStats) {
        // 更新现有统计
        statsData = JSON.parse(existingStats.statistics_data);
        statsData.totalResponses = (statsData.totalResponses || 0) + 1;

        if (!statsData.distribution) {
          statsData.distribution = {};
        }

        const valueKey = Array.isArray(response.value) ? response.value.join(',') : String(response.value);
        statsData.distribution[valueKey] = (statsData.distribution[valueKey] || 0) + 1;
        statsData.lastUpdated = new Date().toISOString();

        // 更新统计数据
        await db.execute(
          'UPDATE questionnaire_statistics SET statistics_data = ?, last_updated = ? WHERE questionnaire_id = ? AND question_id = ?',
          [JSON.stringify(statsData), statsData.lastUpdated, questionnaireId, questionKey]
        );
      } else {
        // 创建新统计
        const valueKey = Array.isArray(response.value) ? response.value.join(',') : String(response.value);
        statsData = {
          questionId: questionKey,
          questionType: response.questionType || 'text',
          totalResponses: 1,
          distribution: {
            [valueKey]: 1
          },
          lastUpdated: new Date().toISOString()
        };

        // 插入新统计数据
        await db.execute(
          'INSERT INTO questionnaire_statistics (questionnaire_id, question_id, statistics_data) VALUES (?, ?, ?)',
          [questionnaireId, questionKey, JSON.stringify(statsData)]
        );
      }
    }
  }
}

export function createQuestionnaireRoutes() {
  const questionnaire = new Hono<{ Bindings: Env; Variables: AuthContext }>();

  // 提交问卷 (公开接口，不需要认证) - 新版本
  questionnaire.post('/', optionalAuthMiddleware, async (c) => {
    try {
      const body = await c.req.json() as QuestionnaireSubmissionRequest;
      const { questionnaireId, sectionResponses, metadata } = body;

      // 验证必填字段
      if (!questionnaireId || !sectionResponses || !Array.isArray(sectionResponses)) {
        return c.json({
          success: false,
          error: 'Validation Error',
          message: '问卷数据格式不正确'
        }, 400);
      }

      // 验证问卷ID是否存在
      const db = createDatabaseService(c.env);
      const questionnaireConfig = await db.queryFirst<any>(
        'SELECT * FROM questionnaire_configs WHERE id = ? AND status = ?',
        [questionnaireId, 'active']
      );

      if (!questionnaireConfig) {
        return c.json({
          success: false,
          error: 'Validation Error',
          message: '问卷配置不存在或已停用'
        }, 400);
      }

      // 验证节响应数据
      if (sectionResponses.length === 0) {
        return c.json({
          success: false,
          error: 'Validation Error',
          message: '问卷响应数据不能为空'
        }, 400);
      }

      // 验证每个节的数据完整性
      for (const section of sectionResponses) {
        if (!section.sectionId || !section.questionResponses || !Array.isArray(section.questionResponses)) {
          return c.json({
            success: false,
            error: 'Validation Error',
            message: `节 ${section.sectionId} 数据格式不正确`
          }, 400);
        }

        // 验证问题响应
        for (const response of section.questionResponses) {
          if (!response.questionId || response.value === undefined || response.value === null) {
            return c.json({
              success: false,
              error: 'Validation Error',
              message: `问题 ${response.questionId} 的回答不能为空`
            }, 400);
          }
        }
      }

      const user = c.get('user'); // 可能为空（匿名提交）

      // 生成响应ID和元数据
      const responseId = `resp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const now = new Date().toISOString();

      const responseMetadata: ResponseMetadata = {
        responseId,
        isAnonymous: !user,
        startedAt: metadata?.startedAt || now,
        completedAt: metadata?.completedAt || now,
        submittedAt: now,
        userAgent: c.req.header('User-Agent') || 'unknown',
        ipAddress: c.req.header('CF-Connecting-IP') || c.req.header('X-Forwarded-For') || '127.0.0.1',
        source: 'web',
        version: '2.0.0',
        ...metadata
      };

      // 构建完整的响应数据
      const responseData = {
        questionnaireId,
        sectionResponses,
        metadata: responseMetadata
      };

      // 插入问卷数据到新表
      const result = await db.execute(
        `INSERT INTO questionnaire_responses_v2
         (id, questionnaire_id, user_id, response_data, metadata, status)
         VALUES (?, ?, ?, ?, ?, ?)`,
        [
          responseId,
          questionnaireId,
          user?.id || null,
          JSON.stringify(responseData),
          JSON.stringify(responseMetadata),
          'pending' // 默认状态为待审核
        ]
      );

      if (!result.success) {
        throw new Error('Failed to insert questionnaire');
      }

      // 更新统计数据 (异步处理，不影响响应)
      updateQuestionnaireStatistics(db, questionnaireId, sectionResponses).catch(error => {
        console.error('Failed to update statistics:', error);
      });

      // 记录系统日志
      if (user) {
        await db.execute(
          `INSERT INTO system_logs (user_id, action, resource_type, resource_id, details, ip_address)
           VALUES (?, ?, ?, ?, ?, ?)`,
          [
            user.id,
            'CREATE',
            'questionnaire',
            responseId,
            JSON.stringify({
              action: 'submit_questionnaire',
              questionnaireId,
              timestamp: now
            }),
            responseMetadata.ipAddress
          ]
        );
      }

      return c.json({
        success: true,
        data: {
          responseId,
          questionnaireId,
          status: 'pending',
          submittedAt: now
        },
        message: '问卷提交成功，感谢您的参与！'
      }, 201);

    } catch (error: any) {
      console.error('Questionnaire submission error:', error);
      return c.json({
        success: false,
        error: 'Internal Server Error',
        message: '提交失败，请稍后重试'
      }, 500);
    }
  });

  // 获取问卷列表 (需要认证)
  questionnaire.get('/', authMiddleware, async (c) => {
    try {
      const user = c.get('user');
      const page = parseInt(c.req.query('page') || '1');
      const pageSize = parseInt(c.req.query('pageSize') || '10');
      const status = c.req.query('status');
      const startDate = c.req.query('startDate');
      const endDate = c.req.query('endDate');

      const db = createDatabaseService(c.env);

      // 构建查询条件
      let whereClause = '1=1';
      const params: any[] = [];

      // 根据用户角色过滤数据
      if (user.role === 'user') {
        whereClause += ' AND user_id = ?';
        params.push(user.id);
      }

      if (status) {
        whereClause += ' AND status = ?';
        params.push(status);
      }

      if (startDate) {
        whereClause += ' AND created_at >= ?';
        params.push(startDate);
      }

      if (endDate) {
        whereClause += ' AND created_at <= ?';
        params.push(endDate);
      }

      const sql = `SELECT * FROM questionnaire_responses_v2 WHERE ${whereClause} ORDER BY created_at DESC`;

      const result = await db.paginate<any>(sql, params, page, pageSize);

      // 格式化返回数据
      const formattedData = result.data.map((item: any) => ({
        id: item.id,
        questionnaireId: item.questionnaire_id,
        userId: item.user_id,
        responseData: JSON.parse(item.response_data),
        metadata: JSON.parse(item.metadata),
        status: item.status,
        reviewerId: item.reviewer_id,
        reviewComment: item.review_comment,
        reviewedAt: item.reviewed_at,
        createdAt: item.created_at,
        updatedAt: item.updated_at
      }));

      return c.json({
        success: true,
        data: formattedData,
        pagination: result.pagination,
        message: '获取问卷列表成功'
      });

    } catch (error: any) {
      console.error('Get questionnaires error:', error);
      return c.json({
        success: false,
        error: 'Internal Server Error',
        message: '获取问卷列表失败'
      }, 500);
    }
  });

  // 获取单个问卷详情 (需要认证)
  questionnaire.get('/:id', authMiddleware, async (c) => {
    try {
      const id = c.req.param('id');
      const user = c.get('user');
      const db = createDatabaseService(c.env);

      let sql = 'SELECT * FROM questionnaire_responses_v2 WHERE id = ?';
      const params = [id];

      // 普通用户只能查看自己的问卷
      if (user.role === 'user') {
        sql += ' AND user_id = ?';
        params.push(user.id);
      }

      const questionnaire = await db.queryFirst<any>(sql, params);

      if (!questionnaire) {
        return c.json({
          success: false,
          error: 'Not Found',
          message: '问卷不存在或无权访问'
        }, 404);
      }

      // 格式化返回数据
      const formattedData = {
        id: questionnaire.id,
        questionnaireId: questionnaire.questionnaire_id,
        userId: questionnaire.user_id,
        responseData: JSON.parse(questionnaire.response_data),
        metadata: JSON.parse(questionnaire.metadata),
        status: questionnaire.status,
        reviewerId: questionnaire.reviewer_id,
        reviewComment: questionnaire.review_comment,
        reviewedAt: questionnaire.reviewed_at,
        createdAt: questionnaire.created_at,
        updatedAt: questionnaire.updated_at
      };

      return c.json({
        success: true,
        data: formattedData,
        message: '获取问卷详情成功'
      });

    } catch (error: any) {
      console.error('Get questionnaire error:', error);
      return c.json({
        success: false,
        error: 'Internal Server Error',
        message: '获取问卷详情失败'
      }, 500);
    }
  });

  // 更新问卷状态 (需要审核员权限)
  questionnaire.put('/:id', authMiddleware, async (c) => {
    try {
      const id = c.req.param('id');
      const user = c.get('user');
      const body = await c.req.json();
      const { status, comment } = body;

      // 检查权限
      if (!['reviewer', 'admin', 'super_admin'].includes(user.role)) {
        return c.json({
          success: false,
          error: 'Forbidden',
          message: '权限不足'
        }, 403);
      }

      // 验证状态
      if (!['approved', 'rejected'].includes(status)) {
        return c.json({
          success: false,
          error: 'Validation Error',
          message: '无效的状态值'
        }, 400);
      }

      const db = createDatabaseService(c.env);

      // 检查问卷是否存在
      const questionnaire = await db.queryFirst<QuestionnaireResponse>(
        'SELECT * FROM questionnaire_responses WHERE id = ?',
        [id]
      );

      if (!questionnaire) {
        return c.json({
          success: false,
          error: 'Not Found',
          message: '问卷不存在'
        }, 404);
      }

      // 更新问卷状态
      await db.execute(
        'UPDATE questionnaire_responses SET status = ? WHERE id = ?',
        [status, id]
      );

      // 记录审核记录
      await db.execute(
        'INSERT INTO reviews (questionnaire_id, reviewer_id, status, comment) VALUES (?, ?, ?, ?)',
        [id, user.id, status, comment || '']
      );

      // 记录系统日志
      await db.execute(
        `INSERT INTO system_logs (user_id, action, resource_type, resource_id, details, ip_address) 
         VALUES (?, ?, ?, ?, ?, ?)`,
        [
          user.id,
          'UPDATE',
          'questionnaire',
          id,
          JSON.stringify({ 
            action: `${status}_questionnaire`, 
            comment,
            timestamp: new Date().toISOString() 
          }),
          c.req.header('CF-Connecting-IP') || c.req.header('X-Forwarded-For') || 'unknown'
        ]
      );

      return c.json({
        success: true,
        data: { id, status },
        message: `问卷${status === 'approved' ? '审核通过' : '审核拒绝'}`
      });

    } catch (error: any) {
      console.error('Update questionnaire error:', error);
      return c.json({
        success: false,
        error: 'Internal Server Error',
        message: '更新问卷状态失败'
      }, 500);
    }
  });

  // 获取问卷配置 (公开接口)
  questionnaire.get('/config/:id', async (c) => {
    try {
      const questionnaireId = c.req.param('id');
      const db = createDatabaseService(c.env);

      const config = await db.queryFirst<any>(
        'SELECT * FROM questionnaire_configs WHERE id = ? AND status = ?',
        [questionnaireId, 'active']
      );

      if (!config) {
        return c.json({
          success: false,
          error: 'Not Found',
          message: '问卷配置不存在'
        }, 404);
      }

      return c.json({
        success: true,
        data: {
          id: config.id,
          title: config.title,
          description: config.description,
          config: JSON.parse(config.config_data),
          version: config.version,
          status: config.status
        },
        message: '获取问卷配置成功'
      });

    } catch (error: any) {
      console.error('Get questionnaire config error:', error);
      return c.json({
        success: false,
        error: 'Internal Server Error',
        message: '获取问卷配置失败'
      }, 500);
    }
  });

  // 获取问卷统计数据 (公开接口)
  questionnaire.get('/statistics/:id', async (c) => {
    try {
      const questionnaireId = c.req.param('id');
      const questionId = c.req.query('questionId');
      const db = createDatabaseService(c.env);

      let sql = 'SELECT * FROM questionnaire_statistics WHERE questionnaire_id = ?';
      const params = [questionnaireId];

      if (questionId) {
        sql += ' AND question_id = ?';
        params.push(questionId);
      }

      const statistics = await db.query<any>(sql, params);

      const formattedStats = statistics.map((stat: any) => ({
        questionId: stat.question_id,
        data: JSON.parse(stat.statistics_data),
        lastUpdated: stat.last_updated
      }));

      return c.json({
        success: true,
        data: formattedStats,
        message: '获取统计数据成功'
      });

    } catch (error: any) {
      console.error('Get questionnaire statistics error:', error);
      return c.json({
        success: false,
        error: 'Internal Server Error',
        message: '获取统计数据失败'
      }, 500);
    }
  });

  return questionnaire;
}
