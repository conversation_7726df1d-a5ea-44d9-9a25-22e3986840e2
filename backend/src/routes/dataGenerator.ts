/**
 * 数据生成器路由
 * 提供批量测试数据生成功能
 */

import { Hono } from 'hono';
import { createDatabaseService } from '../db';
import { authMiddleware } from '../middleware/auth';

const dataGenerator = new Hono();

// 数据生成配置
interface GenerationConfig {
  type: 'questionnaire' | 'story' | 'voice' | 'user';
  count: number;
  quality: 'basic' | 'standard' | 'premium';
  batchSize?: number;
  template?: string;
  options?: {
    includeVoices?: boolean;
    diversity?: number;
    realism?: number;
    creativity?: number;
  };
}

// 数据模板
const dataTemplates = {
  questionnaire: {
    personalInfo: {
      names: ['张伟', '李娜', '王强', '刘敏', '陈杰', '杨丽', '赵磊', '孙静', '周涛', '吴雪'],
      ages: [22, 23, 24, 25, 26],
      genders: ['male', 'female'],
      phones: ['138****1234', '139****5678', '186****9012'],
      emails: ['<EMAIL>', '<EMAIL>', '<EMAIL>']
    },
    educationInfo: {
      universities: ['北京大学', '清华大学', '复旦大学', '上海交通大学', '浙江大学', '南京大学'],
      majors: ['计算机科学与技术', '软件工程', '电子信息工程', '机械工程', '金融学', '市场营销'],
      degrees: ['bachelor', 'master', 'doctor'],
      graduationYears: [2022, 2023, 2024]
    },
    employmentInfo: {
      statuses: ['employed', 'unemployed', 'studying', 'entrepreneurship'],
      companies: ['阿里巴巴', '腾讯', '百度', '字节跳动', '美团', '滴滴', '京东', '网易'],
      positions: ['软件工程师', '产品经理', '数据分析师', '运营专员', '设计师', '测试工程师'],
      salaries: ['5000-8000', '8000-12000', '12000-20000', '20000-30000', '30000+']
    },
    jobSearchInfo: {
      industries: ['互联网', '金融', '教育', '医疗', '制造业', '房地产', '零售'],
      positions: ['技术岗位', '管理岗位', '销售岗位', '运营岗位', '设计岗位'],
      expectedSalaries: ['8000-12000', '12000-18000', '18000-25000', '25000+']
    },
    advice: [
      '建议学弟学妹们要注重实践能力的培养，多参加实习和项目经验。',
      '在校期间要保持学习的热情，技术更新很快，需要不断跟进。',
      '面试时要展现自己的项目经验和解决问题的能力。',
      '简历要突出重点，展示自己的核心竞争力。',
      '要有明确的职业规划，知道自己想要什么。'
    ],
    observations: [
      '当前就业市场竞争激烈，需要不断提升自己的综合能力。',
      '企业更看重实际能力和项目经验，而不仅仅是学历。',
      '新兴技术领域有更多机会，但也需要持续学习。',
      '软技能同样重要，沟通能力和团队协作能力不可忽视。',
      '要关注行业发展趋势，选择有前景的方向。'
    ]
  },
  story: {
    titles: [
      '我的求职之路：从迷茫到收获心仪offer',
      '转行程序员的心路历程',
      '应届生求职经验分享',
      '从实习到正式工作的转变',
      '职场新人的成长感悟'
    ],
    contents: [
      '回想起自己的求职经历，真是五味杂陈。作为一名应届毕业生，刚开始投简历时总是石沉大海，让我对自己的能力产生了怀疑。后来通过不断学习和练习，逐渐提升了技术水平，调整了求职策略，最终收到了心仪公司的offer。',
      '决定转行做程序员是一个艰难的决定。从零开始学习编程，每天都在挑战自己的极限。虽然过程很辛苦，但看到自己一点点进步，最终成功转行，这种成就感是无法言喻的。',
      '作为应届生，求职过程中遇到了很多挑战。技术面试、项目经验、简历优化等都需要认真准备。通过参加技术分享会、刷题练习、项目实战，最终找到了满意的工作。'
    ]
  },
  voice: {
    encouragements: [
      '相信自己，每一次努力都不会白费！',
      '求职路虽然艰辛，但坚持下去一定会有收获。',
      '不要害怕失败，每次面试都是宝贵的经验。',
      '保持积极的心态，机会总是留给有准备的人。',
      '学弟学妹们加油，你们一定可以的！'
    ],
    experiences: [
      '面试前一定要充分准备，了解公司和岗位要求。',
      '简历要简洁明了，突出自己的核心优势。',
      '技术面试要多练习，熟悉常见的算法和数据结构。',
      '项目经验很重要，要能清楚地表达自己的贡献。',
      '保持学习的习惯，技术更新很快。'
    ]
  }
};

// 生成随机数据
function generateRandomData(type: string, template: string, count: number) {
  const results = [];
  
  for (let i = 0; i < count; i++) {
    switch (type) {
      case 'questionnaire':
        results.push(generateQuestionnaireData());
        break;
      case 'story':
        results.push(generateStoryData());
        break;
      case 'voice':
        results.push(generateVoiceData());
        break;
      case 'user':
        results.push(generateUserData());
        break;
    }
  }
  
  return results;
}

function generateQuestionnaireData() {
  const templates = dataTemplates.questionnaire;
  
  return {
    personalInfo: {
      name: getRandomItem(templates.personalInfo.names),
      age: getRandomItem(templates.personalInfo.ages),
      gender: getRandomItem(templates.personalInfo.genders),
      phone: getRandomItem(templates.personalInfo.phones),
      email: getRandomItem(templates.personalInfo.emails)
    },
    educationInfo: {
      university: getRandomItem(templates.educationInfo.universities),
      major: getRandomItem(templates.educationInfo.majors),
      degree: getRandomItem(templates.educationInfo.degrees),
      graduationYear: getRandomItem(templates.educationInfo.graduationYears)
    },
    employmentInfo: {
      currentStatus: getRandomItem(templates.employmentInfo.statuses),
      company: getRandomItem(templates.employmentInfo.companies),
      position: getRandomItem(templates.employmentInfo.positions),
      salary: getRandomItem(templates.employmentInfo.salaries)
    },
    jobSearchInfo: {
      preferredIndustry: getRandomItem(templates.jobSearchInfo.industries),
      preferredPosition: getRandomItem(templates.jobSearchInfo.positions),
      expectedSalary: getRandomItem(templates.jobSearchInfo.expectedSalaries)
    },
    employmentStatus: {
      isEmployed: Math.random() > 0.3,
      employmentType: Math.random() > 0.2 ? 'full-time' : 'part-time'
    },
    adviceForStudents: getRandomItem(templates.advice),
    observationsOnEmployment: getRandomItem(templates.observations)
  };
}

function generateStoryData() {
  const templates = dataTemplates.story;
  
  return {
    title: getRandomItem(templates.titles),
    content: getRandomItem(templates.contents),
    category: 'job_hunting',
    tags: ['求职', '经验分享', '职场'],
    author: getRandomItem(dataTemplates.questionnaire.personalInfo.names),
    status: 'pending'
  };
}

function generateVoiceData() {
  const templates = dataTemplates.voice;
  
  return {
    content: Math.random() > 0.5 
      ? getRandomItem(templates.encouragements)
      : getRandomItem(templates.experiences),
    type: Math.random() > 0.5 ? 'encouragement' : 'experience',
    author: getRandomItem(dataTemplates.questionnaire.personalInfo.names),
    status: 'pending'
  };
}

function generateUserData() {
  const templates = dataTemplates.questionnaire;
  
  return {
    username: `user_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`,
    displayName: getRandomItem(templates.personalInfo.names),
    email: getRandomItem(templates.personalInfo.emails),
    userType: 'semi_anonymous',
    status: 'active'
  };
}

function getRandomItem<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)];
}

// 需要管理员权限
dataGenerator.use('*', authMiddleware);

// 开始数据生成
dataGenerator.post('/start', async (c) => {
  try {
    const config: GenerationConfig = await c.req.json();
    const db = createDatabaseService(c.env);
    
    // 生成数据
    const generatedData = generateRandomData(
      config.type,
      config.template || 'basic',
      config.count
    );
    
    // 批量插入数据库
    let insertedCount = 0;
    const batchSize = config.batchSize || 10;
    
    for (let i = 0; i < generatedData.length; i += batchSize) {
      const batch = generatedData.slice(i, i + batchSize);
      
      for (const item of batch) {
        try {
          if (config.type === 'questionnaire') {
            await db.execute(
              `INSERT INTO questionnaire_responses 
               (user_id, personal_info, education_info, employment_info, job_search_info, employment_status, status, created_at, updated_at)
               VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
              [
                null,
                JSON.stringify(item.personalInfo),
                JSON.stringify(item.educationInfo),
                JSON.stringify(item.employmentInfo),
                JSON.stringify(item.jobSearchInfo),
                JSON.stringify(item.employmentStatus),
                'pending',
                new Date().toISOString(),
                new Date().toISOString()
              ]
            );
            insertedCount++;
          }
          // 可以添加其他类型的数据插入逻辑
        } catch (error) {
          console.error('插入数据失败:', error);
        }
      }
    }
    
    return c.json({
      success: true,
      data: {
        generationId: `gen_${Date.now()}`,
        totalCount: config.count,
        insertedCount,
        batchSize: config.batchSize || 10,
        estimatedTime: config.count * 100,
        status: 'completed'
      },
      message: `成功生成 ${insertedCount} 条${config.type}数据`
    });
    
  } catch (error: any) {
    console.error('数据生成失败:', error);
    return c.json({
      success: false,
      error: 'Internal Server Error',
      message: '数据生成失败'
    }, 500);
  }
});

// 获取生成统计
dataGenerator.get('/stats', async (c) => {
  try {
    const db = createDatabaseService(c.env);
    
    // 获取今日生成数量（模拟数据）
    const todayGenerated = Math.floor(Math.random() * 50) + 20;
    const totalGenerated = Math.floor(Math.random() * 1000) + 500;
    const pendingReview = Math.floor(Math.random() * 100) + 50;
    
    return c.json({
      success: true,
      data: {
        todayGenerated,
        totalGenerated,
        pendingReview,
        approvedCount: totalGenerated - pendingReview,
        rejectedCount: Math.floor(totalGenerated * 0.1),
        passRate: 85.2,
        averageResponseTime: 1250,
        successRate: 94.8,
        errorRate: 5.2
      },
      message: '获取统计数据成功'
    });
    
  } catch (error: any) {
    console.error('获取统计失败:', error);
    return c.json({
      success: false,
      error: 'Internal Server Error',
      message: '获取统计失败'
    }, 500);
  }
});

// 获取生成进度（模拟）
dataGenerator.get('/progress/:id', async (c) => {
  const generationId = c.req.param('id');
  
  return c.json({
    success: true,
    data: {
      generationId,
      completed: Math.floor(Math.random() * 100),
      total: 100,
      currentBatch: Math.floor(Math.random() * 10) + 1,
      totalBatches: 10,
      status: 'running',
      errors: [],
      estimatedTimeRemaining: Math.floor(Math.random() * 5000)
    },
    message: '获取进度成功'
  });
});

// 取消生成任务
dataGenerator.post('/cancel/:id', async (c) => {
  const generationId = c.req.param('id');
  
  return c.json({
    success: true,
    data: { generationId, status: 'cancelled' },
    message: '生成任务已取消'
  });
});

export { dataGenerator };
