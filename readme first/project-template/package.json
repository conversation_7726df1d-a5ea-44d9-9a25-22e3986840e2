{"name": "college-employment-survey-v2", "version": "1.0.0", "description": "大学生就业问卷调查平台 V2", "private": true, "workspaces": ["frontend", "backend", "shared"], "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "npm run dev --workspace=frontend", "dev:backend": "npm run dev --workspace=backend", "build": "npm run build --workspaces", "test": "npm run test --workspaces", "lint": "npm run lint --workspaces", "type-check": "npm run type-check --workspaces", "deploy": "npm run deploy --workspaces", "clean": "npm run clean --workspaces && rm -rf node_modules"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "concurrently": "^8.2.0", "eslint": "^8.45.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^5.0.0", "husky": "^8.0.3", "lint-staged": "^13.2.3", "prettier": "^3.0.0", "typescript": "^5.1.6"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "*.{json,md,yml,yaml}": ["prettier --write"]}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.6.0"}