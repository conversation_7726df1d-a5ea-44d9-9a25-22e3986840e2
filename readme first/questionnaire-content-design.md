# 问卷内容设计 V1

## 🎯 问卷设计原则

- **用户友好**: 问题简洁明了，易于理解
- **逻辑清晰**: 问题顺序符合用户思维逻辑
- **数据价值**: 每个问题都有明确的分析价值
- **隐私保护**: 支持匿名提交，保护用户隐私
- **适应性强**: 支持不同就业状态的用户

## 📋 问卷结构设计

### 整体流程
```
开始 → 基本信息 → 教育背景 → 就业状况 → 求职经历 → 工作体验 → 建议分享 → 提交
```

### 预计完成时间
- **总时长**: 8-12分钟
- **题目数量**: 15-20题
- **必填题目**: 8题
- **选填题目**: 7-12题

## 📝 具体问卷内容

### 第一部分：基本信息 (必填)

#### 1. 学历层次
**题目**: 您的最高学历是？
**类型**: 单选题
**选项**:
- 高中/中专
- 专科/高职
- 本科
- 硕士研究生
- 博士研究生

#### 2. 专业领域
**题目**: 您的专业属于哪个领域？
**类型**: 单选题
**选项**:
- 计算机/软件工程
- 电子信息/通信
- 机械/自动化
- 经济/金融
- 管理/商务
- 文学/语言
- 教育/师范
- 医学/生物
- 艺术/设计
- 法学/政治
- 理学/数学
- 其他

#### 3. 毕业年份
**题目**: 您的毕业年份是？
**类型**: 下拉选择
**选项**: 2018年-2024年

#### 4. 所在地区
**题目**: 您目前所在的地区是？
**类型**: 单选题
**选项**:
- 北京
- 上海
- 广州
- 深圳
- 杭州
- 南京
- 成都
- 武汉
- 西安
- 重庆
- 天津
- 苏州
- 其他一线城市
- 其他二线城市
- 三四线城市
- 县城/农村

### 第二部分：就业状况 (必填)

#### 5. 当前就业状态
**题目**: 您目前的就业状态是？
**类型**: 单选题
**选项**:
- 已就业（全职）
- 已就业（兼职/实习）
- 求职中
- 准备考研/考公
- 自主创业
- 暂不就业
- 继续深造

#### 6. 求职时长 (条件显示：选择"求职中"时显示)
**题目**: 您已经求职多长时间了？
**类型**: 单选题
**选项**:
- 1个月以内
- 1-3个月
- 3-6个月
- 6-12个月
- 1年以上

#### 7. 工作满意度 (条件显示：选择"已就业"时显示)
**题目**: 您对当前工作的整体满意度是？
**类型**: 评分题 (1-10分)
**说明**: 1分=非常不满意，10分=非常满意

#### 8. 薪资范围 (条件显示：选择"已就业"时显示)
**题目**: 您当前的月薪范围是？（税前）
**类型**: 单选题
**选项**:
- 3000元以下
- 3000-5000元
- 5000-8000元
- 8000-12000元
- 12000-20000元
- 20000-30000元
- 30000元以上
- 不方便透露

### 第三部分：求职经历 (选填)

#### 9. 求职渠道
**题目**: 您主要通过哪些渠道求职？（可多选）
**类型**: 多选题
**选项**:
- 招聘网站（如智联、前程无忧）
- 校园招聘会
- 社交平台（如LinkedIn、脉脉）
- 内推/熟人介绍
- 企业官网直投
- 猎头推荐
- 实习转正
- 其他

#### 10. 求职难点
**题目**: 您在求职过程中遇到的主要困难是？（可多选）
**类型**: 多选题
**选项**:
- 缺乏相关工作经验
- 专业技能不够匹配
- 简历投递无回应
- 面试表现不佳
- 薪资期望与现实差距大
- 地域限制
- 行业竞争激烈
- 缺乏求职技巧
- 其他

### 第四部分：工作体验 (选填)

#### 11. 工作与专业匹配度 (条件显示：已就业用户)
**题目**: 您的工作与所学专业的匹配程度如何？
**类型**: 单选题
**选项**:
- 非常匹配
- 比较匹配
- 一般匹配
- 不太匹配
- 完全不匹配

#### 12. 职业发展规划
**题目**: 您对未来3-5年的职业发展有什么规划？
**类型**: 文本题
**字数限制**: 200字以内

### 第五部分：建议分享 (核心内容)

#### 13. 给学弟学妹的建议 (必填)
**题目**: 基于您的经历，您想对正在求职或即将求职的学弟学妹说些什么？
**类型**: 长文本题
**字数限制**: 50-500字
**提示**: 可以分享求职经验、技能建议、心态调整等

#### 14. 对就业形势的观察 (选填)
**题目**: 您如何看待当前的就业形势？有什么观察和思考？
**类型**: 长文本题
**字数限制**: 50-300字

### 第六部分：提交设置

#### 15. 匿名设置
**题目**: 您希望以什么方式分享您的经历？
**类型**: 单选题
**选项**:
- 完全匿名（不显示任何个人信息）
- 部分匿名（仅显示学历和地区）
- 实名分享（显示昵称，需要邮箱验证）

#### 16. 邮箱验证 (条件显示：选择"实名分享"时)
**题目**: 请输入您的邮箱地址用于验证
**类型**: 邮箱输入框
**验证**: 邮箱格式验证

## 🔧 技术实现规范

### 数据验证规则
```typescript
interface QuestionnaireValidation {
  educationLevel: {
    required: true,
    type: 'string',
    enum: ['高中/中专', '专科/高职', '本科', '硕士研究生', '博士研究生']
  },
  major: {
    required: true,
    type: 'string',
    maxLength: 50
  },
  graduationYear: {
    required: true,
    type: 'number',
    min: 2018,
    max: 2024
  },
  region: {
    required: true,
    type: 'string',
    maxLength: 20
  },
  employmentStatus: {
    required: true,
    type: 'string',
    enum: ['已就业（全职）', '已就业（兼职/实习）', '求职中', '准备考研/考公', '自主创业', '暂不就业', '继续深造']
  },
  adviceForStudents: {
    required: true,
    type: 'string',
    minLength: 50,
    maxLength: 500
  },
  observationOnEmployment: {
    required: false,
    type: 'string',
    maxLength: 300
  },
  isAnonymous: {
    required: true,
    type: 'boolean'
  }
}
```

### 条件逻辑
```typescript
interface ConditionalLogic {
  // 求职时长仅在"求职中"时显示
  jobSearchDuration: {
    showWhen: { employmentStatus: '求职中' },
    required: true
  },
  
  // 工作满意度和薪资仅在"已就业"时显示
  jobSatisfaction: {
    showWhen: { employmentStatus: ['已就业（全职）', '已就业（兼职/实习）'] },
    required: false
  },
  
  // 邮箱验证仅在选择实名分享时显示
  emailVerification: {
    showWhen: { isAnonymous: false },
    required: true
  }
}
```

### 数据脱敏处理
```typescript
interface DataDeidentification {
  // 自动脱敏规则
  autoMask: {
    phone: /1[3-9]\d{9}/g,           // 手机号
    email: /\w+@\w+\.\w+/g,          // 邮箱
    idCard: /\d{15}|\d{18}/g,        // 身份证
    bankCard: /\d{16,19}/g           // 银行卡
  },
  
  // 敏感词过滤
  sensitiveWords: ['具体公司名', '真实姓名', '详细地址'],
  
  // 地区泛化
  regionGeneralization: {
    '北京市朝阳区': '北京',
    '上海市浦东新区': '上海'
  }
}
```

## 📊 数据分析维度

### 统计分析指标
- **学历分布**: 各学历层次的就业情况
- **专业热度**: 不同专业的就业难易程度
- **地区差异**: 不同地区的就业机会和薪资水平
- **时间趋势**: 就业形势的时间变化
- **满意度分析**: 工作满意度与各因素的关联

### 可视化图表
- 学历-就业状态分布图
- 专业-薪资范围散点图
- 地区就业热力图
- 求职时长分布图
- 满意度评分分布图

---

*此问卷设计确保收集到有价值的就业数据，同时保护用户隐私，为后续数据分析和可视化提供坚实基础。*
