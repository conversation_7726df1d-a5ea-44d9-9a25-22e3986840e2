{"name": "frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "lint:fix": "eslint . --fix", "preview": "vite preview", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:watch": "vitest --watch", "clean": "rm -rf dist node_modules/.vite", "deploy": "echo \"Deploy script not configured\" && exit 0"}, "dependencies": {"@ant-design/icons": "^5.3.7", "antd": "^5.21.4", "axios": "^1.7.7", "dayjs": "^1.11.13", "echarts": "^5.5.1", "echarts-for-react": "^3.0.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.26.2", "zustand": "^4.5.5"}, "devDependencies": {"@eslint/js": "^9.9.1", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^22.5.4", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "@vitest/ui": "^3.2.4", "autoprefixer": "^10.4.20", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "jsdom": "^26.1.0", "postcss": "^8.4.45", "tailwindcss": "^3.4.10", "typescript": "^5.5.4", "typescript-eslint": "^8.4.0", "vite": "^5.4.3", "vitest": "^3.2.4"}}