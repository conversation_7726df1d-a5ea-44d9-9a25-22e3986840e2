# Cloudflare Pages 配置文件

name = "college-employment-survey-frontend"
compatibility_date = "2024-01-01"

[env.production]
# 生产环境配置
vars = { ENVIRONMENT = "production" }

[env.staging]
# 预发布环境配置
vars = { ENVIRONMENT = "staging" }

[build]
# 构建配置
command = "npm run build"
cwd = "."
watch_dir = "src"

[build.upload]
# 上传配置
format = "modules"
dir = "dist"
main = "./dist/index.html"

# 环境变量
[vars]
VITE_APP_ENV = "production"
VITE_APP_TITLE = "大学生就业问卷调查平台"
VITE_APP_VERSION = "1.0.0"
VITE_ENABLE_ANALYTICS = "true"
VITE_ENABLE_ERROR_BOUNDARY = "true"
VITE_ENABLE_PERFORMANCE_MONITORING = "true"
