/**
 * 管理系统认证状态管理
 * 专门管理管理员和审核员的认证状态
 */

import React from 'react';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

import type {
  ManagementUser,
  ManagementSession,
  ManagementAuthResult,
  ManagementCredentials,
  ManagementPermission,
  ManagementPermissionResult,
  ManagementUserType
} from '../types/management-auth';

import { MANAGEMENT_ROUTE_PERMISSIONS } from '../types/management-auth';
import { managementAuthService } from '../services/managementAuthService';

interface ManagementAuthState {
  // 状态
  currentUser: ManagementUser | null;
  currentSession: ManagementSession | null;
  authToken: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;

  // 认证操作
  login: (credentials: ManagementCredentials) => Promise<boolean>;
  logout: () => void;
  
  // 权限检查
  hasPermission: (permission: ManagementPermission) => boolean;
  hasUserType: (userType: ManagementUserType) => boolean;
  isAdmin: () => boolean;
  isReviewer: () => boolean;
  canAccessRoute: (route: string) => ManagementPermissionResult;
  
  // 会话管理
  refreshSession: () => Promise<void>;
  checkSession: () => boolean;
  
  // 工具方法
  clearError: () => void;
  initializeAuth: () => void;
  getDefaultRedirectPath: () => string;
}

// 临时禁用持久化来测试问题
export const useManagementAuthStore = create<ManagementAuthState>()(
  // persist(
    (set, get) => ({
      // 初始状态
      currentUser: null,
      currentSession: null,
      authToken: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // 管理员/审核员登录
      login: async (credentials: ManagementCredentials): Promise<boolean> => {
        set({ isLoading: true, error: null });

        try {
          // 1. 直接调用服务层登录
          const success = await managementAuthService.login(credentials);
          console.log('🔍 管理员登录服务层调用完成');

          // 2. 强制从服务层获取最新状态
          const currentUser = managementAuthService.getCurrentUser();
          const currentSession = managementAuthService.getCurrentSession();
          const authToken = managementAuthService.getAuthToken();
          const isValid = managementAuthService.isSessionValid();

          console.log('🔍 服务层状态检查:', {
            hasUser: !!currentUser,
            hasSession: !!currentSession,
            hasToken: !!authToken,
            isValid,
            userType: currentUser?.userType
          });

          // 3. 如果服务层有有效状态，强制更新Store
          if (currentUser && currentSession && isValid) {
            // 使用直接赋值而不是set方法
            const newState = {
              currentUser,
              currentSession,
              authToken,
              isAuthenticated: true,
              isLoading: false,
              error: null
            };

            // 直接设置状态
            set(newState);

            console.log('✅ 管理用户登录成功，状态已强制更新:', {
              userType: currentUser.userType,
              username: currentUser.username,
              isAuthenticated: true
            });

            // 验证状态是否正确设置
            setTimeout(() => {
              const currentState = get();
              console.log('⏱️ 延迟验证store状态:', {
                isAuthenticated: currentState.isAuthenticated,
                hasUser: !!currentState.currentUser,
                userType: currentState.currentUser?.userType
              });
            }, 100);

            return true;
          } else {
            console.log('❌ 服务层状态无效');
            set({
              isLoading: false,
              error: '登录状态设置失败'
            });
            return false;
          }
        } catch (error: any) {
          set({
            isLoading: false,
            error: error.message || '登录过程中发生错误'
          });
          return false;
        }
      },

      // 登出
      logout: (): void => {
        managementAuthService.logout();
        set({
          currentUser: null,
          currentSession: null,
          authToken: null,
          isAuthenticated: false,
          isLoading: false,
          error: null
        });
        console.log('管理用户已登出');
      },

      // 权限检查
      hasPermission: (permission: ManagementPermission): boolean => {
        const { currentUser } = get();
        if (!currentUser) return false;
        return currentUser.permissions.includes(permission);
      },

      // 用户类型检查
      hasUserType: (userType: ManagementUserType): boolean => {
        const { currentUser } = get();
        if (!currentUser) return false;
        return currentUser.userType === userType;
      },

      // 检查是否为管理员
      isAdmin: (): boolean => {
        const { currentUser } = get();
        if (!currentUser) return false;
        return ['ADMIN', 'SUPER_ADMIN'].includes(currentUser.userType);
      },

      // 检查是否为审核员
      isReviewer: (): boolean => {
        const { currentUser } = get();
        if (!currentUser) return false;
        return ['REVIEWER', 'ADMIN', 'SUPER_ADMIN'].includes(currentUser.userType);
      },

      // 路由访问检查
      canAccessRoute: (route: string): ManagementPermissionResult => {
        const { currentUser } = get();
        
        // 获取路由所需权限
        const requiredPermissions = MANAGEMENT_ROUTE_PERMISSIONS[route];
        
        // 如果路由不需要权限，允许访问
        if (!requiredPermissions || requiredPermissions.length === 0) {
          return { hasPermission: true };
        }
        
        // 如果用户未登录
        if (!currentUser) {
          return {
            hasPermission: false,
            reason: '需要登录才能访问此页面',
            requiredPermissions
          };
        }
        
        // 检查是否具有任意一个所需权限
        const hasAccess = requiredPermissions.some(permission => 
          currentUser.permissions.includes(permission)
        );
        
        if (!hasAccess) {
          return {
            hasPermission: false,
            reason: '您没有访问此页面的权限',
            requiredPermissions
          };
        }
        
        return { hasPermission: true };
      },

      // 刷新会话
      refreshSession: async (): Promise<void> => {
        try {
          const success = await managementAuthService.refreshSession();
          if (success) {
            const currentUser = managementAuthService.getCurrentUser();
            const currentSession = managementAuthService.getCurrentSession();
            const authToken = managementAuthService.getAuthToken();
            
            set({
              currentUser,
              currentSession,
              authToken,
              isAuthenticated: !!currentUser && !!currentSession && managementAuthService.isSessionValid()
            });
          } else {
            // 刷新失败，清除认证状态
            set({
              currentUser: null,
              currentSession: null,
              authToken: null,
              isAuthenticated: false
            });
          }
        } catch (error) {
          console.error('刷新管理会话失败:', error);
          set({
            currentUser: null,
            currentSession: null,
            authToken: null,
            isAuthenticated: false
          });
        }
      },

      // 检查会话有效性
      checkSession: (): boolean => {
        return managementAuthService.isSessionValid();
      },

      // 清除错误
      clearError: (): void => {
        set({ error: null });
      },

      // 获取默认重定向路径
      getDefaultRedirectPath: (): string => {
        return managementAuthService.getDefaultRedirectPath();
      },

      // 初始化认证状态
      initializeAuth: (): void => {
        // 总是检查服务层状态并同步到 Store
        const currentUser = managementAuthService.getCurrentUser();
        const currentSession = managementAuthService.getCurrentSession();
        const authToken = managementAuthService.getAuthToken();
        const isValid = managementAuthService.isSessionValid();

        if (currentUser && currentSession && isValid) {
          // 强制同步到 Store
          set({
            currentUser,
            currentSession,
            authToken,
            isAuthenticated: true,
            isLoading: false,
            error: null
          });
        } else {
          // 清除认证状态
          set({
            currentUser: null,
            currentSession: null,
            authToken: null,
            isAuthenticated: false,
            isLoading: false,
            error: null
          });
          managementAuthService.logout();
        }
      }
    })
    // {
    //   name: 'management-auth-storage',
    //   partialize: (state) => ({
    //     // 持久化认证状态和用户信息
    //     isAuthenticated: state.isAuthenticated,
    //     currentUser: state.currentUser,
    //     currentSession: state.currentSession,
    //     authToken: state.authToken
    //   })
    // }
  // )
);

// 导出便捷的hook
export const useManagementAuth = () => {
  const store = useManagementAuthStore();
  
  // 初始化时检查认证状态
  React.useEffect(() => {
    store.initializeAuth();
    store.refreshSession();
  }, []);
  
  return store;
};

// 导出权限检查hook
export const useManagementPermission = (permission: ManagementPermission) => {
  const hasPermission = useManagementAuthStore(state => state.hasPermission);
  return hasPermission(permission);
};

// 导出路由保护hook
export const useManagementRouteAccess = (route: string) => {
  const canAccessRoute = useManagementAuthStore(state => state.canAccessRoute);
  return canAccessRoute(route);
};

// 导出用户类型检查hook
export const useManagementUserType = () => {
  const currentUser = useManagementAuthStore(state => state.currentUser);
  const hasUserType = useManagementAuthStore(state => state.hasUserType);
  const isAdmin = useManagementAuthStore(state => state.isAdmin);
  const isReviewer = useManagementAuthStore(state => state.isReviewer);
  
  return {
    currentUser,
    hasUserType,
    isAdmin: isAdmin(),
    isReviewer: isReviewer(),
    userType: currentUser?.userType || null
  };
};
