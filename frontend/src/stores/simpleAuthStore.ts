import { create } from 'zustand';

interface SimpleAuthState {
  isAuthenticated: boolean;
  user: { username: string; role: string } | null;
  login: (username: string) => void;
  logout: () => void;
}

export const useSimpleAuthStore = create<SimpleAuthState>((set) => ({
  isAuthenticated: false,
  user: null,
  login: (username: string) => {
    set({
      isAuthenticated: true,
      user: { username, role: 'user' }
    });
  },
  logout: () => {
    set({
      isAuthenticated: false,
      user: null
    });
  }
}));
