/**
 * 问卷系统认证状态管理
 * 专门管理半匿名用户的认证状态
 */

import React from 'react';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

import type {
  QuestionnaireUser,
  QuestionnaireSession,
  QuestionnaireAuthResult,
  ABCredentials,
  QuestionnairePermission,
  QuestionnairePermissionResult
} from '../types/questionnaire-auth';

import { QUESTIONNAIRE_ROUTE_PERMISSIONS } from '../types/questionnaire-auth';
import { questionnaireAuthService } from '../services/questionnaireAuthService';

interface QuestionnaireAuthState {
  // 状态
  currentUser: QuestionnaireUser | null;
  currentSession: QuestionnaireSession | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;

  // 认证操作
  loginWithAB: (credentials: ABCredentials) => Promise<boolean>;
  logout: () => void;
  
  // 权限检查
  hasPermission: (permission: QuestionnairePermission) => boolean;
  canAccessRoute: (route: string) => QuestionnairePermissionResult;
  
  // 会话管理
  refreshSession: () => Promise<void>;
  checkSession: () => boolean;
  
  // 工具方法
  clearError: () => void;
  initializeAuth: () => void;
}

export const useQuestionnaireAuthStore = create<QuestionnaireAuthState>()(
  persist(
    (set, get) => ({
      // 初始状态
      currentUser: null,
      currentSession: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // A+B 登录
      loginWithAB: async (credentials: ABCredentials): Promise<boolean> => {
        set({ isLoading: true, error: null });

        try {
          const result: QuestionnaireAuthResult = await questionnaireAuthService.loginWithAB(credentials);

          if (result.success && result.user && result.session) {
            set({
              currentUser: result.user,
              currentSession: result.session,
              isAuthenticated: true,
              isLoading: false,
              error: null
            });

            console.log('问卷用户登录成功:', {
              userType: result.user.userType,
              uuid: result.user.uuid,
              display_name: result.user.display_name
            });

            return true;
          } else {
            set({
              isLoading: false,
              error: result.error || '登录失败'
            });
            return false;
          }
        } catch (error: any) {
          set({
            isLoading: false,
            error: error.message || '登录过程中发生错误'
          });
          return false;
        }
      },

      // 登出
      logout: (): void => {
        questionnaireAuthService.logout();
        set({
          currentUser: null,
          currentSession: null,
          isAuthenticated: false,
          isLoading: false,
          error: null
        });
        console.log('问卷用户已登出');
      },

      // 权限检查
      hasPermission: (permission: QuestionnairePermission): boolean => {
        const { currentUser } = get();
        if (!currentUser) return false;
        return currentUser.permissions.includes(permission);
      },

      // 路由访问检查
      canAccessRoute: (route: string): QuestionnairePermissionResult => {
        const { currentUser } = get();
        
        // 获取路由所需权限
        const requiredPermissions = QUESTIONNAIRE_ROUTE_PERMISSIONS[route];
        
        // 如果路由不需要权限，允许访问
        if (!requiredPermissions || requiredPermissions.length === 0) {
          return { hasPermission: true };
        }
        
        // 如果用户未登录
        if (!currentUser) {
          return {
            hasPermission: false,
            reason: '需要登录才能访问此页面',
            requiredPermissions
          };
        }
        
        // 检查是否具有任意一个所需权限
        const hasAccess = requiredPermissions.some(permission => 
          currentUser.permissions.includes(permission)
        );
        
        if (!hasAccess) {
          return {
            hasPermission: false,
            reason: '您没有访问此页面的权限',
            requiredPermissions
          };
        }
        
        return { hasPermission: true };
      },

      // 刷新会话
      refreshSession: async (): Promise<void> => {
        const currentUser = questionnaireAuthService.getCurrentUser();
        const currentSession = questionnaireAuthService.getCurrentSession();
        
        set({
          currentUser,
          currentSession,
          isAuthenticated: !!currentUser && !!currentSession && questionnaireAuthService.isSessionValid()
        });
      },

      // 检查会话有效性
      checkSession: (): boolean => {
        return questionnaireAuthService.isSessionValid();
      },

      // 清除错误
      clearError: (): void => {
        set({ error: null });
      },

      // 初始化认证状态
      initializeAuth: (): void => {
        const state = get();
        
        // 如果持久化状态显示已认证，但没有用户信息，尝试从服务层恢复
        if (state.isAuthenticated && !state.currentUser) {
          const currentUser = questionnaireAuthService.getCurrentUser();
          const currentSession = questionnaireAuthService.getCurrentSession();
          
          if (currentUser && currentSession && questionnaireAuthService.isSessionValid()) {
            set({
              currentUser,
              currentSession,
              isAuthenticated: true
            });
          } else {
            // 如果无法恢复或会话已过期，清除认证状态
            set({
              currentUser: null,
              currentSession: null,
              isAuthenticated: false
            });
            questionnaireAuthService.logout();
          }
        }
      }
    }),
    {
      name: 'questionnaire-auth-storage',
      partialize: (state) => ({
        // 只持久化认证状态，用户信息由服务层管理
        isAuthenticated: state.isAuthenticated
      })
    }
  )
);

// 导出便捷的hook
export const useQuestionnaireAuth = () => {
  const store = useQuestionnaireAuthStore();
  
  // 初始化时检查认证状态
  React.useEffect(() => {
    store.initializeAuth();
    store.refreshSession();
  }, []);
  
  return store;
};

// 导出权限检查hook
export const useQuestionnairePermission = (permission: QuestionnairePermission) => {
  const hasPermission = useQuestionnaireAuthStore(state => state.hasPermission);
  return hasPermission(permission);
};

// 导出路由保护hook
export const useQuestionnaireRouteAccess = (route: string) => {
  const canAccessRoute = useQuestionnaireAuthStore(state => state.canAccessRoute);
  return canAccessRoute(route);
};
