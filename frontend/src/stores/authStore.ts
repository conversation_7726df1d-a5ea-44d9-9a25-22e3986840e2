import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type {
  User,
  UserRole,
  Permission,
  LoginRequest,
  RegisterRequest,
  QuickRegisterRequest
} from '../types/auth';
import { ROLE_PERMISSIONS } from '../types/auth';
import { AuthService } from '../services/auth';
import { generateAnonymousId } from '../utils/permissions';

interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

interface AuthActions {
  login: (request: LoginRequest) => Promise<void>;
  logout: () => Promise<void>;
  register: (request: RegisterRequest) => Promise<void>;
  quickRegister: (request: QuickRegisterRequest) => Promise<void>;
  loadUserFromStorage: () => void;
  clearError: () => void;
  updateUser: (userData: Partial<User>) => void;
  checkPermission: (permission: Permission) => boolean;
  checkRole: (role: UserRole) => boolean;
  checkAuth: () => Promise<void>;
  refreshToken: () => Promise<void>;
}

type AuthStore = AuthState & AuthActions;

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // 初始状态
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // 登录
      login: async (request: LoginRequest) => {
        set({ isLoading: true, error: null });

        try {
          const response = await AuthService.login(request);
          const { user, token } = response.data;

          // 确保用户有正确的权限
          const userWithPermissions = {
            ...user,
            permissions: user.permissions || ROLE_PERMISSIONS[user.role] || []
          };

          // 保存到本地存储
          AuthService.saveAuthData(token, userWithPermissions);

          set({
            user: userWithPermissions,
            token,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } catch (error: any) {
          set({
            isLoading: false,
            error: error.response?.data?.message || '登录失败',
          });
          throw error;
        }
      },

      // 注册
      register: async (request: RegisterRequest) => {
        set({ isLoading: true, error: null });

        try {
          const response = await AuthService.register(request);
          const { user, token } = response.data;

          // 确保用户有正确的权限
          const userWithPermissions = {
            ...user,
            permissions: user.permissions || ROLE_PERMISSIONS[user.role] || []
          };

          // 保存到本地存储
          AuthService.saveAuthData(token, userWithPermissions);

          set({
            user: userWithPermissions,
            token,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } catch (error: any) {
          set({
            isLoading: false,
            error: error.response?.data?.message || '注册失败',
          });
          throw error;
        }
      },

      // 快捷注册（匿名用户）
      quickRegister: async (request: QuickRegisterRequest) => {
        set({ isLoading: true, error: null });

        try {
          const anonymousId = generateAnonymousId();
          const username = request.nickname || `匿名用户_${anonymousId.slice(-6)}`;

          // 创建匿名用户对象
          const anonymousUser: User = {
            id: anonymousId,
            username,
            role: 'user',
            permissions: ROLE_PERMISSIONS.user,
            isAnonymous: true,
            anonymousId,
            createdAt: new Date().toISOString(),
            status: 'active'
          };

          // 生成临时令牌
          const tempToken = `temp_${anonymousId}`;

          // 保存到本地存储
          AuthService.saveAuthData(tempToken, anonymousUser);

          set({
            user: anonymousUser,
            token: tempToken,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } catch (error: any) {
          set({
            isLoading: false,
            error: '快捷注册失败',
          });
          throw error;
        }
      },

      // 登出
      logout: async () => {
        set({ isLoading: true });
        
        try {
          await AuthService.logout();
        } catch (error) {
          console.error('Logout error:', error);
        } finally {
          // 清除状态
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          });
        }
      },

      // 从本地存储加载用户信息
      loadUserFromStorage: () => {
        const token = AuthService.getAuthToken();
        const user = AuthService.getUserInfo();
        
        if (token && user) {
          set({
            user,
            token,
            isAuthenticated: true,
          });
        }
      },

      // 清除错误
      clearError: () => {
        set({ error: null });
      },

      // 更新用户信息
      updateUser: (userData: Partial<User>) => {
        const { user } = get();
        if (user) {
          const updatedUser = { ...user, ...userData };
          set({ user: updatedUser });
          
          // 更新本地存储
          localStorage.setItem('user_info', JSON.stringify(updatedUser));
        }
      },

      // 检查权限
      checkPermission: (permission: Permission) => {
        const { user, isAuthenticated } = get();

        if (!isAuthenticated || !user) {
          return false;
        }

        // 检查用户权限列表
        if (user.permissions?.includes(permission)) {
          return true;
        }

        // 检查角色默认权限
        const rolePermissions = ROLE_PERMISSIONS[user.role] || [];
        return rolePermissions.includes(permission);
      },

      // 检查角色
      checkRole: (role: UserRole) => {
        const { user, isAuthenticated } = get();

        if (!isAuthenticated || !user) {
          return false;
        }

        return user.role === role;
      },

      // 检查认证状态
      checkAuth: async () => {
        const { token } = get();

        if (!token) {
          return;
        }

        set({ isLoading: true });

        try {
          // 验证token有效性
          const response = await AuthService.verifyToken();
          const user = response.data;

          // 确保用户有正确的权限
          const userWithPermissions = {
            ...user,
            permissions: user.permissions || ROLE_PERMISSIONS[user.role] || []
          };

          set({
            user: userWithPermissions,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } catch (error) {
          // Token无效，清除认证状态
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          });

          // 清除本地存储
          AuthService.clearAuthData();
        }
      },

      // 刷新令牌
      refreshToken: async () => {
        const { token } = get();

        if (!token) {
          throw new Error('No token available');
        }

        try {
          const response = await AuthService.refreshToken();
          const { token: newToken, user } = response.data;

          // 确保用户有正确的权限
          const userWithPermissions = {
            ...user,
            permissions: user.permissions || ROLE_PERMISSIONS[user.role] || []
          };

          // 更新存储
          AuthService.saveAuthData(newToken, userWithPermissions);

          set({
            user: userWithPermissions,
            token: newToken,
            isAuthenticated: true,
            error: null,
          });
        } catch (error) {
          // 刷新失败，清除认证状态
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            error: null,
          });

          AuthService.clearAuthData();
          throw error;
        }
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);
