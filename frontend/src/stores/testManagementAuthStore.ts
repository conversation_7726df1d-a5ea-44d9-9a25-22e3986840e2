/**
 * 测试版本的管理认证Store
 * 用于验证Zustand是否能正常工作
 */

import { create } from 'zustand';
import type { ManagementUser, ManagementSession, ManagementCredentials } from '../types/management-auth';
import { managementAuthService } from '../services/managementAuthService';

interface TestManagementAuthState {
  // 基础状态
  currentUser: ManagementUser | null;
  currentSession: ManagementSession | null;
  authToken: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;

  // 操作方法
  login: (credentials: ManagementCredentials) => Promise<boolean>;
  logout: () => void;
  
  // 权限检查
  isAdmin: () => boolean;
  isReviewer: () => boolean;
}

// 创建简化版本的Store，不使用持久化
export const useTestManagementAuthStore = create<TestManagementAuthState>((set, get) => ({
  // 初始状态
  currentUser: null,
  currentSession: null,
  authToken: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,

  // 简化的登录方法
  login: async (credentials: ManagementCredentials): Promise<boolean> => {
    console.log('🧪 测试Store登录开始');
    
    // 设置加载状态
    set({ isLoading: true, error: null });
    console.log('🧪 设置加载状态完成');

    try {
      // 调用服务层
      const result = await managementAuthService.login(credentials);
      console.log('🧪 服务层调用完成:', result.success);

      if (result.success) {
        // 从服务层获取状态
        const currentUser = managementAuthService.getCurrentUser();
        const currentSession = managementAuthService.getCurrentSession();
        const authToken = managementAuthService.getAuthToken();
        
        console.log('🧪 从服务层获取状态:', {
          hasUser: !!currentUser,
          hasSession: !!currentSession,
          hasToken: !!authToken
        });

        if (currentUser && currentSession) {
          // 直接设置状态
          const newState = {
            currentUser,
            currentSession,
            authToken,
            isAuthenticated: true,
            isLoading: false,
            error: null
          };
          
          console.log('🧪 准备设置新状态:', newState);
          set(newState);
          
          // 立即验证状态
          const afterState = get();
          console.log('🧪 设置后状态验证:', {
            isAuthenticated: afterState.isAuthenticated,
            hasUser: !!afterState.currentUser,
            userType: afterState.currentUser?.userType
          });
          
          return true;
        }
      }
      
      set({ isLoading: false, error: '登录失败' });
      return false;
      
    } catch (error) {
      console.error('🧪 测试Store登录错误:', error);
      set({ isLoading: false, error: '登录异常' });
      return false;
    }
  },

  // 登出方法
  logout: () => {
    managementAuthService.logout();
    set({
      currentUser: null,
      currentSession: null,
      authToken: null,
      isAuthenticated: false,
      isLoading: false,
      error: null
    });
  },

  // 权限检查方法
  isAdmin: () => {
    const state = get();
    return state.currentUser?.userType === 'ADMIN' || state.currentUser?.userType === 'SUPER_ADMIN';
  },

  isReviewer: () => {
    const state = get();
    return state.currentUser?.userType === 'REVIEWER' || 
           state.currentUser?.userType === 'ADMIN' || 
           state.currentUser?.userType === 'SUPER_ADMIN';
  }
}));
