@tailwind base;
@tailwind components;
@tailwind utilities;

/* ===== 全局色彩方案：白底、黑字、灰色块 ===== */

/* CSS 变量定义 */
:root {
  /* 主色调 */
  --color-white: #ffffff;
  --color-black: #000000;
  --color-text-primary: #000000;
  --color-text-secondary: #666666;
  --color-text-tertiary: #999999;

  /* 背景色 */
  --color-bg-primary: #ffffff;
  --color-bg-secondary: #f8f9fa;
  --color-bg-tertiary: #f0f0f0;

  /* 灰色块 */
  --color-gray-50: #fafafa;
  --color-gray-100: #f5f5f5;
  --color-gray-200: #eeeeee;
  --color-gray-300: #e0e0e0;
  --color-gray-400: #bdbdbd;
  --color-gray-500: #9e9e9e;
  --color-gray-600: #757575;
  --color-gray-700: #616161;
  --color-gray-800: #424242;
  --color-gray-900: #212121;

  /* 边框色 */
  --color-border-light: #f0f0f0;
  --color-border-base: #e8e8e8;
  --color-border-dark: #d9d9d9;

  /* 阴影 */
  --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.05);
  --shadow-base: 0 2px 8px rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.1);
  --shadow-heavy: 0 8px 24px rgba(0, 0, 0, 0.12);

  /* 圆角 */
  --border-radius-sm: 4px;
  --border-radius-base: 6px;
  --border-radius-lg: 8px;
}

/* 全局样式重置 */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  line-height: 1.6;
  color: var(--color-text-primary);
  background-color: var(--color-bg-secondary);
  font-size: 14px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  min-height: 100vh;
  background-color: var(--color-bg-secondary);
}

/* ===== Ant Design 组件样式统一覆盖 ===== */

/* 布局组件 */
.ant-layout {
  min-height: 100vh;
  background: var(--color-bg-secondary) !important;
}

.ant-layout-header {
  background: var(--color-bg-primary) !important;
  border-bottom: 1px solid var(--color-border-base) !important;
  color: var(--color-text-primary) !important;
}

.ant-layout-content {
  padding: 24px;
  background: var(--color-bg-secondary) !important;
  color: var(--color-text-primary) !important;
}

.ant-layout-sider {
  background: var(--color-bg-primary) !important;
}

.ant-layout-footer {
  background: var(--color-gray-50) !important;
  border-top: 1px solid var(--color-border-base) !important;
  color: var(--color-text-secondary) !important;
}

/* 卡片组件 */
.ant-card {
  background: var(--color-bg-primary) !important;
  border: 1px solid var(--color-border-base) !important;
  border-radius: var(--border-radius-lg) !important;
  box-shadow: var(--shadow-light) !important;
  color: var(--color-text-primary) !important;
}

.ant-card:hover {
  border-color: var(--color-border-dark) !important;
  box-shadow: var(--shadow-base) !important;
}

.ant-card-head {
  background: var(--color-bg-primary) !important;
  border-bottom: 1px solid var(--color-border-light) !important;
  color: var(--color-text-primary) !important;
}

.ant-card-body {
  background: var(--color-bg-primary) !important;
  color: var(--color-text-primary) !important;
}

/* 按钮组件 */
.ant-btn {
  border-radius: var(--border-radius-sm) !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
}

.ant-btn-default {
  background: var(--color-bg-primary) !important;
  border-color: var(--color-border-base) !important;
  color: var(--color-text-primary) !important;
}

.ant-btn-default:hover {
  background: var(--color-gray-50) !important;
  border-color: var(--color-border-dark) !important;
  color: var(--color-text-primary) !important;
}

.ant-btn-primary {
  background: var(--color-black) !important;
  border-color: var(--color-black) !important;
  color: var(--color-white) !important;
}

.ant-btn-primary:hover {
  background: var(--color-gray-800) !important;
  border-color: var(--color-gray-800) !important;
  color: var(--color-white) !important;
}

/* 自定义组件样式 */
.questionnaire-form {
  max-width: 800px;
  margin: 0 auto;
  padding: 24px;
  background: var(--color-bg-primary);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--color-border-base);
  box-shadow: var(--shadow-light);
}

.data-chart {
  padding: 16px;
  background: var(--color-bg-primary);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--color-border-base);
  box-shadow: var(--shadow-light);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ant-layout-content {
    padding: 16px;
  }

  .questionnaire-form {
    padding: 16px;
    margin: 0 8px;
  }
}
