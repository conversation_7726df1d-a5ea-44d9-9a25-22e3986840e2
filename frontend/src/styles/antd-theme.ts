import type { ThemeConfig } from 'antd';

// 白底、黑字、灰色块主题配置
export const antdTheme: ThemeConfig = {
  token: {
    // 主色调
    colorPrimary: '#000000',
    colorSuccess: '#52c41a',
    colorWarning: '#faad14',
    colorError: '#ff4d4f',
    colorInfo: '#1890ff',
    
    // 文本颜色
    colorText: '#000000',
    colorTextSecondary: '#666666',
    colorTextTertiary: '#999999',
    colorTextQuaternary: '#cccccc',
    
    // 背景颜色
    colorBgBase: '#ffffff',
    colorBgContainer: '#ffffff',
    colorBgElevated: '#ffffff',
    colorBgLayout: '#f8f9fa',
    colorBgSpotlight: '#ffffff',
    colorBgMask: 'rgba(0, 0, 0, 0.45)',
    
    // 边框颜色
    colorBorder: '#e8e8e8',
    colorBorderSecondary: '#f0f0f0',
    
    // 填充颜色
    colorFill: '#f5f5f5',
    colorFillSecondary: '#f0f0f0',
    colorFillTertiary: '#fafafa',
    colorFillQuaternary: '#ffffff',
    
    // 字体
    fontFamily: `-apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif`,
    fontSize: 14,
    fontSizeHeading1: 32,
    fontSizeHeading2: 24,
    fontSizeHeading3: 20,
    fontSizeHeading4: 18,
    fontSizeHeading5: 16,
    fontSizeLG: 16,
    fontSizeSM: 12,
    fontSizeXL: 20,
    
    // 行高
    lineHeight: 1.5,
    lineHeightHeading1: 1.2,
    lineHeightHeading2: 1.3,
    lineHeightHeading3: 1.4,
    lineHeightHeading4: 1.4,
    lineHeightHeading5: 1.5,
    lineHeightLG: 1.5,
    lineHeightSM: 1.5,
    
    // 圆角
    borderRadius: 6,
    borderRadiusLG: 8,
    borderRadiusSM: 4,
    borderRadiusXS: 2,
    
    // 控件高度
    controlHeight: 32,
    controlHeightLG: 40,
    controlHeightSM: 24,
    controlHeightXS: 16,
    
    // 间距
    padding: 16,
    paddingLG: 24,
    paddingSM: 12,
    paddingXS: 8,
    paddingXXS: 4,
    
    margin: 16,
    marginLG: 24,
    marginSM: 12,
    marginXS: 8,
    marginXXS: 4,
    
    // 阴影
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
    boxShadowSecondary: '0 1px 3px rgba(0, 0, 0, 0.05)',
    boxShadowTertiary: '0 4px 12px rgba(0, 0, 0, 0.1)',
    
    // 动画
    motionDurationFast: '0.1s',
    motionDurationMid: '0.2s',
    motionDurationSlow: '0.3s',
    motionEaseInOut: 'cubic-bezier(0.645, 0.045, 0.355, 1)',
    motionEaseOut: 'cubic-bezier(0.215, 0.61, 0.355, 1)',
    motionEaseIn: 'cubic-bezier(0.55, 0.055, 0.675, 0.19)',
    
    // 线条宽度
    lineWidth: 1,
    lineWidthBold: 2,
    
    // 透明度
    opacityLoading: 0.65,
  },
  
  components: {
    // 按钮组件
    Button: {
      primaryColor: '#ffffff',
      primaryShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
      defaultColor: '#000000',
      defaultBg: '#ffffff',
      defaultBorderColor: '#e8e8e8',
      defaultShadow: '0 1px 3px rgba(0, 0, 0, 0.05)',
      ghostColor: '#000000',
      ghostBg: 'transparent',
      ghostBorderColor: '#000000',
      linkColor: '#000000',
      textColor: '#666666',
      textBg: 'transparent',
      borderRadius: 4,
      controlHeight: 32,
      controlHeightLG: 40,
      controlHeightSM: 24,
    },
    
    // 输入框组件
    Input: {
      colorBorder: '#e8e8e8',
      colorBorderHover: '#d9d9d9',
      colorPrimaryHover: '#000000',
      borderRadius: 4,
      controlHeight: 32,
      controlHeightLG: 40,
      controlHeightSM: 24,
    },
    
    // 卡片组件
    Card: {
      colorBorderSecondary: '#e8e8e8',
      colorBgContainer: '#ffffff',
      borderRadiusLG: 8,
      boxShadowTertiary: '0 2px 8px rgba(0, 0, 0, 0.06)',
    },
    
    // 表格组件
    Table: {
      colorBgContainer: '#ffffff',
      colorFillAlter: '#fafafa',
      colorBorderSecondary: '#f0f0f0',
      borderRadius: 8,
    },
    
    // 菜单组件
    Menu: {
      colorBgContainer: '#ffffff',
      colorItemText: '#666666',
      colorItemTextSelected: '#000000',
      colorItemTextHover: '#000000',
      colorItemBg: '#ffffff',
      colorItemBgSelected: '#f0f0f0',
      colorItemBgHover: '#f5f5f5',
      borderRadius: 4,
    },
    
    // 分页组件
    Pagination: {
      colorBgContainer: '#ffffff',
      colorBorder: '#e8e8e8',
      colorPrimary: '#000000',
      borderRadius: 4,
    },
    
    // 标签组件
    Tag: {
      colorBgContainer: '#f5f5f5',
      colorBorder: '#e8e8e8',
      colorText: '#000000',
      borderRadiusSM: 4,
    },
    
    // 统计组件
    Statistic: {
      colorText: '#000000',
      colorTextDescription: '#666666',
      fontSizeHeading3: 24,
      fontWeightStrong: 600,
    },
    
    // 布局组件
    Layout: {
      colorBgBody: '#f8f9fa',
      colorBgHeader: '#ffffff',
      colorBgTrigger: '#ffffff',
      colorText: '#000000',
    },
    
    // 表单组件
    Form: {
      labelColor: '#000000',
      labelFontSize: 14,
      labelHeight: 32,
      verticalLabelPadding: '0 0 8px',
    },
    
    // 选择器组件
    Select: {
      colorBorder: '#e8e8e8',
      colorBorderHover: '#d9d9d9',
      colorPrimaryHover: '#000000',
      borderRadius: 4,
      controlHeight: 32,
      controlHeightLG: 40,
      controlHeightSM: 24,
    },
    
    // 日期选择器
    DatePicker: {
      colorBorder: '#e8e8e8',
      colorBorderHover: '#d9d9d9',
      colorPrimaryHover: '#000000',
      borderRadius: 4,
      controlHeight: 32,
      controlHeightLG: 40,
      controlHeightSM: 24,
    },
    
    // 步骤条
    Steps: {
      colorPrimary: '#000000',
      colorText: '#000000',
      colorTextDescription: '#666666',
      colorSplit: '#e8e8e8',
    },
    
    // 结果页
    Result: {
      colorText: '#000000',
      colorTextDescription: '#666666',
      titleFontSize: 24,
      subtitleFontSize: 16,
    },
  },
};

export default antdTheme;
