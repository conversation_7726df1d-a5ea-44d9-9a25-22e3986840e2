/* ===== 全局主题配置：白底、黑字、灰色块 ===== */

:root {
  /* ===== 主色调 ===== */
  --theme-primary: #000000;
  --theme-secondary: #666666;
  --theme-tertiary: #999999;
  --theme-white: #ffffff;
  --theme-black: #000000;

  /* ===== 文本颜色 ===== */
  --text-primary: #000000;
  --text-secondary: #666666;
  --text-tertiary: #999999;
  --text-disabled: #cccccc;
  --text-inverse: #ffffff;

  /* ===== 背景颜色 ===== */
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-tertiary: #f5f5f5;
  --bg-quaternary: #f0f0f0;
  --bg-dark: #000000;

  /* ===== 灰色系统 ===== */
  --gray-50: #fafafa;
  --gray-100: #f5f5f5;
  --gray-200: #eeeeee;
  --gray-300: #e0e0e0;
  --gray-400: #bdbdbd;
  --gray-500: #9e9e9e;
  --gray-600: #757575;
  --gray-700: #616161;
  --gray-800: #424242;
  --gray-900: #212121;

  /* ===== 边框颜色 ===== */
  --border-light: #f0f0f0;
  --border-base: #e8e8e8;
  --border-dark: #d9d9d9;
  --border-darker: #bfbfbf;

  /* ===== 阴影系统 ===== */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.04);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.05);
  --shadow-base: 0 2px 8px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.12);
  --shadow-xl: 0 12px 32px rgba(0, 0, 0, 0.15);

  /* ===== 圆角系统 ===== */
  --radius-xs: 2px;
  --radius-sm: 4px;
  --radius-base: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-full: 50%;

  /* ===== 间距系统 ===== */
  --space-xs: 4px;
  --space-sm: 8px;
  --space-base: 12px;
  --space-md: 16px;
  --space-lg: 24px;
  --space-xl: 32px;
  --space-2xl: 48px;
  --space-3xl: 64px;

  /* ===== 字体系统 ===== */
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-base: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-2xl: 24px;
  --font-size-3xl: 32px;
  --font-size-4xl: 40px;

  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  --line-height-tight: 1.2;
  --line-height-base: 1.5;
  --line-height-relaxed: 1.6;
  --line-height-loose: 1.8;

  /* ===== 过渡动画 ===== */
  --transition-fast: 0.15s ease;
  --transition-base: 0.3s ease;
  --transition-slow: 0.5s ease;

  /* ===== 层级系统 ===== */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* ===== 全局样式重置 ===== */
* {
  box-sizing: border-box;
}

html {
  font-size: 14px;
  line-height: var(--line-height-base);
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 
               'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', 
               Helvetica, Arial, sans-serif;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-base);
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ===== 通用工具类 ===== */
.theme-bg-primary { background-color: var(--bg-primary) !important; }
.theme-bg-secondary { background-color: var(--bg-secondary) !important; }
.theme-bg-tertiary { background-color: var(--bg-tertiary) !important; }
.theme-bg-dark { background-color: var(--bg-dark) !important; }

.theme-text-primary { color: var(--text-primary) !important; }
.theme-text-secondary { color: var(--text-secondary) !important; }
.theme-text-tertiary { color: var(--text-tertiary) !important; }
.theme-text-inverse { color: var(--text-inverse) !important; }

.theme-border-light { border-color: var(--border-light) !important; }
.theme-border-base { border-color: var(--border-base) !important; }
.theme-border-dark { border-color: var(--border-dark) !important; }

.theme-shadow-sm { box-shadow: var(--shadow-sm) !important; }
.theme-shadow-base { box-shadow: var(--shadow-base) !important; }
.theme-shadow-md { box-shadow: var(--shadow-md) !important; }
.theme-shadow-lg { box-shadow: var(--shadow-lg) !important; }

.theme-radius-sm { border-radius: var(--radius-sm) !important; }
.theme-radius-base { border-radius: var(--radius-base) !important; }
.theme-radius-md { border-radius: var(--radius-md) !important; }
.theme-radius-lg { border-radius: var(--radius-lg) !important; }

/* ===== 响应式断点 ===== */
@media (max-width: 576px) {
  :root {
    --font-size-base: 14px;
    --space-lg: 16px;
    --space-xl: 24px;
  }
}

@media (max-width: 768px) {
  :root {
    --space-2xl: 32px;
    --space-3xl: 48px;
  }
}

/* ===== 打印样式 ===== */
@media print {
  * {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
  }
}
