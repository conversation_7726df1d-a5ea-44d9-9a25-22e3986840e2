/**
 * 首页主要内容区域样式
 * 复现原项目的视觉设计和布局风格
 */

.heroSection {
  min-height: calc(100vh - 120px);
  padding: 60px 24px 40px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
}

/* 简化的标题区域 */
.titleSection {
  text-align: center;
  max-width: 600px;
  margin-bottom: 40px;
}

.mainTitle {
  font-size: 2.5rem !important;
  font-weight: 700 !important;
  color: #1a1a1a !important;
  margin-bottom: 24px !important;
  line-height: 1.2 !important;
  letter-spacing: -0.02em;
}

.description {
  font-size: 1.1rem !important;
  color: #666 !important;
  line-height: 1.8 !important;
  margin-bottom: 0 !important;
  max-width: 700px;
  margin: 0 auto;
}

/* 核心功能按钮区域 */
.actionSection {
  margin: 40px 0 50px;
  text-align: center;
}

.primaryAction {
  background: #1890ff !important;
  border-color: #1890ff !important;
  color: #fff !important;
  font-weight: 600 !important;
  height: 48px !important;
  padding: 0 32px !important;
  font-size: 16px !important;
  border-radius: 6px !important;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3) !important;
  transition: all 0.3s ease !important;
}

.primaryAction:hover {
  background: #40a9ff !important;
  border-color: #40a9ff !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(24, 144, 255, 0.4) !important;
}

.secondaryAction {
  background: #fff !important;
  border: 2px solid #333 !important;
  color: #333 !important;
  font-weight: 600 !important;
  height: 48px !important;
  padding: 0 24px !important;
  font-size: 16px !important;
  border-radius: 6px !important;
  transition: all 0.3s ease !important;
}

.secondaryAction:hover {
  background: #333 !important;
  color: #fff !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.adminAction {
  background: #f0f9ff !important;
  border: 2px solid #1890ff !important;
  color: #1890ff !important;
  font-weight: 600 !important;
  height: 48px !important;
  padding: 0 24px !important;
  font-size: 16px !important;
  border-radius: 6px !important;
  transition: all 0.3s ease !important;
}

.adminAction:hover {
  background: #1890ff !important;
  color: #fff !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3) !important;
}

/* 功能模块卡片区域 */
.featuresSection {
  width: 100%;
  max-width: 1200px;
}

.featureCard {
  height: 100%;
  border-radius: 12px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
  transition: all 0.3s ease !important;
  border: none !important;
  overflow: hidden;
}

.featureCard:hover {
  transform: translateY(-8px) !important;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15) !important;
}

.cardContent {
  padding: 32px 24px;
  text-align: center;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.cardIcon {
  font-size: 3rem;
  color: #333;
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.cardTitle {
  color: #1a1a1a !important;
  font-weight: 600 !important;
  margin-bottom: 16px !important;
  font-size: 1.3rem !important;
}

.cardDescription {
  color: #666 !important;
  line-height: 1.6 !important;
  margin-bottom: 24px !important;
  flex: 1;
  display: flex;
  align-items: center;
}

.cardLink {
  color: #333 !important;
  font-weight: 600 !important;
  text-decoration: none !important;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease !important;
  padding: 8px 16px;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
}

.cardLink:hover {
  color: #fff !important;
  background: #333 !important;
  border-color: #333 !important;
  transform: translateX(4px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .heroSection {
    padding: 40px 16px;
  }
  
  .mainTitle {
    font-size: 2rem !important;
  }
  
  .description {
    font-size: 1rem !important;
  }
  
  .actionSection {
    margin-bottom: 60px;
  }
  
  .primaryAction,
  .secondaryAction,
  .adminAction {
    width: 100%;
    margin-bottom: 12px;
  }
  
  .cardContent {
    padding: 24px 20px;
  }
}

@media (max-width: 480px) {
  .mainTitle {
    font-size: 1.8rem !important;
  }
  
  .cardIcon {
    font-size: 2.5rem;
  }
}
