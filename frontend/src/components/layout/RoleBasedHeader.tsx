import React from 'react';
import { Button, Typography, Space, Layout, Menu, Avatar, Dropdown } from 'antd';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import {
  UserOutlined,
  LoginOutlined,
  LogoutOutlined,
  SettingOutlined,
  DashboardOutlined,
  FileTextOutlined,
  Bar<PERSON>hartOutlined,
  TeamOutlined,
  MessageOutlined,
  BookOutlined,
  DatabaseOutlined,
  ThunderboltOutlined
} from '@ant-design/icons';
import { useAuthStore } from '../../stores/authStore';
import { useManagementAuthStore } from '../../stores/managementAuthStore';
import type { UserRole } from '../../types/auth';
import { getAccessibleMenuItems, getRoleDisplayName } from '../../utils/permissions';
import styles from './RoleBasedHeader.module.css';

const { Title } = Typography;
const { Header } = Layout;

interface RoleBasedHeaderProps {
  role?: UserRole;
  hideNavigation?: boolean;
}

export const RoleBasedHeader: React.FC<RoleBasedHeaderProps> = ({
  role,
  hideNavigation = false
}) => {
  // 根据角色选择合适的认证系统
  const authStore = useAuthStore();
  const managementAuthStore = useManagementAuthStore();

  const isManagementRole = role === 'admin' || role === 'reviewer';

  // 根据角色类型获取对应的认证状态
  const { isAuthenticated, logout } = isManagementRole ? managementAuthStore : authStore;
  const user = isManagementRole ? managementAuthStore.currentUser : authStore.user;



  const location = useLocation();
  const navigate = useNavigate();

  const handleLogout = async () => {
    await logout();
    navigate('/');
  };

  // 根据角色获取菜单项
  const getMenuItems = () => {
    if (hideNavigation) return [];

    // 如果指定了角色，使用角色专用菜单
    if (role === 'reviewer') {
      return [
        {
          key: 'dashboard',
          label: <Link to="/reviewer/dashboard">审核工作台</Link>,
          icon: <DashboardOutlined />
        },
        {
          key: 'questionnaires',
          label: <Link to="/reviewer/questionnaires">问卷审核</Link>,
          icon: <FileTextOutlined />
        },
        {
          key: 'stories',
          label: <Link to="/reviewer/stories">故事审核</Link>,
          icon: <BookOutlined />
        },
        {
          key: 'voices',
          label: <Link to="/reviewer/voices">心声审核</Link>,
          icon: <MessageOutlined />
        },
        {
          key: 'history',
          label: <Link to="/reviewer/history">审核历史</Link>,
          icon: <BarChartOutlined />
        }
      ];
    }

    if (role === 'admin') {
      return [
        {
          key: 'dashboard',
          label: <Link to="/admin">管理控制台</Link>,
          icon: <DashboardOutlined />
        },
        {
          key: 'users',
          label: <Link to="/admin/users">用户管理</Link>,
          icon: <UserOutlined />
        },
        {
          key: 'reviewers',
          label: <Link to="/admin/reviewers">审核员管理</Link>,
          icon: <TeamOutlined />
        },
        {
          key: 'content',
          label: <Link to="/admin/content">内容管理</Link>,
          icon: <FileTextOutlined />
        },
        {
          key: 'system',
          label: <Link to="/admin/system">系统设置</Link>,
          icon: <SettingOutlined />
        },
        {
          key: 'api-data',
          label: <Link to="/admin/api-data">API与数据</Link>,
          icon: <BarChartOutlined />
        },
        {
          key: 'data-generator',
          label: <Link to="/admin/data-generator">数据生成器</Link>,
          icon: <DatabaseOutlined />
        },
        {
          key: 'ai-management',
          label: 'AI管理',
          icon: <ThunderboltOutlined />,
          children: [
            {
              key: 'ai-sources',
              label: <Link to="/admin/ai/sources">AI水源配置</Link>
            },
            {
              key: 'ai-monitor',
              label: <Link to="/admin/ai/monitor">监控面板</Link>
            },
            {
              key: 'ai-cost',
              label: <Link to="/admin/ai/cost">成本控制</Link>
            },
            {
              key: 'ai-review-assistant',
              label: <Link to="/admin/ai/review-assistant">审核助手</Link>
            }
          ]
        }
      ];
    }

    // 普通用户菜单
    const accessibleItems = getAccessibleMenuItems(user);
    return accessibleItems.map(item => ({
      key: item.key,
      label: <Link to={item.path}>{item.label}</Link>
    }));
  };

  // 获取当前选中的菜单项
  const getSelectedKey = () => {
    const path = location.pathname;
    
    if (role === 'reviewer') {
      if (path.includes('/questionnaires')) return 'questionnaires';
      if (path.includes('/stories')) return 'stories';
      if (path.includes('/voices')) return 'voices';
      if (path.includes('/history')) return 'history';
      return 'dashboard';
    }
    
    if (role === 'admin') {
      if (path.includes('/users')) return 'users';
      if (path.includes('/reviewers')) return 'reviewers';
      if (path.includes('/content')) return 'content';
      if (path.includes('/system')) return 'system';
      if (path.includes('/api-data')) return 'api-data';
      if (path.includes('/data-generator')) return 'data-generator';
      return 'dashboard';
    }
    
    // 普通用户路径匹配
    if (path === '/') return 'home';
    if (path.startsWith('/questionnaire')) return 'questionnaire';
    if (path.startsWith('/analytics')) return 'analytics';
    if (path.startsWith('/stories')) return 'stories';
    if (path.startsWith('/voices')) return 'voices';
    if (path.startsWith('/profile')) return 'profile';
    
    return '';
  };

  // 用户下拉菜单
  const userMenu = (
    <Menu>
      {user && (
        <>
          <Menu.Item key="role" disabled>
            <Space>
              <UserOutlined />
              {isManagementRole
                ? (user as any).userType || (user as any).display_name || user.username
                : getRoleDisplayName(user.role)
              }
            </Space>
          </Menu.Item>
          <Menu.Divider />
          {!isManagementRole && user.role === 'user' && (
            <Menu.Item key="profile">
              <Link to="/profile">
                <Space>
                  <UserOutlined />
                  个人中心
                </Space>
              </Link>
            </Menu.Item>
          )}
          <Menu.Item key="logout" onClick={handleLogout}>
            <Space>
              <LogoutOutlined />
              退出登录
            </Space>
          </Menu.Item>
        </>
      )}
    </Menu>
  );

  // 获取页面标题
  const getPageTitle = () => {
    if (role === 'reviewer') return '审核员工作台';
    if (role === 'admin') return '管理员控制台';
    return '大学生就业问卷调查';
  };

  return (
    <Header className={styles.header}>
      <div className={styles.headerContent}>
        {/* Logo和标题 */}
        <div className={styles.logoSection}>
          <Link to={role ? (role === 'reviewer' ? '/reviewer' : '/admin') : '/'}>
            <Title level={4} className={styles.logo}>
              {getPageTitle()}
            </Title>
          </Link>
        </div>
        
        {/* 导航菜单 */}
        {!hideNavigation && (
          <Menu
            mode="horizontal"
            selectedKeys={[getSelectedKey()]}
            className={styles.menu}
            items={getMenuItems()}
          />
        )}
        
        {/* 用户操作区域 */}
        <Space className={styles.userActions}>
          {isAuthenticated && user && (
            <Dropdown overlay={userMenu} placement="bottomRight">
              <Button type="text" className={styles.userButton}>
                <Space>
                  <Avatar
                    size="small"
                    icon={<UserOutlined />}
                    src={user.avatar}
                  />
                  <span className={styles.username}>
                    {isManagementRole
                      ? (user as any).display_name || user.username
                      : (user.isAnonymous ? '匿名用户' : user.username)
                    }
                  </span>
                </Space>
              </Button>
            </Dropdown>
          )}
        </Space>
      </div>
    </Header>
  );
};
