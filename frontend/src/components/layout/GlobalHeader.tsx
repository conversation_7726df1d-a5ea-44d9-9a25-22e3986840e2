import React from 'react';
import { Button, Typography, Space, Layout, Menu } from 'antd';
import { Link, useLocation } from 'react-router-dom';
import {
  SettingOutlined,
  UserOutlined,
  LogoutOutlined
} from '@ant-design/icons';
import { useUniversalAuthStore } from '../../stores/universalAuthStore';
import { useManagementAuthStore } from '../../stores/managementAuthStore';
import { GlobalStateIndicator } from '../common/GlobalStateIndicator';
import styles from './GlobalHeader.module.css';

const { Title } = Typography;
const { Header } = Layout;

export const GlobalHeader: React.FC = () => {
  const { isAuthenticated: universalIsAuthenticated, currentUser: universalUser, logout: universalLogout } = useUniversalAuthStore();
  const { isAuthenticated: managementIsAuthenticated, currentUser: managementUser, logout: managementLogout } = useManagementAuthStore();
  const location = useLocation();

  // 根据当前路径确定使用哪个认证系统
  const isAdminPath = location.pathname.startsWith('/admin') || location.pathname.startsWith('/reviewer');
  const isAuthenticated = isAdminPath ? managementIsAuthenticated : universalIsAuthenticated;
  const currentUser = isAdminPath ? managementUser : universalUser;
  const logout = isAdminPath ? managementLogout : universalLogout;

  // 根据当前路径确定选中的菜单项
  const getSelectedKey = () => {
    const path = location.pathname;
    if (path === '/') return 'home';
    if (path.startsWith('/questionnaire')) return 'survey';
    if (path.startsWith('/analytics')) return 'analysis';
    if (path.startsWith('/results')) return 'results';
    if (path.startsWith('/stories')) return 'story';
    if (path.startsWith('/voices')) return 'about';
    if (path.startsWith('/admin-test')) return 'admin-test';
    if (path.startsWith('/reviewer-test')) return 'reviewer-test';
    if (path.startsWith('/admin')) return 'admin';
    return '';
  };

  const handleLogout = () => {
    logout();
  };

  const menuItems = [
    {
      key: 'home',
      label: <Link to="/" style={{ color: 'inherit', textDecoration: 'none' }}>首页</Link>
    },
    {
      key: 'survey',
      label: <Link to="/questionnaire" style={{ color: 'inherit', textDecoration: 'none' }}>问卷调查</Link>
    },
    {
      key: 'analysis',
      label: <Link to="/analytics" style={{ color: 'inherit', textDecoration: 'none' }}>数据可视化</Link>
    },
    {
      key: 'results',
      label: <Link to="/results" style={{ color: 'inherit', textDecoration: 'none' }}>结果分析</Link>
    },
    {
      key: 'story',
      label: <Link to="/stories" style={{ color: 'inherit', textDecoration: 'none' }}>故事墙</Link>
    },
    {
      key: 'about',
      label: <Link to="/voices" style={{ color: 'inherit', textDecoration: 'none' }}>问卷心声</Link>
    },
    {
      key: 'test-item',
      label: '测试菜单'
    },

  ];

  // 保留原有的条件性管理后台菜单项（如果已登录）
  if (isAuthenticated && currentUser && (currentUser.userType === 'admin' || currentUser.userType === 'super_admin')) {
    menuItems.push({
      key: 'admin',
      label: <Link to="/admin" style={{ color: 'inherit', textDecoration: 'none' }}>管理后台</Link>
    });
  }

  return (
    <Header className={styles.header}>
      <div className={styles.headerContent}>
        <Link to="/" style={{ textDecoration: 'none' }}>
          <Title level={3} className={styles.logo}>
            大学生就业问卷调查
          </Title>
        </Link>
        
        <Menu
          mode="horizontal"
          selectedKeys={[getSelectedKey()]}
          className={styles.menu}
          items={menuItems}
        />
        
        <Space className={styles.userActions}>
          {/* 版本号指示器 */}
          <span style={{
            fontSize: '12px',
            color: '#666',
            backgroundColor: '#f0f0f0',
            padding: '2px 6px',
            borderRadius: '3px',
            fontFamily: 'monospace'
          }}>
            v1.0.2
          </span>
          {/* 全局状态指示器 */}
          <GlobalStateIndicator />
        </Space>
      </div>
    </Header>
  );
};
