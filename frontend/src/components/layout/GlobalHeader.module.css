.header {
  background: #fff !important;
  border-bottom: 1px solid #e8e8e8 !important;
  padding: 0 50px !important;
  height: 64px !important;
  line-height: 64px !important;
  position: sticky !important;
  top: 0 !important;
  z-index: 1000 !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
}

.headerContent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

.logo {
  margin: 0 !important;
  color: #000000 !important;
  font-weight: 600 !important;
  font-size: 18px !important;
  line-height: 1.2 !important;
  cursor: pointer;
  transition: color 0.3s ease;
}

.logo:hover {
  color: #666666 !important;
}

.menu {
  border: none !important;
  background: transparent !important;
  flex: 1;
  justify-content: center;
  display: flex;
}

.menu .ant-menu-item {
  color: #666666 !important;
  font-weight: 500;
  margin: 0 8px;
  padding: 0 16px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.menu .ant-menu-item:hover {
  color: #000000 !important;
  background: #f5f5f5 !important;
}

.menu .ant-menu-item-selected {
  color: #000000 !important;
  background: #f0f0f0 !important;
  font-weight: 600;
}

.menu .ant-menu-item-selected::after {
  display: none !important;
}

.userActions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.welcomeText {
  color: #666;
  font-size: 14px;
  white-space: nowrap;
}

.loginButton,
.adminButton,
.logoutButton {
  background: #fff !important;
  color: #333 !important;
  border: 1px solid #e8e8e8 !important;
  height: 32px !important;
  padding: 0 16px !important;
  font-size: 14px !important;
  font-weight: 400 !important;
  border-radius: 4px !important;
  transition: all 0.3s ease !important;
}

.loginButton:hover,
.adminButton:hover,
.logoutButton:hover {
  background: #f5f5f5 !important;
  border-color: #d9d9d9 !important;
  color: #000 !important;
}

.adminButton {
  background: #fff !important;
  color: #ff4d4f !important;
  border-color: #ff4d4f !important;
}

.adminButton:hover {
  background: #ff4d4f !important;
  border-color: #ff4d4f !important;
  color: #fff !important;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .header {
    padding: 0 24px !important;
  }
  
  .headerContent {
    max-width: none;
  }
}

@media (max-width: 768px) {
  .header {
    padding: 0 16px !important;
  }
  
  .headerContent {
    flex-wrap: wrap;
    height: auto;
    min-height: 64px;
    padding: 8px 0;
  }
  
  .logo {
    font-size: 16px !important;
    margin-bottom: 8px;
  }
  
  .menu {
    order: 3;
    width: 100%;
    justify-content: flex-start;
    margin-top: 8px;
  }
  
  .menu .ant-menu-item {
    margin: 0 4px;
    padding: 0 8px;
    font-size: 13px;
  }
  
  .userActions {
    order: 2;
    gap: 8px;
  }
  
  .welcomeText {
    font-size: 12px;
  }
  
  .loginButton,
  .registerButton,
  .logoutButton {
    height: 28px !important;
    padding: 0 12px !important;
    font-size: 12px !important;
  }
}

@media (max-width: 480px) {
  .headerContent {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .logo {
    text-align: center;
    margin-bottom: 0;
  }
  
  .menu {
    order: 2;
    margin-top: 0;
  }
  
  .userActions {
    order: 3;
    justify-content: center;
  }
}
