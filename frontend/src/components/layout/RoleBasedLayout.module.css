.layout {
  min-height: 100vh !important;
  background: #ffffff !important;
}

.content {
  background: #ffffff !important;
  min-height: calc(100vh - 64px - 80px) !important; /* 减去header和footer的高度 */
  padding: 0 !important;
}

.footer {
  background: #fafafa !important;
  border-top: 1px solid #e8e8e8 !important;
  padding: 24px 50px !important;
  text-align: center !important;
  margin-top: auto !important;
}

.footerContent {
  max-width: 1200px;
  margin: 0 auto;
}

.footerMain {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.footerText {
  text-align: center;
  flex: 1;
}

.footerText p {
  margin: 0 !important;
  color: #666 !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
}

.footerText p:first-child {
  margin-bottom: 4px !important;
  font-weight: 500 !important;
}

.footerActions {
  flex-shrink: 0;
  margin-left: 24px;
}

/* 底部管理员链接样式 */
.adminLinks {
  margin-top: 8px;
  justify-content: center;
}

.footerAdminButton {
  color: #666 !important;
  font-size: 13px !important;
  padding: 4px 8px !important;
  height: auto !important;
  border-radius: 4px !important;
  transition: all 0.2s ease !important;
}

.footerAdminButton:hover {
  color: #1890ff !important;
  background-color: rgba(24, 144, 255, 0.06) !important;
}

/* 审核员专用样式 */
.layout.reviewer {
  background: #f6f8ff !important;
}

.layout.reviewer .content {
  background: #f6f8ff !important;
}

.layout.reviewer .footer {
  background: #e6f7ff !important;
  border-top-color: #d6e3ff !important;
}

.layout.reviewer .footerContent p {
  color: #1890ff !important;
}

.layout.reviewer .footerContent p:first-child {
  color: #0050b3 !important;
}

/* 管理员专用样式 */
.layout.admin {
  background: #fff7e6 !important;
}

.layout.admin .content {
  background: #fff7e6 !important;
}

.layout.admin .footer {
  background: #fff2e8 !important;
  border-top-color: #ffd591 !important;
}

.layout.admin .footerContent p {
  color: #fa8c16 !important;
}

.layout.admin .footerContent p:first-child {
  color: #d46b08 !important;
}

/* 状态提示样式 */
.layout .ant-alert {
  border-radius: 6px !important;
  margin: 16px 24px 0 !important;
}

.layout .ant-alert-warning {
  background: #fff7e6 !important;
  border: 1px solid #ffd591 !important;
}

.layout .ant-alert-warning .ant-alert-icon {
  color: #fa8c16 !important;
}

.layout .ant-alert-info {
  background: #f6f8ff !important;
  border: 1px solid #d6e3ff !important;
}

.layout .ant-alert-info .ant-alert-icon {
  color: #1890ff !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content {
    min-height: calc(100vh - 80px - 80px) !important; /* 移动端header可能更高 */
  }

  .footer {
    padding: 16px 24px !important;
  }

  .footerMain {
    flex-direction: column;
    gap: 12px;
  }

  .footerActions {
    margin-left: 0;
  }

  .footerText p {
    font-size: 12px !important;
  }

  .layout .ant-alert {
    margin: 12px 16px 0 !important;
  }
}

@media (max-width: 480px) {
  .content {
    min-height: calc(100vh - 100px - 80px) !important; /* 移动端header更高 */
  }
  
  .footer {
    padding: 12px 16px !important;
  }
  
  .layout .ant-alert {
    margin: 8px 12px 0 !important;
  }
  
  .layout .ant-alert .ant-alert-message {
    font-size: 13px !important;
  }
  
  .layout .ant-alert .ant-alert-description {
    font-size: 12px !important;
  }
}
