/* 问卷项目专用布局样式 */

.layout {
  min-height: 100vh;
  background: #f8f9fa;
}

/* 头部导航 - 白底黑字主题 */
.header {
  background: #ffffff !important;
  border-bottom: 1px solid #e8e8e8 !important;
  padding: 0 24px !important;
  height: 64px !important;
  line-height: 64px !important;
  position: sticky !important;
  top: 0 !important;
  z-index: 1000 !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
}

.headerContent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  max-width: 1400px;
  margin: 0 auto;
}

/* Logo区域 */
.logoSection {
  flex-shrink: 0;
  margin-right: 32px;
}

.logo {
  font-size: 18px;
  font-weight: 600;
  color: #000000;
  text-decoration: none;
  transition: color 0.3s ease;
}

.logo:hover {
  color: #666666;
}

/* 导航菜单 - 恢复清爽的样式方案 */
.menu {
  flex: 1;
  border: none !important;
  background: transparent !important;
  justify-content: center;
  display: flex;
  min-width: 0;
}

.menu :global(.ant-menu-item) {
  margin: 0 8px;
  padding: 0 16px;
  border-radius: 4px;
  transition: all 0.3s ease;
  font-weight: 500;
  background: transparent !important;
  color: #666666 !important;
  border: 1px solid transparent;
  height: auto;
  line-height: 1.4;
}

.menu :global(.ant-menu-item:hover) {
  background: #f5f5f5 !important;
  color: #000000 !important;
  border-color: #e8e8e8;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.menu :global(.ant-menu-item-selected) {
  background: #f0f0f0 !important;
  color: #000000 !important;
  font-weight: 600;
  border-color: #d9d9d9;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.menu :global(.ant-menu-item-selected::after) {
  display: none;
}

.menu :global(.ant-menu-item a) {
  color: inherit;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 6px;
}

/* 用户操作区域 */
.userSection {
  flex-shrink: 0;
  margin-left: 24px;
}

.userButton {
  height: 40px !important;
  padding: 0 12px !important;
  border-radius: 6px !important;
  border: 1px solid #e8e8e8 !important;
  background: #fff !important;
  color: #333 !important;
  transition: all 0.3s ease !important;
}

.userButton:hover {
  background: #f5f5f5 !important;
  border-color: #d9d9d9 !important;
  color: #000 !important;
}

.avatar {
  background: #1890ff;
}

.username {
  font-size: 14px;
  font-weight: 500;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 内容区域 - 白底主题 */
.content {
  padding: 24px;
  background: #f8f9fa;
  min-height: calc(100vh - 64px - 80px); /* 减去header和footer高度 */
  color: #000000;
}

/* 底部栏 - 灰色块主题 */
.footer {
  background: #f5f5f5 !important;
  border-top: 1px solid #e8e8e8 !important;
  padding: 24px 50px !important;
  text-align: left !important;
  margin-top: auto !important;
}

.footerContent {
  max-width: 1200px;
  margin: 0 auto;
}

.footerMain {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.footerText {
  text-align: left;
  flex: 1;
}

.footerText p {
  margin: 0 !important;
  color: #666666 !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
}

.footerText p:first-child {
  margin-bottom: 4px !important;
  font-weight: 500 !important;
  color: #000000 !important;
}

.footerActions {
  flex-shrink: 0;
  margin-left: 24px;
}

.footerAdminButton {
  color: #000000 !important;
  font-size: 14px !important;
  padding: 8px 16px !important;
  height: auto !important;
  border-radius: 4px !important;
  transition: all 0.3s ease !important;
  border: 1px solid #000000 !important;
  font-weight: 600 !important;
  background-color: #ffffff !important;
}

.footerAdminButton:hover {
  color: #ffffff !important;
  background-color: #000000 !important;
  border-color: #000000 !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .header {
    padding: 0 16px !important;
    height: 56px !important;
    line-height: 56px !important;
  }
  
  .headerContent {
    gap: 12px;
  }
  
  .logoSection {
    margin-right: 16px;
  }
  
  .logo {
    font-size: 16px;
  }
  
  /* 移动端隐藏水平菜单，使用底部导航 */
  .menu {
    display: none;
  }
  
  .userSection {
    margin-left: 12px;
  }
  
  .userButton {
    height: 36px !important;
    padding: 0 8px !important;
  }
  
  .username {
    display: none; /* 移动端隐藏用户名 */
  }
  
  .content {
    padding: 16px 12px;
    padding-bottom: 80px; /* 为底部导航留出空间 */
    min-height: calc(100vh - 56px - 80px - 80px); /* header + footer + mobile nav */
  }

  .footer {
    padding: 16px 20px !important;
  }

  .footerMain {
    flex-direction: column;
    gap: 12px;
  }

  .footerActions {
    margin-left: 0;
  }
}

/* 平板端优化 */
@media (min-width: 769px) and (max-width: 1024px) {
  .headerContent {
    max-width: 100%;
    padding: 0 16px;
  }
  
  .menu :global(.ant-menu-item) {
    margin: 0 4px;
    padding: 0 12px;
  }
  
  .content {
    padding: 20px 16px;
  }
}

/* 大屏优化 */
@media (min-width: 1400px) {
  .headerContent {
    max-width: 1600px;
  }
  
  .content {
    max-width: 1600px;
    margin: 0 auto;
    padding: 32px 24px;
  }
}

/* 导航项图标优化 */
.menu :global(.anticon) {
  font-size: 16px;
  margin-right: 6px;
}

/* 用户下拉菜单样式 */
:global(.ant-dropdown) {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

:global(.ant-dropdown .ant-dropdown-menu) {
  border-radius: 8px;
  padding: 8px;
}

:global(.ant-dropdown .ant-dropdown-menu-item) {
  border-radius: 6px;
  margin: 2px 0;
  padding: 8px 12px;
}

/* 响应式字体 */
@media (max-width: 480px) {
  .logo {
    font-size: 14px;
  }
  
  .content {
    padding: 12px 8px;
    padding-bottom: 80px;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .header {
    border-bottom: 2px solid #000 !important;
  }
  
  .menu :global(.ant-menu-item-selected) {
    background: #000 !important;
    color: #fff !important;
  }
  
  .userButton {
    border: 2px solid #000 !important;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .layout {
    background: #1f1f1f;
  }
  
  .header {
    background: #1f1f1f !important;
    border-bottom-color: #333 !important;
  }
  
  .logo {
    color: #40a9ff;
  }
  
  .menu :global(.ant-menu-item) {
    color: #ccc;
  }
  
  .menu :global(.ant-menu-item:hover) {
    background: rgba(64, 169, 255, 0.1);
    color: #40a9ff;
  }
  
  .menu :global(.ant-menu-item-selected) {
    background: rgba(64, 169, 255, 0.2);
    color: #40a9ff;
  }
  
  .userButton {
    background: #2a2a2a !important;
    border-color: #444 !important;
    color: #ccc !important;
  }
  
  .content {
    background: #1f1f1f;
    color: #ccc;
  }

  .footer {
    background: #2a2a2a !important;
    border-top-color: #444 !important;
  }

  .footerText p {
    color: #ccc !important;
  }

  .footerAdminButton {
    color: #ccc !important;
  }

  .footerAdminButton:hover {
    color: #40a9ff !important;
    background-color: rgba(64, 169, 255, 0.1) !important;
  }
}
