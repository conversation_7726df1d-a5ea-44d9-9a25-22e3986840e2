import React, { useState, useCallback, useEffect } from 'react';
import {
  Input,
  InputNumber,
  Select,
  Radio,
  Checkbox,
  DatePicker,
  TimePicker,
  Rate,
  Slider,
  Upload,
  Button,
  Card,
  Badge,
  Tooltip,
  Progress,
  Typography,
  Space,
  Alert
} from 'antd';
import {
  UploadOutlined,
  Bar<PERSON>hartOutlined,
  UserOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import type { Question, QuestionStatistics } from '../../types/questionnaire-engine';
import { useQuestionStatistics } from '../hooks/useQuestionStatistics';
import styles from './QuestionRenderer.module.css';

const { TextArea } = Input;
const { Option } = Select;
const { Group: RadioGroup } = Radio;
const { Group: CheckboxGroup } = Checkbox;
const { Text, Title } = Typography;

interface QuestionRendererProps {
  question: Question;
  value: any;
  onChange: (value: any) => void;
  onBlur?: (value: any) => void;
  error?: string;
  disabled?: boolean;
  showStatistics?: boolean;
  questionnaireId?: string;
  className?: string;
}

export const QuestionRenderer: React.FC<QuestionRendererProps> = ({
  question,
  value,
  onChange,
  onBlur,
  error,
  disabled = false,
  showStatistics = false,
  questionnaireId,
  className = ''
}) => {
  const [showStatsDetails, setShowStatsDetails] = useState(false);
  const [isFocused, setIsFocused] = useState(false);

  // 获取统计数据
  const { statistics, loading: statsLoading } = useQuestionStatistics(
    questionnaireId,
    question.id,
    showStatistics
  );

  // 处理输入变化
  const handleChange = useCallback((newValue: any) => {
    onChange(newValue);
  }, [onChange]);

  // 处理失焦
  const handleBlur = useCallback(() => {
    setIsFocused(false);
    onBlur?.(value);
  }, [onBlur, value]);

  // 处理聚焦
  const handleFocus = useCallback(() => {
    setIsFocused(true);
  }, []);

  // 渲染问题标题
  const renderQuestionTitle = () => (
    <div className={styles.questionTitle}>
      <Space align="start">
        <Text strong className={styles.titleText}>
          {question.title}
          {question.required && <span className={styles.required}>*</span>}
        </Text>
        
        {statistics && statistics.totalResponses > 0 && (
          <Badge 
            count={`${statistics.totalResponses}人已答`} 
            style={{ backgroundColor: '#52c41a' }}
          />
        )}
        
        {showStatistics && statistics && (
          <Tooltip title={showStatsDetails ? '隐藏统计' : '显示统计'}>
            <Button
              type="text"
              size="small"
              icon={showStatsDetails ? <EyeInvisibleOutlined /> : <EyeOutlined />}
              onClick={() => setShowStatsDetails(!showStatsDetails)}
            />
          </Tooltip>
        )}
      </Space>
      
      {question.description && (
        <Text type="secondary" className={styles.description}>
          {question.description}
        </Text>
      )}
    </div>
  );

  // 渲染统计信息
  const renderStatistics = () => {
    if (!showStatistics || !showStatsDetails || !statistics || statistics.totalResponses === 0) {
      return null;
    }

    return (
      <Card size="small" className={styles.statisticsCard}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Text strong>
            <BarChartOutlined /> 其他人的选择分布
          </Text>
          
          {question.type === 'radio' || question.type === 'select' ? (
            <div className={styles.distributionChart}>
              {Object.entries(statistics.distribution || {}).map(([option, count]) => {
                const percentage = ((count as number) / statistics.totalResponses) * 100;
                const optionLabel = question.options?.find(opt => opt.value === option)?.label || option;
                
                return (
                  <div key={option} className={styles.distributionItem}>
                    <Space justify="space-between" style={{ width: '100%' }}>
                      <Text>{optionLabel}</Text>
                      <Text type="secondary">{count}人 ({percentage.toFixed(1)}%)</Text>
                    </Space>
                    <Progress 
                      percent={percentage} 
                      showInfo={false} 
                      strokeColor="#1890ff"
                      size="small"
                    />
                  </div>
                );
              })}
            </div>
          ) : question.type === 'checkbox' ? (
            <div className={styles.distributionChart}>
              {Object.entries(statistics.distribution || {}).map(([option, count]) => {
                const percentage = ((count as number) / statistics.totalResponses) * 100;
                const optionLabel = question.options?.find(opt => opt.value === option)?.label || option;
                
                return (
                  <div key={option} className={styles.distributionItem}>
                    <Space justify="space-between" style={{ width: '100%' }}>
                      <Text>{optionLabel}</Text>
                      <Text type="secondary">{count}人选择 ({percentage.toFixed(1)}%)</Text>
                    </Space>
                    <Progress 
                      percent={percentage} 
                      showInfo={false} 
                      strokeColor="#52c41a"
                      size="small"
                    />
                  </div>
                );
              })}
            </div>
          ) : (
            <Text type="secondary">已有 {statistics.totalResponses} 人回答此问题</Text>
          )}
        </Space>
      </Card>
    );
  };

  // 渲染输入组件
  const renderInput = () => {
    const commonProps = {
      disabled,
      onFocus: handleFocus,
      onBlur: handleBlur,
      status: error ? 'error' : undefined,
      className: styles.inputComponent
    };

    switch (question.type) {
      case 'text':
        return (
          <Input
            {...commonProps}
            value={value}
            onChange={(e) => handleChange(e.target.value)}
            placeholder={question.placeholder}
            maxLength={question.maxLength}
            showCount={!!question.maxLength}
          />
        );

      case 'textarea':
        return (
          <TextArea
            {...commonProps}
            value={value}
            onChange={(e) => handleChange(e.target.value)}
            placeholder={question.placeholder}
            rows={question.rows || 4}
            maxLength={question.maxLength}
            showCount={!!question.maxLength}
          />
        );

      case 'number':
        return (
          <InputNumber
            {...commonProps}
            value={value}
            onChange={handleChange}
            placeholder={question.placeholder}
            min={question.min}
            max={question.max}
            step={question.step}
            style={{ width: '100%' }}
          />
        );

      case 'email':
        return (
          <Input
            {...commonProps}
            type="email"
            value={value}
            onChange={(e) => handleChange(e.target.value)}
            placeholder={question.placeholder || '请输入邮箱地址'}
          />
        );

      case 'phone':
        return (
          <Input
            {...commonProps}
            value={value}
            onChange={(e) => handleChange(e.target.value)}
            placeholder={question.placeholder || '请输入手机号码'}
            maxLength={11}
          />
        );

      case 'select':
        return (
          <Select
            {...commonProps}
            value={value}
            onChange={handleChange}
            placeholder={question.placeholder || '请选择'}
            style={{ width: '100%' }}
            allowClear
          >
            {question.options?.map((option) => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        );

      case 'radio':
        return (
          <RadioGroup
            {...commonProps}
            value={value}
            onChange={(e) => handleChange(e.target.value)}
          >
            <Space direction="vertical">
              {question.options?.map((option) => (
                <Radio key={option.value} value={option.value}>
                  {option.label}
                </Radio>
              ))}
            </Space>
          </RadioGroup>
        );

      case 'checkbox':
        return (
          <CheckboxGroup
            {...commonProps}
            value={value || []}
            onChange={handleChange}
          >
            <Space direction="vertical">
              {question.options?.map((option) => (
                <Checkbox key={option.value} value={option.value}>
                  {option.label}
                </Checkbox>
              ))}
            </Space>
          </CheckboxGroup>
        );

      case 'date':
        return (
          <DatePicker
            {...commonProps}
            value={value}
            onChange={handleChange}
            placeholder={question.placeholder || '请选择日期'}
            style={{ width: '100%' }}
          />
        );

      case 'time':
        return (
          <TimePicker
            {...commonProps}
            value={value}
            onChange={handleChange}
            placeholder={question.placeholder || '请选择时间'}
            style={{ width: '100%' }}
          />
        );

      case 'rating':
        return (
          <Rate
            {...commonProps}
            value={value}
            onChange={handleChange}
            count={question.max || 5}
            allowHalf={question.allowHalf}
          />
        );

      case 'slider':
        return (
          <div className={styles.sliderContainer}>
            <Slider
              {...commonProps}
              value={value}
              onChange={handleChange}
              min={question.min || 0}
              max={question.max || 100}
              step={question.step || 1}
              marks={question.marks}
              included={true}
            />
            {value !== undefined && (
              <Text type="secondary" className={styles.sliderValue}>
                当前值: {value}
              </Text>
            )}
          </div>
        );

      default:
        return (
          <Input
            {...commonProps}
            value={value}
            onChange={(e) => handleChange(e.target.value)}
            placeholder={question.placeholder}
          />
        );
    }
  };

  return (
    <div className={`${styles.questionRenderer} ${className}`}>
      {renderQuestionTitle()}
      
      <div className={styles.inputContainer}>
        {renderInput()}
        
        {error && (
          <Alert
            message={error}
            type="error"
            showIcon
            className={styles.errorAlert}
          />
        )}
      </div>
      
      {renderStatistics()}
    </div>
  );
};
