import React, { useState, useEffect, useCallback } from 'react';
import { Card, Button, Progress, message, Spin, Alert } from 'antd';
import { ArrowLeftOutlined, ArrowRightOutlined, CheckOutlined } from '@ant-design/icons';
import { QuestionRenderer } from './QuestionRenderer';
import { useQuestionnaire } from '../hooks/useQuestionnaire';
import { useQuestionValidation } from '../hooks/useQuestionValidation';
import { useProgress } from '../hooks/useProgress';
import { useLocalStorage } from '../hooks/useLocalStorage';
import type { 
  QuestionnaireConfig, 
  QuestionnaireResponse, 
  SectionResponse,
  QuestionResponse 
} from '../../types/questionnaire-engine';
import styles from './QuestionnaireEngine.module.css';

interface QuestionnaireEngineProps {
  questionnaire: QuestionnaireConfig;
  onSubmit: (data: QuestionnaireResponse) => Promise<any>;
  onProgress?: (progress: { current: number; total: number; percentage: number }) => void;
  config?: {
    showStatistics?: boolean;
    autoSave?: boolean;
    theme?: string;
    allowBack?: boolean;
    showProgress?: boolean;
  };
  className?: string;
}

export const QuestionnaireEngine: React.FC<QuestionnaireEngineProps> = ({
  questionnaire,
  onSubmit,
  onProgress,
  config = {},
  className
}) => {
  const {
    showStatistics = true,
    autoSave = true,
    theme = 'default',
    allowBack = true,
    showProgress = true
  } = config;

  // 如果问卷配置为空，显示加载状态
  console.log('QuestionnaireEngine received questionnaire:', questionnaire);
  if (!questionnaire || !questionnaire.sections || questionnaire.sections.length === 0) {
    console.log('Questionnaire is invalid, showing loading state');
    return (
      <div className={styles.loadingContainer}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>加载问卷配置中...</div>
      </div>
    );
  }

  // 状态管理
  const [currentSectionIndex, setCurrentSectionIndex] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [startTime] = useState(Date.now());

  // 自定义 hooks
  const {
    responses,
    updateResponse,
    clearResponses,
    getResponsesForSection,
    getAllResponses
  } = useQuestionnaire(questionnaire.id);

  const {
    validateSection,
    validateQuestion,
    getValidationErrors
  } = useQuestionValidation(questionnaire);

  const {
    progress,
    updateProgress
  } = useProgress(questionnaire.sections.length);

  const {
    saveToStorage,
    loadFromStorage,
    clearStorage
  } = useLocalStorage(`questionnaire-${questionnaire.id}`);

  // 当前节
  const currentSection = questionnaire.sections[currentSectionIndex];
  const isLastSection = currentSectionIndex === questionnaire.sections.length - 1;
  const isFirstSection = currentSectionIndex === 0;

  // 如果当前节不存在或没有问题，显示错误
  if (!currentSection || !currentSection.questions || !Array.isArray(currentSection.questions)) {
    console.error('Current section is invalid:', currentSection);
    console.error('Available keys in currentSection:', currentSection ? Object.keys(currentSection) : 'null');
    console.error('Questions field:', currentSection?.questions);
    console.error('Full questionnaire structure:', questionnaire);
    return (
      <div className={styles.errorContainer}>
        <Alert
          message="问卷配置错误"
          description={`第 ${currentSectionIndex + 1} 部分配置不正确`}
          type="error"
          showIcon
        />
      </div>
    );
  }

  // 自动保存
  useEffect(() => {
    if (autoSave && responses.length > 0) {
      const saveData = {
        responses: getAllResponses(),
        currentSectionIndex,
        timestamp: Date.now()
      };
      saveToStorage(saveData);
    }
  }, [responses, currentSectionIndex, autoSave, getAllResponses, saveToStorage]);

  // 加载保存的数据
  useEffect(() => {
    if (autoSave) {
      const savedData = loadFromStorage();
      if (savedData && savedData.responses) {
        // 恢复保存的响应数据
        savedData.responses.forEach((response: QuestionResponse) => {
          updateResponse(response.questionId, response.value, response.sectionId);
        });
        
        // 恢复当前节索引
        if (savedData.currentSectionIndex !== undefined) {
          setCurrentSectionIndex(savedData.currentSectionIndex);
        }
        
        message.info('已恢复之前保存的填写进度');
      }
    }
  }, [autoSave, loadFromStorage, updateResponse]);

  // 更新进度
  useEffect(() => {
    const completedSections = questionnaire.sections.slice(0, currentSectionIndex).length;
    const currentSectionProgress = getCurrentSectionProgress();
    const totalProgress = (completedSections + currentSectionProgress) / questionnaire.sections.length;
    
    updateProgress(totalProgress * 100);
    
    if (onProgress) {
      onProgress({
        current: completedSections,
        total: questionnaire.sections.length,
        percentage: totalProgress * 100
      });
    }
  }, [currentSectionIndex, responses, questionnaire.sections.length, onProgress, updateProgress]);

  // 获取当前节的完成进度
  const getCurrentSectionProgress = useCallback(() => {
    if (!currentSection) return 0;
    
    const sectionResponses = getResponsesForSection(currentSection.id);
    const requiredQuestions = currentSection.questions.filter(q => q.required);
    const answeredRequired = requiredQuestions.filter(q => 
      sectionResponses.some(r => r.questionId === q.id && r.value !== null && r.value !== undefined)
    );
    
    return requiredQuestions.length > 0 ? answeredRequired.length / requiredQuestions.length : 1;
  }, [currentSection, getResponsesForSection]);

  // 处理问题回答
  const handleQuestionResponse = useCallback((questionId: string, value: any) => {
    updateResponse(questionId, value, currentSection.id);
  }, [updateResponse, currentSection?.id]);

  // 验证当前节
  const validateCurrentSection = useCallback(() => {
    if (!currentSection) return true;
    
    const sectionResponses = getResponsesForSection(currentSection.id);
    const validationResult = validateSection(currentSection.id, sectionResponses);
    
    if (!validationResult.valid) {
      const errors = getValidationErrors(currentSection.id);
      message.error(`请完成所有必填项目: ${errors.join(', ')}`);
      return false;
    }
    
    return true;
  }, [currentSection, getResponsesForSection, validateSection, getValidationErrors]);

  // 下一步
  const handleNext = useCallback(() => {
    if (!validateCurrentSection()) return;
    
    if (isLastSection) {
      handleSubmit();
    } else {
      setCurrentSectionIndex(prev => prev + 1);
    }
  }, [validateCurrentSection, isLastSection]);

  // 上一步
  const handlePrevious = useCallback(() => {
    if (!isFirstSection) {
      setCurrentSectionIndex(prev => prev - 1);
    }
  }, [isFirstSection]);

  // 提交问卷
  const handleSubmit = useCallback(async () => {
    if (!validateCurrentSection()) return;
    
    setIsSubmitting(true);
    
    try {
      // 构建提交数据
      const sectionResponses: SectionResponse[] = questionnaire.sections.map(section => ({
        sectionId: section.id,
        sectionTitle: section.title,
        questionResponses: getResponsesForSection(section.id),
        completedAt: new Date().toISOString()
      }));

      const submissionData: QuestionnaireResponse = {
        questionnaireId: questionnaire.id,
        sectionResponses,
        metadata: {
          responseId: `resp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          isAnonymous: true,
          startedAt: new Date(startTime).toISOString(),
          completedAt: new Date().toISOString(),
          submittedAt: new Date().toISOString(),
          userAgent: navigator.userAgent,
          source: 'web',
          version: '2.0.0'
        }
      };

      await onSubmit(submissionData);
      
      // 清理本地数据
      clearResponses();
      if (autoSave) {
        clearStorage();
      }
      
      message.success('问卷提交成功！');
      
    } catch (error) {
      console.error('提交失败:', error);
      message.error('提交失败，请重试');
    } finally {
      setIsSubmitting(false);
    }
  }, [
    validateCurrentSection,
    questionnaire,
    getResponsesForSection,
    startTime,
    onSubmit,
    clearResponses,
    autoSave,
    clearStorage
  ]);

  // 渲染进度条
  const renderProgress = () => {
    if (!showProgress) return null;
    
    return (
      <div className={styles.progressContainer}>
        <Progress
          percent={progress}
          strokeColor={{
            '0%': '#108ee9',
            '100%': '#87d068',
          }}
          format={(percent) => `${Math.round(percent || 0)}%`}
        />
        <div className={styles.progressText}>
          第 {currentSectionIndex + 1} 部分，共 {questionnaire.sections.length} 部分
        </div>
      </div>
    );
  };

  // 渲染导航按钮
  const renderNavigation = () => {
    return (
      <div className={styles.navigation}>
        <Button
          type="default"
          icon={<ArrowLeftOutlined />}
          onClick={handlePrevious}
          disabled={isFirstSection || isSubmitting}
          style={{ visibility: allowBack && !isFirstSection ? 'visible' : 'hidden' }}
        >
          上一步
        </Button>
        
        <Button
          type="primary"
          icon={isLastSection ? <CheckOutlined /> : <ArrowRightOutlined />}
          onClick={handleNext}
          loading={isSubmitting}
          size="large"
        >
          {isLastSection ? '提交问卷' : '下一步'}
        </Button>
      </div>
    );
  };

  // 加载状态
  if (!currentSection) {
    return (
      <div className={styles.loading}>
        <Spin size="large" />
        <div>加载问卷中...</div>
      </div>
    );
  }

  return (
    <div className={`${styles.questionnaireEngine} ${styles[theme]} ${className || ''}`}>
      {renderProgress()}
      
      <Card
        title={currentSection.title}
        className={styles.sectionCard}
        extra={
          <span className={styles.sectionCounter}>
            {currentSectionIndex + 1} / {questionnaire.sections.length}
          </span>
        }
      >
        {currentSection.description && (
          <Alert
            message={currentSection.description}
            type="info"
            showIcon
            className={styles.sectionDescription}
          />
        )}
        
        <div className={styles.questionsContainer}>
          {currentSection.questions.map((question) => (
            <QuestionRenderer
              key={question.id}
              question={question}
              value={getResponsesForSection(currentSection.id).find(r => r.questionId === question.id)?.value}
              onChange={(value) => handleQuestionResponse(question.id, value)}
              showStatistics={showStatistics}
              questionnaireId={questionnaire.id}
              className={styles.questionItem}
            />
          ))}
        </div>
        
        {renderNavigation()}
      </Card>
    </div>
  );
};
