/* 问卷引擎主容器 */
.questionnaireEngine {
  max-width: 800px;
  margin: 0 auto;
  padding: 24px;
  background: var(--color-background);
  min-height: 100vh;
}

/* 进度条容器 */
.progressContainer {
  margin-bottom: 24px;
  padding: 16px;
  background: var(--color-card-background);
  border-radius: var(--border-radius);
  border: 1px solid var(--color-border);
}

.progressText {
  margin-top: 8px;
  text-align: center;
  color: var(--color-text-secondary);
  font-size: 14px;
}

/* 节卡片 */
.sectionCard {
  margin-bottom: 24px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-card);
  border: 1px solid var(--color-border);
}

.sectionCard .ant-card-head {
  background: var(--color-card-background);
  border-bottom: 1px solid var(--color-border);
}

.sectionCard .ant-card-body {
  background: var(--color-card-background);
  padding: 24px;
}

.sectionCounter {
  color: var(--color-text-secondary);
  font-size: 14px;
  font-weight: normal;
}

/* 节描述 */
.sectionDescription {
  margin-bottom: 24px;
  border-radius: var(--border-radius);
}

/* 问题容器 */
.questionsContainer {
  margin-bottom: 32px;
}

.questionItem {
  margin-bottom: 24px;
  padding: 20px;
  background: var(--color-background);
  border-radius: var(--border-radius);
  border: 1px solid var(--color-border-light);
  transition: all 0.3s ease;
}

.questionItem:hover {
  border-color: var(--color-primary);
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
}

.questionItem:last-child {
  margin-bottom: 0;
}

/* 导航按钮 */
.navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 24px;
  border-top: 1px solid var(--color-border);
}

.navigation .ant-btn {
  min-width: 120px;
  height: 40px;
  border-radius: var(--border-radius);
  font-weight: 500;
}

.navigation .ant-btn-primary {
  background: var(--color-primary);
  border-color: var(--color-primary);
}

.navigation .ant-btn-primary:hover {
  background: var(--color-primary-hover);
  border-color: var(--color-primary-hover);
}

/* 加载状态 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  color: var(--color-text-secondary);
}

.loading .ant-spin {
  margin-bottom: 16px;
}

/* 主题样式 */
.employment-survey {
  --color-primary: #1890ff;
  --color-primary-hover: #40a9ff;
}

.employment-survey .sectionCard {
  border-color: var(--color-primary);
}

.employment-survey .sectionCard .ant-card-head {
  background: linear-gradient(135deg, var(--color-primary) 0%, #40a9ff 100%);
  color: white;
}

.employment-survey .sectionCard .ant-card-head-title {
  color: white;
}

.employment-survey .sectionCounter {
  color: rgba(255, 255, 255, 0.8);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .questionnaireEngine {
    padding: 16px;
    margin: 0;
    max-width: 100%;
  }
  
  .sectionCard .ant-card-body {
    padding: 16px;
  }
  
  .questionItem {
    padding: 16px;
    margin-bottom: 16px;
  }
  
  .navigation {
    flex-direction: column;
    gap: 12px;
  }
  
  .navigation .ant-btn {
    width: 100%;
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .questionnaireEngine {
    padding: 12px;
  }
  
  .progressContainer {
    padding: 12px;
    margin-bottom: 16px;
  }
  
  .sectionCard .ant-card-body {
    padding: 12px;
  }
  
  .questionItem {
    padding: 12px;
  }
}
