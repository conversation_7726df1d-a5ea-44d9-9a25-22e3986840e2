/* 问题渲染器主容器 */
.questionRenderer {
  margin-bottom: 24px;
}

/* 问题标题 */
.questionTitle {
  margin-bottom: 12px;
}

.titleText {
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text);
  line-height: 1.5;
}

.required {
  color: #ff4d4f;
  margin-left: 4px;
  font-weight: bold;
}

.description {
  display: block;
  margin-top: 4px;
  font-size: 14px;
  color: var(--color-text-secondary);
  line-height: 1.4;
}

/* 输入组件容器 */
.inputContainer {
  margin-bottom: 16px;
}

.inputComponent {
  width: 100%;
}

/* 错误提示 */
.errorAlert {
  margin-top: 8px;
  border-radius: var(--border-radius);
}

/* 统计卡片 */
.statisticsCard {
  margin-top: 16px;
  border-radius: var(--border-radius);
  border: 1px solid var(--color-border-light);
  background: var(--color-background-light);
}

.statisticsCard .ant-card-body {
  padding: 16px;
}

/* 分布图表 */
.distributionChart {
  margin-top: 12px;
}

.distributionItem {
  margin-bottom: 12px;
}

.distributionItem:last-child {
  margin-bottom: 0;
}

.distributionItem .ant-progress {
  margin-top: 4px;
}

/* 滑块容器 */
.sliderContainer {
  padding: 8px 0;
}

.sliderValue {
  display: block;
  text-align: center;
  margin-top: 8px;
  font-size: 14px;
}

/* 特殊输入组件样式 */
.questionRenderer .ant-input,
.questionRenderer .ant-input-number,
.questionRenderer .ant-select,
.questionRenderer .ant-picker {
  border-radius: var(--border-radius);
  border-color: var(--color-border);
  transition: all 0.3s ease;
}

.questionRenderer .ant-input:hover,
.questionRenderer .ant-input-number:hover,
.questionRenderer .ant-select:hover .ant-select-selector,
.questionRenderer .ant-picker:hover {
  border-color: var(--color-primary);
}

.questionRenderer .ant-input:focus,
.questionRenderer .ant-input-number:focus,
.questionRenderer .ant-select:focus .ant-select-selector,
.questionRenderer .ant-picker:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 单选框和复选框组 */
.questionRenderer .ant-radio-group,
.questionRenderer .ant-checkbox-group {
  width: 100%;
}

.questionRenderer .ant-radio-wrapper,
.questionRenderer .ant-checkbox-wrapper {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
  padding: 8px 12px;
  border-radius: var(--border-radius);
  border: 1px solid var(--color-border-light);
  background: var(--color-background);
  transition: all 0.3s ease;
  line-height: 1.5;
}

.questionRenderer .ant-radio-wrapper:hover,
.questionRenderer .ant-checkbox-wrapper:hover {
  border-color: var(--color-primary);
  background: var(--color-background-light);
}

.questionRenderer .ant-radio-wrapper:last-child,
.questionRenderer .ant-checkbox-wrapper:last-child {
  margin-bottom: 0;
}

.questionRenderer .ant-radio,
.questionRenderer .ant-checkbox {
  margin-right: 8px;
  margin-top: 2px;
}

/* 评分组件 */
.questionRenderer .ant-rate {
  font-size: 24px;
  color: var(--color-primary);
}

/* 滑块组件 */
.questionRenderer .ant-slider {
  margin: 16px 8px;
}

.questionRenderer .ant-slider-track {
  background: var(--color-primary);
}

.questionRenderer .ant-slider-handle {
  border-color: var(--color-primary);
}

.questionRenderer .ant-slider-handle:focus {
  box-shadow: 0 0 0 5px rgba(24, 144, 255, 0.2);
}

/* 上传组件 */
.questionRenderer .ant-upload {
  width: 100%;
}

.questionRenderer .ant-upload-btn {
  width: 100%;
  height: 100px;
  border: 2px dashed var(--color-border);
  border-radius: var(--border-radius);
  background: var(--color-background);
  transition: all 0.3s ease;
}

.questionRenderer .ant-upload-btn:hover {
  border-color: var(--color-primary);
  background: var(--color-background-light);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .questionRenderer {
    margin-bottom: 20px;
  }
  
  .titleText {
    font-size: 15px;
  }
  
  .description {
    font-size: 13px;
  }
  
  .statisticsCard .ant-card-body {
    padding: 12px;
  }
  
  .questionRenderer .ant-radio-wrapper,
  .questionRenderer .ant-checkbox-wrapper {
    padding: 6px 10px;
    margin-bottom: 8px;
  }
  
  .questionRenderer .ant-rate {
    font-size: 20px;
  }
}

@media (max-width: 480px) {
  .questionRenderer {
    margin-bottom: 16px;
  }
  
  .titleText {
    font-size: 14px;
  }
  
  .description {
    font-size: 12px;
  }
  
  .questionRenderer .ant-radio-wrapper,
  .questionRenderer .ant-checkbox-wrapper {
    padding: 4px 8px;
    margin-bottom: 6px;
    font-size: 14px;
  }
  
  .questionRenderer .ant-rate {
    font-size: 18px;
  }
  
  .sliderContainer {
    padding: 4px 0;
  }
}
