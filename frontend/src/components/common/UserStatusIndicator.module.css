/* 用户状态指示器样式 */

.container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.dropdownButton {
  height: auto !important;
  padding: 8px 12px !important;
  border-radius: 8px !important;
  transition: all 0.3s ease !important;
  border: 1px solid transparent !important;
}

.dropdownButton:hover {
  background-color: #f5f5f5 !important;
  border-color: #d9d9d9 !important;
}

.dropdownButton:focus {
  background-color: #f5f5f5 !important;
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1) !important;
}

.userInfo {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 2px;
  min-width: 0;
}

.userName {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
  line-height: 1.2;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.statusText {
  font-size: 12px !important;
  color: #8c8c8c !important;
  line-height: 1 !important;
}

.dropdownIcon {
  font-size: 12px;
  color: #8c8c8c;
  transition: transform 0.3s ease;
}

.dropdownButton:hover .dropdownIcon {
  color: #1890ff;
}

/* 下拉菜单项样式 */
.menuItem {
  padding: 4px 0;
}

.menuItemTitle {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
  line-height: 1.4;
}

.menuItemDesc {
  font-size: 12px;
  color: #8c8c8c;
  line-height: 1.3;
  margin-top: 2px;
}

/* 简化版本样式 */
.simpleStatus {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 6px;
  background-color: #f6f8fa;
  border: 1px solid #e1e4e8;
}

.simpleStatusText {
  font-size: 12px !important;
  font-weight: 500 !important;
  color: #586069 !important;
  line-height: 1 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .userInfo {
    display: none;
  }
  
  .dropdownButton {
    padding: 6px !important;
  }
  
  .userName {
    max-width: 80px;
  }
  
  .simpleStatus {
    padding: 3px 6px;
  }
  
  .simpleStatusText {
    font-size: 11px !important;
  }
}

@media (max-width: 480px) {
  .container {
    gap: 4px;
  }
  
  .dropdownButton {
    padding: 4px !important;
  }
}

/* 加载状态 */
.dropdownButton.ant-btn-loading {
  pointer-events: none;
}

.dropdownButton .ant-btn-loading-icon {
  margin-right: 4px;
}

/* 状态徽章自定义样式 */
.container :global(.ant-badge) {
  display: flex;
  align-items: center;
}

.container :global(.ant-badge-status-dot) {
  width: 8px;
  height: 8px;
  margin-right: 4px;
}

.container :global(.ant-badge-status-text) {
  font-size: 12px;
  color: #8c8c8c;
}

/* 头像样式优化 */
.container :global(.ant-avatar) {
  flex-shrink: 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.container :global(.ant-avatar-icon) {
  font-size: 14px;
}

/* 下拉菜单样式优化 */
:global(.ant-dropdown) {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e8e8e8;
}

:global(.ant-dropdown .ant-dropdown-menu) {
  border-radius: 8px;
  padding: 8px;
}

:global(.ant-dropdown .ant-dropdown-menu-item) {
  border-radius: 6px;
  padding: 8px 12px;
  margin-bottom: 2px;
}

:global(.ant-dropdown .ant-dropdown-menu-item:last-child) {
  margin-bottom: 0;
}

:global(.ant-dropdown .ant-dropdown-menu-item:hover) {
  background-color: #f5f5f5;
}

:global(.ant-dropdown .ant-dropdown-menu-item-disabled) {
  color: #262626 !important;
  cursor: default;
}

:global(.ant-dropdown .ant-dropdown-menu-item-disabled:hover) {
  background-color: transparent !important;
}

:global(.ant-dropdown .ant-dropdown-menu-item-divider) {
  margin: 8px 0;
  background-color: #f0f0f0;
}

/* 动画效果 */
.dropdownButton {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.simpleStatus {
  animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-8px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 主题适配 */
@media (prefers-color-scheme: dark) {
  .userName {
    color: #f0f0f0;
  }
  
  .dropdownButton:hover {
    background-color: #2a2a2a !important;
    border-color: #404040 !important;
  }
  
  .simpleStatus {
    background-color: #2a2a2a;
    border-color: #404040;
  }
  
  .simpleStatusText {
    color: #b0b0b0 !important;
  }
  
  .menuItemTitle {
    color: #f0f0f0;
  }
  
  .menuItemDesc {
    color: #b0b0b0;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .dropdownButton {
    border-color: #000 !important;
  }
  
  .userName {
    color: #000;
    font-weight: 600;
  }
  
  .statusText {
    color: #000 !important;
    font-weight: 500 !important;
  }
  
  .simpleStatus {
    border-color: #000;
  }
  
  .simpleStatusText {
    color: #000 !important;
    font-weight: 600 !important;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .dropdownButton,
  .simpleStatus,
  .dropdownIcon {
    transition: none;
    animation: none;
  }
}
