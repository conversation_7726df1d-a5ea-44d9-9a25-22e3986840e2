import React from 'react';
import { errorMonitor } from '../../utils/errorMonitor';

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
}

export class ErrorBoundary extends React.Component<
  React.PropsWithChildren<{}>,
  ErrorBoundaryState
> {
  constructor(props: React.PropsWithChildren<{}>) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    this.setState({ errorInfo });
    
    // 报告 React 错误
    errorMonitor.captureError({
      message: error.message,
      stack: error.stack,
      level: 'error',
      category: 'react',
      extra: {
        componentStack: errorInfo.componentStack,
        errorBoundary: true
      }
    });
  }

  render() {
    if (this.state.hasError) {
      return (
        <div style={{ padding: '20px', textAlign: 'center' }}>
          <h2>出现了一些问题</h2>
          <p>页面遇到了错误，请刷新页面重试。</p>
          <button onClick={() => window.location.reload()}>
            刷新页面
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

// 错误监控 Hook
export function useErrorHandler() {
  return React.useCallback((error: Error, extra?: Record<string, any>) => {
    errorMonitor.captureException(error, extra);
  }, []);
}
