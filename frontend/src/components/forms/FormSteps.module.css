.stepContainer {
  padding: 0;
}

.stepHeader {
  text-align: center;
  margin-bottom: 32px;
}

.stepTitle {
  color: #000;
  font-size: 24px;
  font-weight: 500;
  margin-bottom: 8px;
  line-height: 1.3;
}

.stepDescription {
  color: #666;
  font-size: 16px;
  margin: 0;
  line-height: 1.5;
}

.inputIcon {
  color: #999;
}

/* 表单项样式 */
.stepContainer .ant-form-item-label > label {
  color: #000;
  font-weight: 500;
  font-size: 14px;
}

.stepContainer .ant-input,
.stepContainer .ant-select-selector,
.stepContainer .ant-input-number {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}

.stepContainer .ant-input:hover,
.stepContainer .ant-select-selector:hover,
.stepContainer .ant-input-number:hover {
  border-color: #000;
}

.stepContainer .ant-input:focus,
.stepContainer .ant-select-focused .ant-select-selector,
.stepContainer .ant-input-number-focused {
  border-color: #000;
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
}

.stepContainer .ant-checkbox-wrapper {
  color: #000;
}

.stepContainer .ant-checkbox-checked .ant-checkbox-inner {
  background-color: #000;
  border-color: #000;
}

.stepContainer .ant-checkbox:hover .ant-checkbox-inner {
  border-color: #000;
}

.stepContainer .ant-radio-wrapper {
  color: #000;
}

.stepContainer .ant-radio-checked .ant-radio-inner {
  border-color: #000;
}

.stepContainer .ant-radio-checked .ant-radio-inner::after {
  background-color: #000;
}

.stepContainer .ant-radio:hover .ant-radio-inner {
  border-color: #000;
}

/* 帮助提示框样式 */
.helpTip {
  margin-top: 24px;
  padding: 16px;
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 4px;
}

.helpTipContent {
  display: flex;
  align-items: flex-start;
}

.helpTipIcon {
  flex-shrink: 0;
  margin-right: 12px;
  color: #52c41a;
}

.helpTipText {
  flex: 1;
}

.helpTipTitle {
  color: #389e0d;
  font-size: 14px;
  font-weight: 500;
  margin: 0 0 8px 0;
}

.helpTipList {
  color: #52c41a;
  font-size: 14px;
}

.helpTipList ul {
  margin: 0;
  padding-left: 16px;
}

.helpTipList li {
  margin-bottom: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stepHeader {
    margin-bottom: 24px;
  }

  .stepTitle {
    font-size: 20px;
  }

  .stepDescription {
    font-size: 14px;
  }

  .helpTip {
    margin-top: 16px;
    padding: 12px;
  }

  .helpTipContent {
    flex-direction: column;
  }

  .helpTipIcon {
    margin-right: 0;
    margin-bottom: 8px;
  }
}
