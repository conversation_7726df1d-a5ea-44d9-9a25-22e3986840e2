.modal {
  border-radius: 12px !important;
}

.modal .ant-modal-content {
  border-radius: 12px !important;
  overflow: hidden !important;
}

.modal .ant-modal-body {
  padding: 0 !important;
}

.content {
  padding: 32px;
}

.header {
  text-align: center;
  margin-bottom: 24px;
}

.icon {
  font-size: 48px;
  color: #1890ff;
  margin-bottom: 16px;
  display: block;
}

.title {
  margin: 0 0 8px 0 !important;
  color: #333 !important;
  font-weight: 600 !important;
}

.subtitle {
  margin: 0 !important;
  color: #666 !important;
  font-size: 14px !important;
}

.features {
  margin-bottom: 24px;
}

.featureList {
  margin: 8px 0 0 0 !important;
  padding-left: 0 !important;
  list-style: none !important;
}

.featureList li {
  padding: 4px 0;
  font-size: 13px;
  color: #666;
}

.form {
  margin-bottom: 16px;
}

.form .ant-form-item {
  margin-bottom: 16px !important;
}

.form .ant-form-item-label > label {
  color: #333 !important;
  font-weight: 500 !important;
}

.form .ant-input {
  height: 40px !important;
  border-radius: 6px !important;
  border: 1px solid #e8e8e8 !important;
}

.form .ant-input:focus {
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

.agreement {
  margin-bottom: 24px !important;
}

.agreement .ant-checkbox-wrapper {
  font-size: 13px !important;
  color: #666 !important;
  line-height: 1.4 !important;
}

.link {
  padding: 0 2px !important;
  height: auto !important;
  font-size: 13px !important;
  color: #1890ff !important;
}

.link:hover {
  color: #40a9ff !important;
}

.actions {
  margin-bottom: 0 !important;
}

.buttonGroup {
  width: 100%;
  display: flex !important;
  justify-content: center !important;
}

.cancelButton {
  height: 40px !important;
  padding: 0 24px !important;
  border-radius: 6px !important;
  border: 1px solid #e8e8e8 !important;
  background: #fff !important;
  color: #666 !important;
  font-weight: 400 !important;
}

.cancelButton:hover {
  border-color: #d9d9d9 !important;
  color: #333 !important;
}

.submitButton {
  height: 40px !important;
  padding: 0 32px !important;
  border-radius: 6px !important;
  background: #1890ff !important;
  border-color: #1890ff !important;
  font-weight: 500 !important;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3) !important;
}

.submitButton:hover {
  background: #40a9ff !important;
  border-color: #40a9ff !important;
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.4) !important;
}

.submitButton:disabled {
  background: #f5f5f5 !important;
  border-color: #e8e8e8 !important;
  color: #bbb !important;
  box-shadow: none !important;
}

.footer {
  text-align: center;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.footerText {
  font-size: 13px !important;
  color: #666 !important;
}

.footerText .ant-btn-link {
  padding: 0 4px !important;
  height: auto !important;
  font-size: 13px !important;
  color: #1890ff !important;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .content {
    padding: 24px 20px;
  }
  
  .icon {
    font-size: 40px;
    margin-bottom: 12px;
  }
  
  .title {
    font-size: 20px !important;
  }
  
  .subtitle {
    font-size: 13px !important;
  }
  
  .features {
    margin-bottom: 20px;
  }
  
  .featureList li {
    font-size: 12px;
  }
  
  .form .ant-input {
    height: 36px !important;
  }
  
  .cancelButton,
  .submitButton {
    height: 36px !important;
    padding: 0 20px !important;
    font-size: 14px !important;
  }
  
  .buttonGroup {
    flex-direction: column !important;
    gap: 12px !important;
  }
  
  .buttonGroup .ant-btn {
    width: 100% !important;
  }
}
