.stateSwitcher {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

.header {
  text-align: center;
  margin-bottom: 32px;
}

.header h3 {
  margin-bottom: 8px;
  color: #262626;
}

.optionsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.optionCard {
  border-radius: 12px;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.optionCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.currentState {
  border-color: #1890ff !important;
  background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
}

.optionIcon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.optionTitle {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #262626;
}

.currentBadge {
  background: #1890ff;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.optionContent {
  margin-top: 8px;
}

.features {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.feature {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #595959;
}

.featureDot {
  color: #1890ff;
  font-weight: bold;
}

.userTypeSelect {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  transition: border-color 0.3s ease;
}

.userTypeSelect:focus {
  border-color: #1890ff;
  outline: none;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stateSwitcher {
    padding: 16px;
  }
  
  .optionsGrid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .header {
    margin-bottom: 24px;
  }
}

@media (max-width: 480px) {
  .optionCard {
    margin: 0 -8px;
  }
  
  .optionIcon {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }
}
