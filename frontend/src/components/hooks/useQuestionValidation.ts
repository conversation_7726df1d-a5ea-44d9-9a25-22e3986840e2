import { useCallback, useMemo } from 'react';
import type { QuestionnaireConfig, QuestionResponse, Question } from '../../types/questionnaire-engine';

interface ValidationResult {
  valid: boolean;
  errors: string[];
}

interface UseQuestionValidationReturn {
  validateQuestion: (question: Question, value: any) => ValidationResult;
  validateSection: (sectionId: string, responses: QuestionResponse[]) => ValidationResult;
  validateQuestionnaire: (responses: QuestionResponse[]) => ValidationResult;
  getValidationErrors: (sectionId?: string) => string[];
}

export const useQuestionValidation = (questionnaire: QuestionnaireConfig): UseQuestionValidationReturn => {
  
  // 验证单个问题
  const validateQuestion = useCallback((question: Question, value: any): ValidationResult => {
    const errors: string[] = [];

    // 必填验证
    if (question.required) {
      if (value === null || value === undefined || value === '') {
        errors.push(`${question.title}是必填项`);
      }
      
      // 数组类型的空值检查
      if (Array.isArray(value) && value.length === 0) {
        errors.push(`${question.title}至少需要选择一项`);
      }
    }

    // 如果没有值，跳过其他验证
    if (value === null || value === undefined || value === '') {
      return { valid: errors.length === 0, errors };
    }

    // 根据问题类型进行特定验证
    switch (question.type) {
      case 'text':
      case 'textarea':
        if (typeof value === 'string') {
          if (question.minLength && value.length < question.minLength) {
            errors.push(`${question.title}至少需要${question.minLength}个字符`);
          }
          if (question.maxLength && value.length > question.maxLength) {
            errors.push(`${question.title}不能超过${question.maxLength}个字符`);
          }
        }
        break;

      case 'number':
        if (typeof value === 'number') {
          if (question.min !== undefined && value < question.min) {
            errors.push(`${question.title}不能小于${question.min}`);
          }
          if (question.max !== undefined && value > question.max) {
            errors.push(`${question.title}不能大于${question.max}`);
          }
        }
        break;

      case 'email':
        if (typeof value === 'string') {
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (!emailRegex.test(value)) {
            errors.push(`${question.title}格式不正确`);
          }
        }
        break;

      case 'phone':
        if (typeof value === 'string') {
          const phoneRegex = /^1[3-9]\d{9}$/;
          if (!phoneRegex.test(value)) {
            errors.push(`${question.title}格式不正确`);
          }
        }
        break;

      case 'checkbox':
        if (Array.isArray(value)) {
          if (question.minSelect && value.length < question.minSelect) {
            errors.push(`${question.title}至少需要选择${question.minSelect}项`);
          }
          if (question.maxSelect && value.length > question.maxSelect) {
            errors.push(`${question.title}最多只能选择${question.maxSelect}项`);
          }
        }
        break;
    }

    // 自定义验证规则
    if (question.validation) {
      for (const rule of question.validation) {
        switch (rule.type) {
          case 'required':
            if (value === null || value === undefined || value === '' || 
                (Array.isArray(value) && value.length === 0)) {
              errors.push(rule.message || `${question.title}是必填项`);
            }
            break;

          case 'minLength':
            if (typeof value === 'string' && value.length < rule.value) {
              errors.push(rule.message || `${question.title}至少需要${rule.value}个字符`);
            }
            break;

          case 'maxLength':
            if (typeof value === 'string' && value.length > rule.value) {
              errors.push(rule.message || `${question.title}不能超过${rule.value}个字符`);
            }
            break;

          case 'min':
            if (typeof value === 'number' && value < rule.value) {
              errors.push(rule.message || `${question.title}不能小于${rule.value}`);
            }
            break;

          case 'max':
            if (typeof value === 'number' && value > rule.value) {
              errors.push(rule.message || `${question.title}不能大于${rule.value}`);
            }
            break;

          case 'pattern':
            if (typeof value === 'string') {
              const regex = new RegExp(rule.value);
              if (!regex.test(value)) {
                errors.push(rule.message || `${question.title}格式不正确`);
              }
            }
            break;

          case 'email':
            if (typeof value === 'string') {
              const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
              if (!emailRegex.test(value)) {
                errors.push(rule.message || `${question.title}邮箱格式不正确`);
              }
            }
            break;
        }
      }
    }

    return { valid: errors.length === 0, errors };
  }, []);

  // 验证整个节
  const validateSection = useCallback((sectionId: string, responses: QuestionResponse[]): ValidationResult => {
    const section = questionnaire.sections.find(s => s.id === sectionId);
    if (!section) {
      return { valid: false, errors: ['节不存在'] };
    }

    const allErrors: string[] = [];

    for (const question of section.questions) {
      const response = responses.find(r => r.questionId === question.id);
      const value = response?.value;
      
      const questionValidation = validateQuestion(question, value);
      if (!questionValidation.valid) {
        allErrors.push(...questionValidation.errors);
      }
    }

    return { valid: allErrors.length === 0, errors: allErrors };
  }, [questionnaire.sections, validateQuestion]);

  // 验证整个问卷
  const validateQuestionnaire = useCallback((responses: QuestionResponse[]): ValidationResult => {
    const allErrors: string[] = [];

    for (const section of questionnaire.sections) {
      const sectionResponses = responses.filter(r => r.sectionId === section.id);
      const sectionValidation = validateSection(section.id, sectionResponses);
      
      if (!sectionValidation.valid) {
        allErrors.push(...sectionValidation.errors);
      }
    }

    return { valid: allErrors.length === 0, errors: allErrors };
  }, [questionnaire.sections, validateSection]);

  // 获取验证错误信息
  const getValidationErrors = useCallback((sectionId?: string): string[] => {
    // 这里可以根据需要实现获取当前验证错误的逻辑
    // 暂时返回空数组，实际使用时可以结合状态管理
    return [];
  }, []);

  return {
    validateQuestion,
    validateSection,
    validateQuestionnaire,
    getValidationErrors
  };
};
