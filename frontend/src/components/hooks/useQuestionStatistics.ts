import { useState, useEffect, useCallback } from 'react';
import type { QuestionStatistics } from '../../types/questionnaire-engine';

interface UseQuestionStatisticsReturn {
  statistics: QuestionStatistics | null;
  loading: boolean;
  error: string | null;
  refetch: () => void;
}

export const useQuestionStatistics = (
  questionnaireId?: string,
  questionId?: string,
  enabled: boolean = true
): UseQuestionStatisticsReturn => {
  const [statistics, setStatistics] = useState<QuestionStatistics | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 获取统计数据
  const fetchStatistics = useCallback(async () => {
    if (!enabled || !questionnaireId || !questionId) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(
        `/api/questionnaire/statistics/${questionnaireId}?questionId=${encodeURIComponent(questionId)}`
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.success && data.data && data.data.length > 0) {
        const questionStats = data.data.find((stat: any) => 
          stat.questionId === questionId || stat.questionId === `${questionId}`
        );
        
        if (questionStats) {
          setStatistics(questionStats.data);
        } else {
          setStatistics(null);
        }
      } else {
        setStatistics(null);
      }
    } catch (err) {
      console.error('Error fetching question statistics:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch statistics');
      setStatistics(null);
    } finally {
      setLoading(false);
    }
  }, [questionnaireId, questionId, enabled]);

  // 手动重新获取数据
  const refetch = useCallback(() => {
    fetchStatistics();
  }, [fetchStatistics]);

  // 自动获取数据
  useEffect(() => {
    fetchStatistics();
  }, [fetchStatistics]);

  return {
    statistics,
    loading,
    error,
    refetch
  };
};
