import { useState, useCallback, useEffect } from 'react';

interface UseLocalStorageReturn<T> {
  storedValue: T | null;
  saveToStorage: (value: T) => void;
  loadFromStorage: () => T | null;
  clearStorage: () => void;
  updateStorage: (updater: (prev: T | null) => T) => void;
}

export const useLocalStorage = <T = any>(key: string): UseLocalStorageReturn<T> => {
  const [storedValue, setStoredValue] = useState<T | null>(null);

  // 从 localStorage 加载数据
  const loadFromStorage = useCallback((): T | null => {
    try {
      const item = window.localStorage.getItem(key);
      if (item) {
        const parsed = JSON.parse(item);
        setStoredValue(parsed);
        return parsed;
      }
      return null;
    } catch (error) {
      console.error(`Error loading from localStorage key "${key}":`, error);
      return null;
    }
  }, [key]);

  // 保存数据到 localStorage
  const saveToStorage = useCallback((value: T) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);
      window.localStorage.setItem(key, JSON.stringify(valueToStore));
    } catch (error) {
      console.error(`Error saving to localStorage key "${key}":`, error);
    }
  }, [key, storedValue]);

  // 清除 localStorage 中的数据
  const clearStorage = useCallback(() => {
    try {
      window.localStorage.removeItem(key);
      setStoredValue(null);
    } catch (error) {
      console.error(`Error clearing localStorage key "${key}":`, error);
    }
  }, [key]);

  // 更新 localStorage 中的数据
  const updateStorage = useCallback((updater: (prev: T | null) => T) => {
    try {
      const currentValue = loadFromStorage();
      const newValue = updater(currentValue);
      saveToStorage(newValue);
    } catch (error) {
      console.error(`Error updating localStorage key "${key}":`, error);
    }
  }, [key, loadFromStorage, saveToStorage]);

  // 组件挂载时加载数据
  useEffect(() => {
    loadFromStorage();
  }, [loadFromStorage]);

  return {
    storedValue,
    saveToStorage,
    loadFromStorage,
    clearStorage,
    updateStorage
  };
};
