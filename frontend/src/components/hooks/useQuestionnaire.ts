import { useState, useCallback, useMemo } from 'react';
import type { QuestionResponse } from '../../types/questionnaire-engine';

interface UseQuestionnaireReturn {
  responses: QuestionResponse[];
  updateResponse: (questionId: string, value: any, sectionId: string) => void;
  clearResponses: () => void;
  getResponsesForSection: (sectionId: string) => QuestionResponse[];
  getAllResponses: () => QuestionResponse[];
  getResponseValue: (questionId: string) => any;
  hasResponse: (questionId: string) => boolean;
}

export const useQuestionnaire = (questionnaireId: string): UseQuestionnaireReturn => {
  const [responses, setResponses] = useState<QuestionResponse[]>([]);

  // 更新问题响应
  const updateResponse = useCallback((questionId: string, value: any, sectionId: string) => {
    setResponses(prev => {
      const existingIndex = prev.findIndex(r => r.questionId === questionId);
      const newResponse: QuestionResponse = {
        questionId,
        questionTitle: '', // 这里可以从问卷配置中获取
        questionType: '', // 这里可以从问卷配置中获取
        value,
        sectionId,
        answeredAt: new Date().toISOString()
      };

      if (existingIndex >= 0) {
        // 更新现有响应
        const newResponses = [...prev];
        newResponses[existingIndex] = { ...newResponses[existingIndex], ...newResponse };
        return newResponses;
      } else {
        // 添加新响应
        return [...prev, newResponse];
      }
    });
  }, []);

  // 清空所有响应
  const clearResponses = useCallback(() => {
    setResponses([]);
  }, []);

  // 获取指定节的响应
  const getResponsesForSection = useCallback((sectionId: string) => {
    return responses.filter(r => r.sectionId === sectionId);
  }, [responses]);

  // 获取所有响应
  const getAllResponses = useCallback(() => {
    return responses;
  }, [responses]);

  // 获取指定问题的响应值
  const getResponseValue = useCallback((questionId: string) => {
    const response = responses.find(r => r.questionId === questionId);
    return response?.value;
  }, [responses]);

  // 检查是否有指定问题的响应
  const hasResponse = useCallback((questionId: string) => {
    return responses.some(r => r.questionId === questionId && r.value !== null && r.value !== undefined);
  }, [responses]);

  return {
    responses,
    updateResponse,
    clearResponses,
    getResponsesForSection,
    getAllResponses,
    getResponseValue,
    hasResponse
  };
};
