import { useState, useCallback } from 'react';

interface UseProgressReturn {
  progress: number;
  updateProgress: (newProgress: number) => void;
  resetProgress: () => void;
  incrementProgress: (amount: number) => void;
  setProgressByStep: (currentStep: number, totalSteps: number) => void;
}

export const useProgress = (totalSteps: number = 1): UseProgressReturn => {
  const [progress, setProgress] = useState(0);

  // 更新进度
  const updateProgress = useCallback((newProgress: number) => {
    const clampedProgress = Math.max(0, Math.min(100, newProgress));
    setProgress(clampedProgress);
  }, []);

  // 重置进度
  const resetProgress = useCallback(() => {
    setProgress(0);
  }, []);

  // 增加进度
  const incrementProgress = useCallback((amount: number) => {
    setProgress(prev => {
      const newProgress = prev + amount;
      return Math.max(0, Math.min(100, newProgress));
    });
  }, []);

  // 根据步骤设置进度
  const setProgressByStep = useCallback((currentStep: number, totalSteps: number) => {
    if (totalSteps <= 0) return;
    const stepProgress = (currentStep / totalSteps) * 100;
    updateProgress(stepProgress);
  }, [updateProgress]);

  return {
    progress,
    updateProgress,
    resetProgress,
    incrementProgress,
    setProgressByStep
  };
};
