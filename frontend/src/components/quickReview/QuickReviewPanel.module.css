/* 快速审核面板样式 */

.container {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
  min-height: calc(100vh - 64px);
  background: #f5f5f5;
}

/* 头部工具栏 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.headerLeft {
  display: flex;
  align-items: center;
  gap: 16px;
}

.title {
  margin: 0 !important;
  display: flex;
  align-items: center;
}

.progressTag {
  font-size: 14px;
  font-weight: 500;
}

.headerRight {
  display: flex;
  align-items: center;
}

/* 进度条区域 */
.progressSection {
  margin-bottom: 24px;
  padding: 16px 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.progressHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

/* 内容卡片 */
.contentCard {
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  min-height: 400px;
}

.contentHeader {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.contentMeta {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.contentBody {
  min-height: 300px;
}

.contentTitle {
  margin-bottom: 16px !important;
  color: #262626;
  font-weight: 600;
}

.contentText {
  font-size: 16px;
  line-height: 1.6;
  color: #262626;
  margin-bottom: 20px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
  border-left: 4px solid #1890ff;
  white-space: pre-wrap;
  word-break: break-word;
}

.metadata {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

/* 操作按钮 */
.actionButtons {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.undoSection {
  display: flex;
  justify-content: center;
  margin-bottom: 24px;
}

/* 加载状态 */
.loadingCard {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.loadingContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

/* 设置项 */
.settingItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.settingItem:last-child {
  margin-bottom: 0;
}

/* 快捷键帮助 */
.shortcutHelp {
  max-height: 500px;
  overflow-y: auto;
}

.shortcutCategory {
  margin-bottom: 24px;
}

.shortcutCategory:last-child {
  margin-bottom: 0;
}

.shortcutList {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.shortcutItem {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  background: #fafafa;
  border-radius: 6px;
}

.shortcutKey {
  min-width: 60px;
  text-align: center;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-weight: 600;
  background: #262626 !important;
  color: white !important;
  border: none !important;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .container {
    padding: 16px;
  }
  
  .header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .headerLeft {
    justify-content: center;
  }
  
  .headerRight {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 12px;
  }
  
  .actionButtons {
    padding: 16px;
  }
  
  .actionButtons .ant-space {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .contentText {
    font-size: 14px;
    padding: 12px;
  }
  
  .contentMeta {
    justify-content: center;
  }
  
  .metadata {
    justify-content: center;
  }
}

/* 动画效果 */
.contentCard {
  transition: all 0.3s ease;
}

.contentCard:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

.actionButtons .ant-btn {
  transition: all 0.2s ease;
}

.actionButtons .ant-btn:hover {
  transform: translateY(-1px);
}

.actionButtons .ant-btn:active {
  transform: translateY(0);
}

/* 进度条自定义样式 */
.progressSection .ant-progress-bg {
  background: linear-gradient(90deg, #1890ff 0%, #40a9ff 100%);
}

/* 统计卡片样式 */
.ant-statistic-title {
  font-size: 12px;
  color: #8c8c8c;
}

.ant-statistic-content {
  font-size: 20px;
  font-weight: 600;
}

/* 标签样式增强 */
.progressTag {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  border: none;
  color: white;
  font-weight: 600;
}

/* 内容卡片状态指示 */
.contentCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #1890ff, #40a9ff);
  border-radius: 8px 8px 0 0;
}

/* 风险等级颜色 */
.ant-tag {
  border-radius: 4px;
  font-weight: 500;
}

/* 按钮组样式优化 */
.actionButtons .ant-btn-primary {
  background: linear-gradient(135deg, #52c41a, #73d13d);
  border: none;
  font-weight: 600;
}

.actionButtons .ant-btn-danger {
  background: linear-gradient(135deg, #ff4d4f, #ff7875);
  border: none;
  font-weight: 600;
}

/* 撤销按钮样式 */
.undoSection .ant-btn-link {
  color: #8c8c8c;
  font-size: 13px;
}

.undoSection .ant-btn-link:hover {
  color: #1890ff;
}

/* 模态框样式优化 */
.ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 24px;
}

.ant-modal-body {
  padding: 24px;
}

/* 快捷键帮助特殊样式 */
.shortcutHelp .ant-typography h5 {
  color: #1890ff;
  font-weight: 600;
  margin-bottom: 12px;
}
