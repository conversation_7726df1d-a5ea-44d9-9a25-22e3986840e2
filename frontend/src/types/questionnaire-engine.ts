// 问卷引擎类型定义

export interface QuestionnaireConfig {
  id: string;
  title: string;
  description?: string;
  sections: Section[];
  config: QuestionnaireSettings;
  version?: string;
  status?: 'active' | 'inactive' | 'draft';
}

export interface Section {
  id: string;
  title: string;
  description?: string;
  questions: Question[];
  conditions?: Condition[];
}

export interface Question {
  id: string;
  type: QuestionType;
  title: string;
  description?: string;
  required?: boolean;
  placeholder?: string;
  options?: QuestionOption[];
  validation?: ValidationRule[];
  conditions?: Condition[];
  
  // 数字类型特有属性
  min?: number;
  max?: number;
  step?: number;
  
  // 文本类型特有属性
  minLength?: number;
  maxLength?: number;
  rows?: number;
  
  // 选择类型特有属性
  minSelect?: number;
  maxSelect?: number;
  
  // 评分类型特有属性
  allowHalf?: boolean;
  
  // 滑块类型特有属性
  marks?: Record<number, string>;
}

export type QuestionType = 
  | 'text'
  | 'textarea'
  | 'number'
  | 'email'
  | 'phone'
  | 'url'
  | 'password'
  | 'select'
  | 'radio'
  | 'checkbox'
  | 'date'
  | 'time'
  | 'datetime'
  | 'rating'
  | 'slider'
  | 'file'
  | 'image'
  | 'location'
  | 'signature';

export interface QuestionOption {
  value: string | number;
  label: string;
  description?: string;
  disabled?: boolean;
}

export interface ValidationRule {
  type: 'required' | 'minLength' | 'maxLength' | 'min' | 'max' | 'pattern' | 'email' | 'url' | 'custom';
  value?: any;
  message: string;
  customValidator?: (value: any) => boolean;
}

export interface Condition {
  questionId: string;
  operator: 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'greater_than' | 'less_than';
  value: any;
  logic?: 'and' | 'or';
}

export interface QuestionnaireSettings {
  allowAnonymous?: boolean;
  showStatistics?: boolean;
  autoSave?: boolean;
  theme?: string;
  submitButtonText?: string;
  progressType?: 'steps' | 'percentage' | 'none';
  allowBack?: boolean;
  showProgress?: boolean;
  maxAttempts?: number;
  timeLimit?: number;
}

// 响应相关类型
export interface QuestionnaireResponse {
  questionnaireId: string;
  sectionResponses: SectionResponse[];
  metadata: ResponseMetadata;
}

export interface SectionResponse {
  sectionId: string;
  sectionTitle?: string;
  questionResponses: QuestionResponse[];
  completedAt?: string;
}

export interface QuestionResponse {
  questionId: string;
  questionTitle?: string;
  questionType?: string;
  value: any;
  sectionId?: string;
  answeredAt: string;
}

export interface ResponseMetadata {
  responseId?: string;
  isAnonymous?: boolean;
  startedAt?: string;
  completedAt?: string;
  submittedAt?: string;
  userAgent?: string;
  ipAddress?: string;
  source?: string;
  version?: string;
  userId?: string;
}

// 统计相关类型
export interface QuestionStatistics {
  questionId: string;
  questionType: QuestionType;
  totalResponses: number;
  distribution: Record<string, number>;
  averageValue?: number;
  medianValue?: number;
  lastUpdated: string;
}

export interface QuestionnaireStatistics {
  questionnaireId: string;
  totalResponses: number;
  completionRate: number;
  averageCompletionTime: number;
  questionStatistics: QuestionStatistics[];
  lastUpdated: string;
}

// 提交相关类型
export interface SubmissionResult {
  success: boolean;
  responseId?: string;
  message?: string;
  errors?: string[];
}

// 配置加载相关类型
export interface QuestionnaireConfigResponse {
  success: boolean;
  data?: QuestionnaireConfig;
  message?: string;
  error?: string;
}

export interface QuestionnaireStatisticsResponse {
  success: boolean;
  data?: QuestionStatistics[];
  message?: string;
  error?: string;
}

// 进度相关类型
export interface ProgressInfo {
  current: number;
  total: number;
  percentage: number;
  currentSection?: string;
  completedSections?: string[];
}

// 验证相关类型
export interface ValidationResult {
  valid: boolean;
  errors: string[];
  warnings?: string[];
}

// 本地存储相关类型
export interface StoredQuestionnaireData {
  responses: QuestionResponse[];
  currentSectionIndex: number;
  timestamp: number;
  version?: string;
}
