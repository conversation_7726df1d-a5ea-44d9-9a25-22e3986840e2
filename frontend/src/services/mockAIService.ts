/**
 * 模拟AI服务
 * 
 * 为演示提供模拟的AI管理和审核数据
 */

import type {
  AISource,
  SystemMetrics,
  CostControlConfig,
  AIReviewAnalysis,
  ReviewContentType
} from '../types/ai-water-management';

class MockAIService {
  // 模拟AI水源数据
  private mockSources: AISource[] = [
    {
      id: 'openai-gpt4',
      name: 'OpenAI GPT-4',
      type: 'primary',
      provider: 'openai',
      status: 'active',
      config: {
        apiKey: '***',
        endpoint: 'https://api.openai.com/v1/chat/completions',
        model: 'gpt-4',
        maxConcurrent: 10,
        rateLimit: 60,
        costPerToken: 0.00003,
        timeout: 30000,
        retryAttempts: 3,
        retryDelay: 1000
      },
      features: {
        streaming: true,
        functionCalling: true,
        imageGeneration: false,
        codeExecution: false,
        multimodal: true
      },
      limits: {
        maxTokensPerRequest: 4096,
        maxRequestsPerMinute: 60,
        maxTokensPerMinute: 60000,
        maxRequestsPerDay: 86400
      },
      health: {
        lastCheck: new Date().toISOString(),
        responseTime: 1200,
        successRate: 98.5,
        errorCount: 12,
        uptime: 99.2,
        totalChecks: 1440,
        healthyChecks: 1428,
        issues: []
      },
      usage: {
        requestsToday: 1250,
        tokensUsed: 125000,
        costToday: 3.75,
        lastUsed: new Date().toISOString(),
        monthlyRequests: 35000,
        monthlyTokens: 3500000,
        monthlyCost: 105.00
      },
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: new Date().toISOString()
    },
    {
      id: 'grok-beta',
      name: 'Grok Beta',
      type: 'secondary',
      provider: 'grok',
      status: 'active',
      config: {
        apiKey: '***',
        endpoint: 'https://api.x.ai/v1/chat/completions',
        model: 'grok-beta',
        maxConcurrent: 5,
        rateLimit: 30,
        costPerToken: 0.00002,
        timeout: 25000,
        retryAttempts: 3,
        retryDelay: 1000
      },
      features: {
        streaming: true,
        functionCalling: false,
        imageGeneration: false,
        codeExecution: false,
        multimodal: false
      },
      limits: {
        maxTokensPerRequest: 2048,
        maxRequestsPerMinute: 30,
        maxTokensPerMinute: 30000,
        maxRequestsPerDay: 43200
      },
      health: {
        lastCheck: new Date().toISOString(),
        responseTime: 1800,
        successRate: 95.2,
        errorCount: 28,
        uptime: 97.8,
        totalChecks: 1440,
        healthyChecks: 1412,
        issues: ['偶发性超时']
      },
      usage: {
        requestsToday: 680,
        tokensUsed: 68000,
        costToday: 1.36,
        lastUsed: new Date().toISOString(),
        monthlyRequests: 18000,
        monthlyTokens: 1800000,
        monthlyCost: 36.00
      },
      createdAt: '2024-01-15T00:00:00Z',
      updatedAt: new Date().toISOString()
    },
    {
      id: 'claude-3',
      name: 'Claude 3 Sonnet',
      type: 'backup',
      provider: 'claude',
      status: 'inactive',
      config: {
        apiKey: '***',
        endpoint: 'https://api.anthropic.com/v1/messages',
        model: 'claude-3-sonnet-20240229',
        maxConcurrent: 3,
        rateLimit: 20,
        costPerToken: 0.000015,
        timeout: 35000,
        retryAttempts: 3,
        retryDelay: 1000
      },
      features: {
        streaming: true,
        functionCalling: false,
        imageGeneration: false,
        codeExecution: false,
        multimodal: true
      },
      limits: {
        maxTokensPerRequest: 4096,
        maxRequestsPerMinute: 20,
        maxTokensPerMinute: 20000,
        maxRequestsPerDay: 28800
      },
      health: {
        lastCheck: new Date().toISOString(),
        responseTime: 2200,
        successRate: 96.8,
        errorCount: 15,
        uptime: 98.5,
        totalChecks: 1440,
        healthyChecks: 1425,
        issues: []
      },
      usage: {
        requestsToday: 0,
        tokensUsed: 0,
        costToday: 0,
        lastUsed: '2024-01-20T10:30:00Z',
        monthlyRequests: 5000,
        monthlyTokens: 500000,
        monthlyCost: 7.50
      },
      createdAt: '2024-01-10T00:00:00Z',
      updatedAt: new Date().toISOString()
    }
  ];

  // 模拟系统指标
  private mockMetrics: SystemMetrics = {
    overview: {
      totalSources: 3,
      activeSources: 2,
      totalRequests: 1930,
      totalCost: 5.11,
      averageResponseTime: 1500,
      systemHealth: 97.8
    },
    sources: {
      'openai-gpt4': {
        status: 'active',
        health: 99.2,
        requestCount: 1250,
        cost: 3.75,
        responseTime: 1200,
        errorRate: 0.015
      },
      'grok-beta': {
        status: 'active',
        health: 97.8,
        requestCount: 680,
        cost: 1.36,
        responseTime: 1800,
        errorRate: 0.048
      },
      'claude-3': {
        status: 'inactive',
        health: 98.5,
        requestCount: 0,
        cost: 0,
        responseTime: 2200,
        errorRate: 0.032
      }
    },
    performance: {
      requestsPerMinute: 45,
      tokensPerMinute: 4500,
      costPerMinute: 0.135,
      errorRate: 0.025,
      uptime: 98.5
    },
    costs: {
      today: 5.11,
      thisMonth: 148.50,
      budgetUtilization: {
        daily: 0.34,
        monthly: 0.42
      },
      breakdown: {
        openai: 105.00,
        grok: 36.00,
        gemini: 0,
        claude: 7.50
      }
    }
  };

  // 模拟成本控制配置
  private mockCostConfig: CostControlConfig = {
    dailyBudget: 15.00,
    monthlyBudget: 350.00,
    alertThresholds: {
      daily: 12.00,
      monthly: 280.00
    },
    costOptimization: {
      enabled: true,
      autoSwitchToLowerCost: true,
      costThresholdMultiplier: 1.5,
      qualityMinimumThreshold: 0.8
    },
    billing: {
      currency: 'USD',
      trackingEnabled: true,
      reportingInterval: 'daily',
      costBreakdownByTerminal: true
    }
  };

  /**
   * 获取所有AI水源
   */
  async getAllSources(): Promise<AISource[]> {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500));
    return [...this.mockSources];
  }

  /**
   * 获取系统指标
   */
  async getSystemMetrics(): Promise<SystemMetrics> {
    await new Promise(resolve => setTimeout(resolve, 300));
    return { ...this.mockMetrics };
  }

  /**
   * 获取成本控制配置
   */
  async getCostControlConfig(): Promise<CostControlConfig> {
    await new Promise(resolve => setTimeout(resolve, 200));
    return { ...this.mockCostConfig };
  }

  /**
   * 模拟AI审核分析
   */
  async analyzeContent(contentId: string, contentType: ReviewContentType): Promise<AIReviewAnalysis> {
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 根据内容类型生成不同的分析结果
    const baseAnalysis = {
      contentId,
      contentType,
      analysis: {
        qualityScore: 85 + Math.random() * 10,
        sentimentScore: 0.3 + Math.random() * 0.4,
        toxicityScore: Math.random() * 0.2,
        relevanceScore: 0.8 + Math.random() * 0.2,
        readabilityScore: 80 + Math.random() * 15
      },
      flags: {
        hasSensitiveContent: Math.random() < 0.1,
        hasPersonalInfo: Math.random() < 0.2,
        hasInappropriateLanguage: Math.random() < 0.05,
        hasSpam: Math.random() < 0.03,
        hasOffTopic: Math.random() < 0.1
      },
      suggestions: {
        recommendation: 'approve' as const,
        confidence: 0.85 + Math.random() * 0.1,
        reasons: [
          '内容结构完整，信息详实',
          '表达清晰，逻辑性强',
          '情感积极正面',
          '与主题高度相关'
        ],
        improvements: []
      },
      metadata: {
        aiSource: 'openai',
        model: 'gpt-4',
        analysisTime: 1200 + Math.random() * 800,
        cost: 0.002 + Math.random() * 0.003,
        timestamp: new Date().toISOString()
      }
    };

    // 根据随机性调整建议
    if (baseAnalysis.analysis.qualityScore < 70 || baseAnalysis.analysis.toxicityScore > 0.3) {
      baseAnalysis.suggestions.recommendation = 'review_required';
      baseAnalysis.suggestions.confidence = 0.6 + Math.random() * 0.2;
      baseAnalysis.suggestions.reasons = [
        '内容质量需要进一步评估',
        '检测到潜在问题',
        '建议人工仔细审核'
      ];
    }

    return baseAnalysis;
  }

  /**
   * 测试AI水源连接
   */
  async testSourceConnection(sourceId: string): Promise<{
    success: boolean;
    responseTime: number;
    error?: string;
  }> {
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const source = this.mockSources.find(s => s.id === sourceId);
    if (!source) {
      return {
        success: false,
        responseTime: 0,
        error: '水源不存在'
      };
    }

    // 模拟连接测试结果
    const success = Math.random() > 0.1; // 90% 成功率
    return {
      success,
      responseTime: success ? 800 + Math.random() * 1000 : 0,
      error: success ? undefined : '连接超时'
    };
  }
}

// 导出单例实例
export const mockAIService = new MockAIService();
export default mockAIService;
