import ApiService from './api';
import type { QuestionnaireResponse, PaginatedResponse, PaginationParams } from '../types';
import type { QuestionnaireFormData } from '../types/questionnaire';
import type {
  QuestionnaireResponse as NewQuestionnaireResponse,
  QuestionnaireConfig,
  QuestionStatistics
} from '../types/questionnaire-engine';

export class QuestionnaireService {
  // 提交问卷 (旧版本，保持兼容性)
  static async submitQuestionnaire(data: QuestionnaireFormData): Promise<any> {
    return ApiService.post<any>('/questionnaire', data);
  }

  // 提交问卷 (新版本)
  static async submitQuestionnaireV2(data: NewQuestionnaireResponse): Promise<any> {
    return ApiService.post<any>('/questionnaire', data);
  }

  // 获取问卷列表
  static async getQuestionnaires(params: PaginationParams & {
    status?: string;
    userId?: string;
    startDate?: string;
    endDate?: string;
  }): Promise<PaginatedResponse<QuestionnaireResponse>> {
    return ApiService.get<PaginatedResponse<QuestionnaireResponse>>('/questionnaire', { params });
  }

  // 获取单个问卷
  static async getQuestionnaire(id: string): Promise<QuestionnaireResponse> {
    return ApiService.get<QuestionnaireResponse>(`/questionnaire/${id}`);
  }

  // 更新问卷状态
  static async updateQuestionnaireStatus(id: string, status: 'approved' | 'rejected', comment?: string): Promise<QuestionnaireResponse> {
    return ApiService.put<QuestionnaireResponse>(`/questionnaire/${id}`, { status, comment });
  }

  // 删除问卷
  static async deleteQuestionnaire(id: string): Promise<void> {
    return ApiService.delete<void>(`/questionnaire/${id}`);
  }

  // 批量操作
  static async batchUpdateStatus(ids: string[], status: 'approved' | 'rejected', comment?: string): Promise<void> {
    return ApiService.post<void>('/questionnaire/batch-update', { ids, status, comment });
  }

  // 导出数据
  static async exportData(params: {
    format: 'csv' | 'excel';
    status?: string;
    startDate?: string;
    endDate?: string;
  }): Promise<Blob> {
    const response = await ApiService.get<Blob>('/questionnaire/export', {
      params,
      responseType: 'blob'
    });
    return response;
  }

  // 新版本API方法

  // 获取问卷配置
  static async getQuestionnaireConfig(questionnaireId: string): Promise<QuestionnaireConfig> {
    const response = await ApiService.get<any>(`/questionnaire/config/${questionnaireId}`);
    return response.data;
  }

  // 获取问卷统计数据
  static async getQuestionnaireStatistics(questionnaireId: string, questionId?: string): Promise<QuestionStatistics[]> {
    const params = questionId ? { questionId } : {};
    const response = await ApiService.get<any>(`/questionnaire/statistics/${questionnaireId}`, { params });
    return response.data;
  }



  // 数据格式转换工具
  static convertLegacyData(legacyData: QuestionnaireFormData): NewQuestionnaireResponse {
    return {
      questionnaireId: 'employment-survey-v1',
      sectionResponses: [
        {
          sectionId: 'personal',
          sectionTitle: '个人信息',
          questionResponses: Object.entries(legacyData.personalInfo || {}).map(([key, value]) => ({
            questionId: key,
            questionTitle: key,
            questionType: 'text',
            value: value,
            answeredAt: new Date().toISOString()
          })),
          completedAt: new Date().toISOString()
        },
        {
          sectionId: 'education',
          sectionTitle: '教育背景',
          questionResponses: Object.entries(legacyData.educationInfo || {}).map(([key, value]) => ({
            questionId: key,
            questionTitle: key,
            questionType: 'text',
            value: value,
            answeredAt: new Date().toISOString()
          })),
          completedAt: new Date().toISOString()
        },
        {
          sectionId: 'employment',
          sectionTitle: '就业意向',
          questionResponses: Object.entries(legacyData.employmentInfo || {}).map(([key, value]) => ({
            questionId: key,
            questionTitle: key,
            questionType: 'text',
            value: value,
            answeredAt: new Date().toISOString()
          })),
          completedAt: new Date().toISOString()
        }
      ],
      metadata: {
        responseId: `legacy_${Date.now()}`,
        isAnonymous: true,
        startedAt: new Date().toISOString(),
        completedAt: new Date().toISOString(),
        submittedAt: new Date().toISOString(),
        source: 'legacy-conversion',
        version: '1.0.0'
      }
    };
  }
}
