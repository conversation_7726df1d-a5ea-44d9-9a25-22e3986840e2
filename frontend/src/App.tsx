import React, { Suspense, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider, Spin } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { ErrorBoundary } from './components/common/ErrorBoundary';
import { performanceMonitor } from './utils/performanceMonitor';
import { cacheManager } from './utils/cacheManager';
import { useManagementAuthStore } from './stores/managementAuthStore';
import { AccessibilityProvider, AccessibilityShortcuts, SkipToContent } from './components/accessibility/AccessibilityProvider';
import { antdTheme } from './styles/antd-theme';
import {
  PublicRouteGuard,
  UserRouteGuard,
  ReviewerRouteGuard,
  AdminRouteGuard
} from './components/auth/RouteGuard';
import {
  AdminRouteGuard as NewAdminRouteGuard,
  ReviewerRouteGuard as NewReviewerRouteGuard
} from './components/auth/ManagementRouteGuard';
import { GlobalSemiAnonymousLogin } from './components/auth/GlobalSemiAnonymousLogin';
import { QuestionnaireLayout } from './components/layout/QuestionnaireLayout';
import { FloatingUserPanel } from './components/common/FloatingUserPanel';
import './styles/global.css';

// 懒加载组件
const HomePage = React.lazy(() => import('./pages/public/HomePage').then(module => ({ default: module.HomePage })));
const QuestionnairePage = React.lazy(() => import('./pages/public/QuestionnairePage').then(module => ({ default: module.QuestionnairePage })));
const ResultsPage = React.lazy(() => import('./pages/public/ResultsPage').then(module => ({ default: module.ResultsPage })));
const StoriesPage = React.lazy(() => import('./pages/public/StoriesPage').then(module => ({ default: module.StoriesPage })));
const VoicesPage = React.lazy(() => import('./pages/public/VoicesPage').then(module => ({ default: module.VoicesPage })));

const AdminLoginPage = React.lazy(() => import('./pages/auth/AdminLoginPage').then(module => ({ default: module.AdminLoginPage })));
const DashboardPage = React.lazy(() => import('./pages/admin/DashboardPage').then(module => ({ default: module.DashboardPage })));
const ContentManagementPage = React.lazy(() => import('./pages/admin/ContentManagementPage').then(module => ({ default: module.ContentManagementPage })));
const UserManagementPage = React.lazy(() => import('./pages/admin/UserManagementPage').then(module => ({ default: module.UserManagementPage })));
const SystemManagementPage = React.lazy(() => import('./pages/admin/SystemManagementPage').then(module => ({ default: module.SystemManagementPage })));
const PerformanceMonitorPage = React.lazy(() => import('./pages/admin/PerformanceMonitorPage').then(module => ({ default: module.PerformanceMonitorPage })));
const ApiDataPage = React.lazy(() => import('./pages/admin/ApiDataPage').then(module => ({ default: module.ApiDataPage })));
const ReviewerManagementPage = React.lazy(() => import('./pages/admin/ReviewerManagementPage').then(module => ({ default: module.ReviewerManagementPage })));
const SystemLogsPage = React.lazy(() => import('./pages/admin/SystemLogsPage').then(module => ({ default: module.SystemLogsPage })));
const DataGeneratorPage = React.lazy(() => import('./pages/admin/DataGeneratorPage').then(module => ({ default: module.DataGeneratorPage })));

// AI管理页面
const AISourcesPage = React.lazy(() => import('./pages/admin/ai/AISourcesPage').then(module => ({ default: module.AISourcesPage })));
const AIMonitorPage = React.lazy(() => import('./pages/admin/ai/AIMonitorPage').then(module => ({ default: module.AIMonitorPage })));
const AICostControlPage = React.lazy(() => import('./pages/admin/ai/AICostControlPage').then(module => ({ default: module.AICostControlPage })));
const AIReviewAssistantPage = React.lazy(() => import('./pages/admin/ai/AIReviewAssistantPage').then(module => ({ default: module.AIReviewAssistantPage })));

// 测试页面
const StateTest = React.lazy(() => import('./pages/test/StateTest').then(module => ({ default: module.StateTest })));

// 演示页面
const AIDemoPage = React.lazy(() => import('./pages/demo/AIDemoPage'));

// 审核员页面
const ReviewerDashboard = React.lazy(() => import('./pages/reviewer/ReviewerDashboard').then(module => ({ default: module.ReviewerDashboard })));
const QuestionnaireReviewPage = React.lazy(() => import('./pages/reviewer/QuestionnaireReviewPageV2').then(module => ({ default: module.QuestionnaireReviewPageV2 })));
const StoryReviewPage = React.lazy(() => import('./pages/reviewer/StoryReviewPage').then(module => ({ default: module.StoryReviewPage })));
const VoiceReviewPage = React.lazy(() => import('./pages/reviewer/VoiceReviewPage').then(module => ({ default: module.VoiceReviewPage })));
const ReviewHistoryPage = React.lazy(() => import('./pages/reviewer/ReviewHistoryPage').then(module => ({ default: module.ReviewHistoryPage })));
const QuickReviewPage = React.lazy(() => import('./pages/reviewer/QuickReviewPage').then(module => ({ default: module.QuickReviewPage })));

// 测试页面
const TestPermissionPage = React.lazy(() => import('./pages/test/PermissionTestPage').then(module => ({ default: module.PermissionTestPage })));
const AnalyticsPage = React.lazy(() => import('./pages/analytics/AnalyticsPageV2').then(module => ({ default: module.AnalyticsPageV2 })));
const AdvancedAnalyticsPage = React.lazy(() => import('./pages/analytics/AdvancedAnalyticsPage').then(module => ({ default: module.AdvancedAnalyticsPage })));
const AuthDebugPage = React.lazy(() => import('./pages/debug/AuthDebugPage').then(module => ({ default: module.AuthDebugPage })));
const PermissionTestPage = React.lazy(() => import('./pages/debug/PermissionTestPage').then(module => ({ default: module.PermissionTestPage })));
const AuthSystemTestPage = React.lazy(() => import('./pages/debug/AuthSystemTestPage').then(module => ({ default: module.AuthSystemTestPage })));
const NewAdminLoginPage = React.lazy(() => import('./pages/auth/NewAdminLoginPage').then(module => ({ default: module.NewAdminLoginPage })));
const SimpleAdminTestPage = React.lazy(() => import('./pages/debug/SimpleAdminTestPage').then(module => ({ default: module.SimpleAdminTestPage })));
const TestPage = React.lazy(() => import('./pages/TestPage').then(module => ({ default: module.TestPage })));

// 加载中组件
const LoadingSpinner = () => (
  <div className="loading-spinner">
    <Spin size="large" />
  </div>
);

function App() {
  const { initializeAuth } = useManagementAuthStore();

  useEffect(() => {
    // 初始化性能监控
    performanceMonitor.reportMetrics();

    // 清理过期缓存
    cacheManager.cleanup();

    // 初始化管理员认证状态
    initializeAuth();

    // 在开发环境中显示性能信息
    if (process.env.NODE_ENV === 'development') {
      console.log('Performance Monitor initialized');
      console.log('Cache Manager initialized');
      console.log('Management Auth initialized');
    }
  }, [initializeAuth]);

  return (
    <ErrorBoundary>
      <AccessibilityProvider>
        <ConfigProvider
          locale={zhCN}
          theme={antdTheme}
        >
          <Router
            future={{
              v7_startTransition: true,
              v7_relativeSplatPath: true
            }}
          >
            <SkipToContent />
            <AccessibilityShortcuts />
            <div className="App">
              <Suspense fallback={<LoadingSpinner />}>
                <main id="main-content">
                  <Routes>
                    {/* 公开路由 - 无需权限，使用问卷布局 */}
                    <Route path="/" element={
                      <PublicRouteGuard>
                        <QuestionnaireLayout>
                          <HomePage />
                          <FloatingUserPanel />
                        </QuestionnaireLayout>
                      </PublicRouteGuard>
                    } />
                    <Route path="/analytics" element={
                      <PublicRouteGuard>
                        <QuestionnaireLayout>
                          <AnalyticsPage />
                          <FloatingUserPanel />
                        </QuestionnaireLayout>
                      </PublicRouteGuard>
                    } />
                    <Route path="/analytics/advanced" element={
                      <PublicRouteGuard>
                        <QuestionnaireLayout>
                          <AdvancedAnalyticsPage />
                          <FloatingUserPanel />
                        </QuestionnaireLayout>
                      </PublicRouteGuard>
                    } />
                    <Route path="/stories" element={
                      <PublicRouteGuard>
                        <QuestionnaireLayout>
                          <StoriesPage />
                          <FloatingUserPanel />
                        </QuestionnaireLayout>
                      </PublicRouteGuard>
                    } />
                    <Route path="/voices" element={
                      <PublicRouteGuard>
                        <QuestionnaireLayout>
                          <VoicesPage />
                          <FloatingUserPanel />
                        </QuestionnaireLayout>
                      </PublicRouteGuard>
                    } />
                    <Route path="/results" element={
                      <PublicRouteGuard>
                        <QuestionnaireLayout>
                          <ResultsPage />
                          <FloatingUserPanel />
                        </QuestionnaireLayout>
                      </PublicRouteGuard>
                    } />

                    {/* 测试路由 */}
                    <Route path="/test/state" element={<PublicRouteGuard><StateTest /></PublicRouteGuard>} />

                    {/* 管理系统认证路由 */}
                    <Route path="/login" element={<Navigate to="/admin/login" replace />} />
                    <Route path="/reviewer/login" element={<Navigate to="/admin/login" replace />} />
                    <Route path="/admin/login" element={<PublicRouteGuard><NewAdminLoginPage /></PublicRouteGuard>} />
                    <Route path="/admin/login-old" element={<PublicRouteGuard><AdminLoginPage /></PublicRouteGuard>} />

                    {/* 问卷路由 - 公开访问，使用问卷布局 */}
                    <Route path="/questionnaire" element={
                      <PublicRouteGuard>
                        <QuestionnaireLayout>
                          <QuestionnairePage />
                          <FloatingUserPanel />
                        </QuestionnaireLayout>
                      </PublicRouteGuard>
                    } />

                    {/* 审核员路由 - 需要reviewer权限 */}
                    <Route path="/reviewer" element={<NewReviewerRouteGuard><ReviewerDashboard /></NewReviewerRouteGuard>} />
                    <Route path="/reviewer/dashboard" element={<NewReviewerRouteGuard><ReviewerDashboard /></NewReviewerRouteGuard>} />
                    <Route path="/reviewer/questionnaires" element={<NewReviewerRouteGuard><QuestionnaireReviewPage /></NewReviewerRouteGuard>} />
                    <Route path="/reviewer/stories" element={<NewReviewerRouteGuard><StoryReviewPage /></NewReviewerRouteGuard>} />
                    <Route path="/reviewer/voices" element={<NewReviewerRouteGuard><VoiceReviewPage /></NewReviewerRouteGuard>} />
                    <Route path="/reviewer/history" element={<NewReviewerRouteGuard><ReviewHistoryPage /></NewReviewerRouteGuard>} />
                    <Route path="/reviewer/quick-review/:contentType" element={<NewReviewerRouteGuard><QuickReviewPage /></NewReviewerRouteGuard>} />

                    {/* 管理员路由 - 需要admin权限 */}
                    <Route path="/admin" element={<NewAdminRouteGuard><DashboardPage /></NewAdminRouteGuard>} />
                    <Route path="/admin/content" element={<NewAdminRouteGuard><ContentManagementPage /></NewAdminRouteGuard>} />
                    <Route path="/admin/users" element={<NewAdminRouteGuard><UserManagementPage /></NewAdminRouteGuard>} />
                    <Route path="/admin/system" element={<NewAdminRouteGuard><SystemManagementPage /></NewAdminRouteGuard>} />
                    <Route path="/admin/performance" element={<NewAdminRouteGuard><PerformanceMonitorPage /></NewAdminRouteGuard>} />
                    <Route path="/admin/api-data" element={<NewAdminRouteGuard><ApiDataPage /></NewAdminRouteGuard>} />
                    <Route path="/admin/reviewers" element={<NewAdminRouteGuard><ReviewerManagementPage /></NewAdminRouteGuard>} />
                    <Route path="/admin/logs" element={<NewAdminRouteGuard><SystemLogsPage /></NewAdminRouteGuard>} />
                    <Route path="/admin/data-generator" element={<NewAdminRouteGuard><DataGeneratorPage /></NewAdminRouteGuard>} />

                    {/* AI管理路由 - 需要AI管理权限 */}
                    <Route path="/admin/ai/sources" element={<NewAdminRouteGuard><AISourcesPage /></NewAdminRouteGuard>} />
                    <Route path="/admin/ai/monitor" element={<NewAdminRouteGuard><AIMonitorPage /></NewAdminRouteGuard>} />
                    <Route path="/admin/ai/cost" element={<NewAdminRouteGuard><AICostControlPage /></NewAdminRouteGuard>} />
                    <Route path="/admin/ai/review-assistant" element={<NewAdminRouteGuard><AIReviewAssistantPage /></NewAdminRouteGuard>} />

                    {/* 测试路由 - 开发用 */}
                    <Route path="/test" element={<PublicRouteGuard><TestPage /></PublicRouteGuard>} />

                    {/* AI功能演示 */}
                    <Route path="/demo/ai" element={<AIDemoPage />} />
                    <Route path="/test/permissions" element={<PublicRouteGuard><TestPermissionPage /></PublicRouteGuard>} />
                    <Route path="/debug/auth" element={<PublicRouteGuard><AuthDebugPage /></PublicRouteGuard>} />
                    <Route path="/debug/permissions" element={<PublicRouteGuard><PermissionTestPage /></PublicRouteGuard>} />
                    <Route path="/debug/auth-systems" element={<PublicRouteGuard><AuthSystemTestPage /></PublicRouteGuard>} />
                    <Route path="/debug/simple-admin" element={<NewAdminRouteGuard><SimpleAdminTestPage /></NewAdminRouteGuard>} />
                    <Route path="/admin/login-new" element={<PublicRouteGuard><NewAdminLoginPage /></PublicRouteGuard>} />
                  </Routes>
                </main>
              </Suspense>

              {/* 全局半匿名登录组件 */}
              <GlobalSemiAnonymousLogin />
            </div>
          </Router>
        </ConfigProvider>
      </AccessibilityProvider>
    </ErrorBoundary>
  );
}

export default App;
