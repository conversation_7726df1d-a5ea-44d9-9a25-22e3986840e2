import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Select,
  DatePicker,
  Button,
  Space,
  Typography,
  Spin,
  message,
  Alert
} from 'antd';
import {
  DownloadOutlined,
  ReloadOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  SettingOutlined
} from '@ant-design/icons';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Combo<PERSON>hart } from '../../components/charts';
import { QuestionnaireService } from '../../services/questionnaire';
import type { QuestionnaireResponse } from '../../types/questionnaire-engine';
import styles from './AnalyticsPage.module.css';

const { Title } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

// 新的分析数据接口
interface AnalyticsDataV2 {
  totalResponses: number;
  completionRate: number;
  averageTime: number;
  questionnaireTypes: Array<{ name: string; value: number }>;
  genderDistribution: Array<{ name: string; value: number }>;
  educationDistribution: Array<{ name: string; value: number }>;
  salaryExpectation: Array<{ name: string; value: number }>;
  industryPreference: Array<{ name: string; value: number }>;
  monthlyTrend: {
    months: string[];
    responses: number[];
    completions: number[];
  };
  ageDistribution: Array<{ name: string; value: number }>;
}

export const AnalyticsPageV2: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [analyticsData, setAnalyticsData] = useState<AnalyticsDataV2 | null>(null);
  const [questionnaires, setQuestionnaires] = useState<any[]>([]);
  const [dateRange, setDateRange] = useState<[any, any] | null>(null);
  const [selectedQuestionnaireType, setSelectedQuestionnaireType] = useState<string>('all');

  useEffect(() => {
    fetchData();
  }, [dateRange, selectedQuestionnaireType]);

  const fetchData = async () => {
    setLoading(true);
    try {
      // 获取问卷数据
      const response = await QuestionnaireService.getQuestionnaires({
        page: 1,
        pageSize: 1000,
        status: 'approved' // 只分析已通过的问卷
      });

      if (response.success && response.data) {
        setQuestionnaires(response.data);
        const processedData = processAnalyticsData(response.data);
        setAnalyticsData(processedData);
      }
    } catch (error) {
      console.error('获取分析数据失败:', error);
      message.error('获取分析数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理分析数据
  const processAnalyticsData = (questionnaires: any[]): AnalyticsDataV2 => {
    const processedData: AnalyticsDataV2 = {
      totalResponses: questionnaires.length,
      completionRate: 100, // 已提交的问卷完成率为100%
      averageTime: 0,
      questionnaireTypes: [],
      genderDistribution: [],
      educationDistribution: [],
      salaryExpectation: [],
      industryPreference: [],
      monthlyTrend: {
        months: [],
        responses: [],
        completions: []
      },
      ageDistribution: []
    };

    // 统计问卷类型分布
    const typeCount: Record<string, number> = {};
    const genderCount: Record<string, number> = {};
    const educationCount: Record<string, number> = {};
    const salaryCount: Record<string, number> = {};
    const industryCount: Record<string, number> = {};
    const ageCount: Record<string, number> = {};

    questionnaires.forEach(q => {
      // 问卷类型统计
      const type = q.questionnaireId || 'unknown';
      typeCount[type] = (typeCount[type] || 0) + 1;

      // 从响应数据中提取信息
      if (q.responseData && q.responseData.sectionResponses) {
        const responses = q.responseData.sectionResponses;

        // 性别分布
        const personalSection = responses.find((s: any) => s.sectionId === 'personal');
        if (personalSection) {
          const genderResponse = personalSection.questionResponses.find((r: any) => r.questionId === 'gender');
          if (genderResponse) {
            const gender = genderResponse.value;
            genderCount[gender] = (genderCount[gender] || 0) + 1;
          }
        }

        // 教育背景分布
        const educationSection = responses.find((s: any) => s.sectionId === 'education');
        if (educationSection) {
          const degreeResponse = educationSection.questionResponses.find((r: any) => r.questionId === 'degree');
          if (degreeResponse) {
            const degree = degreeResponse.value;
            educationCount[degree] = (educationCount[degree] || 0) + 1;
          }
        }

        // 薪资期望分布
        const employmentSection = responses.find((s: any) => s.sectionId === 'employment');
        if (employmentSection) {
          const salaryResponse = employmentSection.questionResponses.find((r: any) => r.questionId === 'salaryExpectation');
          if (salaryResponse) {
            const salary = salaryResponse.value;
            salaryCount[salary] = (salaryCount[salary] || 0) + 1;
          }

          // 行业偏好分布
          const industryResponse = employmentSection.questionResponses.find((r: any) => r.questionId === 'preferredIndustry');
          if (industryResponse && Array.isArray(industryResponse.value)) {
            industryResponse.value.forEach((industry: string) => {
              industryCount[industry] = (industryCount[industry] || 0) + 1;
            });
          }
        }
      }
    });

    // 转换为图表数据格式
    processedData.questionnaireTypes = Object.entries(typeCount).map(([key, value]) => ({
      name: getQuestionnaireTypeName(key),
      value
    }));

    processedData.genderDistribution = Object.entries(genderCount).map(([key, value]) => ({
      name: getGenderName(key),
      value
    }));

    processedData.educationDistribution = Object.entries(educationCount).map(([key, value]) => ({
      name: getEducationName(key),
      value
    }));

    processedData.salaryExpectation = Object.entries(salaryCount).map(([key, value]) => ({
      name: getSalaryName(key),
      value
    }));

    processedData.industryPreference = Object.entries(industryCount).map(([key, value]) => ({
      name: getIndustryName(key),
      value
    }));

    // 计算月度趋势（简化版本）
    const monthlyData = calculateMonthlyTrend(questionnaires);
    processedData.monthlyTrend = monthlyData;

    return processedData;
  };

  // 辅助函数：获取问卷类型名称
  const getQuestionnaireTypeName = (type: string): string => {
    const typeMap: Record<string, string> = {
      'employment-survey-v1': '就业调研问卷'
    };
    return typeMap[type] || type;
  };

  // 辅助函数：获取性别名称
  const getGenderName = (gender: string): string => {
    const genderMap: Record<string, string> = {
      'male': '男',
      'female': '女',
      'other': '其他'
    };
    return genderMap[gender] || gender;
  };

  // 辅助函数：获取学历名称
  const getEducationName = (education: string): string => {
    const educationMap: Record<string, string> = {
      'high_school': '高中/中专',
      'associate': '大专',
      'bachelor': '本科',
      'master': '硕士',
      'phd': '博士'
    };
    return educationMap[education] || education;
  };

  // 辅助函数：获取薪资名称
  const getSalaryName = (salary: string): string => {
    const salaryMap: Record<string, string> = {
      'below_3k': '3000元以下',
      '3k_5k': '3000-5000元',
      '5k_8k': '5000-8000元',
      '8k_12k': '8000-12000元',
      '12k_20k': '12000-20000元',
      '20k_30k': '20000-30000元',
      'above_30k': '30000元以上'
    };
    return salaryMap[salary] || salary;
  };

  // 辅助函数：获取行业名称
  const getIndustryName = (industry: string): string => {
    const industryMap: Record<string, string> = {
      'tech': '科技/互联网',
      'finance': '金融/银行/保险',
      'education': '教育/培训',
      'healthcare': '医疗健康',
      'manufacturing': '制造业',
      'government': '政府/公共服务',
      'consulting': '咨询服务',
      'media': '媒体/广告',
      'retail': '零售/电商',
      'real_estate': '房地产',
      'other': '其他'
    };
    return industryMap[industry] || industry;
  };

  // 计算月度趋势
  const calculateMonthlyTrend = (questionnaires: any[]) => {
    const monthlyCount: Record<string, number> = {};
    
    questionnaires.forEach(q => {
      const date = new Date(q.metadata?.submittedAt || q.createdAt);
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      monthlyCount[monthKey] = (monthlyCount[monthKey] || 0) + 1;
    });

    const sortedMonths = Object.keys(monthlyCount).sort();
    const responses = sortedMonths.map(month => monthlyCount[month]);

    return {
      months: sortedMonths,
      responses,
      completions: responses // 已提交的问卷完成率为100%
    };
  };

  const handleRefresh = () => {
    fetchData();
  };

  const handleExport = () => {
    // 导出功能实现
    message.info('导出功能开发中...');
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>加载分析数据中...</div>
      </div>
    );
  }

  if (!analyticsData) {
    return (
      <Alert
        message="暂无数据"
        description="暂时没有可分析的问卷数据"
        type="info"
        showIcon
      />
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <Title level={2}>
          <BarChartOutlined /> 数据分析
        </Title>
        <Space>
          <RangePicker onChange={setDateRange} />
          <Select
            value={selectedQuestionnaireType}
            onChange={setSelectedQuestionnaireType}
            style={{ width: 200 }}
          >
            <Option value="all">所有问卷类型</Option>
            <Option value="employment-survey-v1">就业调研问卷</Option>
          </Select>
          <Button icon={<ReloadOutlined />} onClick={handleRefresh}>
            刷新
          </Button>
          <Button type="primary" icon={<DownloadOutlined />} onClick={handleExport}>
            导出报告
          </Button>
        </Space>
      </div>

      {/* 统计概览 */}
      <Row gutter={16} className={styles.statsRow}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总响应数"
              value={analyticsData.totalResponses}
              prefix={<BarChartOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="完成率"
              value={analyticsData.completionRate}
              suffix="%"
              prefix={<PieChartOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="平均用时"
              value={analyticsData.averageTime}
              suffix="分钟"
              prefix={<LineChartOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="问卷类型"
              value={analyticsData.questionnaireTypes.length}
              prefix={<SettingOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* 图表展示 */}
      <Row gutter={16} className={styles.chartsRow}>
        <Col span={12}>
          <Card title="性别分布" className={styles.chartCard}>
            <PieChart data={analyticsData.genderDistribution} />
          </Card>
        </Col>
        <Col span={12}>
          <Card title="学历分布" className={styles.chartCard}>
            <BarChart data={analyticsData.educationDistribution} />
          </Card>
        </Col>
      </Row>

      <Row gutter={16} className={styles.chartsRow}>
        <Col span={12}>
          <Card title="薪资期望分布" className={styles.chartCard}>
            <BarChart data={analyticsData.salaryExpectation} />
          </Card>
        </Col>
        <Col span={12}>
          <Card title="行业偏好分布" className={styles.chartCard}>
            <PieChart data={analyticsData.industryPreference} />
          </Card>
        </Col>
      </Row>

      <Row gutter={16} className={styles.chartsRow}>
        <Col span={24}>
          <Card title="月度趋势" className={styles.chartCard}>
            <LineChart 
              data={analyticsData.monthlyTrend.months.map((month, index) => ({
                name: month,
                value: analyticsData.monthlyTrend.responses[index]
              }))} 
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};
