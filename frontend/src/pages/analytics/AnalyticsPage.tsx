import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Select,
  DatePicker,
  Button,
  Space,
  Typography,
  Spin,
  message
} from 'antd';
import {
  DownloadOutlined,
  ReloadOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  SettingOutlined
} from '@ant-design/icons';
import { Link } from 'react-router-dom';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>mbo<PERSON>hart, HeatmapChart } from '../../components/charts';
import { ExportModal } from '../../components/analytics/ExportModal';
import { useAuthStore } from '../../stores/authStore';
import styles from './AnalyticsPage.module.css';

const { Title } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

// 模拟数据接口
interface AnalyticsData {
  totalResponses: number;
  completionRate: number;
  averageTime: number;
  educationDistribution: Array<{ name: string; value: number }>;
  salaryExpectation: Array<{ name: string; value: number }>;
  employmentStatus: Array<{ name: string; value: number }>;
  monthlyTrend: {
    months: string[];
    responses: number[];
    completions: number[];
  };
  regionDistribution: Array<{ name: string; value: number }>;
  ageDistribution: Array<{ name: string; value: number }>;
  skillsHeatmap: {
    skills: string[];
    levels: string[];
    data: Array<{ x: number; y: number; value: number }>;
  };
}

export const AnalyticsPage: React.FC = () => {
  const { user } = useAuthStore();
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState<AnalyticsData | null>(null);
  const [timeRange, setTimeRange] = useState<string>('last30days');
  const [chartType, setChartType] = useState<string>('bar');
  const [exportModalVisible, setExportModalVisible] = useState(false);

  // 模拟数据
  const mockData: AnalyticsData = {
    totalResponses: 1248,
    completionRate: 87.5,
    averageTime: 12.3,
    educationDistribution: [
      { name: '本科', value: 520 },
      { name: '硕士', value: 380 },
      { name: '博士', value: 120 },
      { name: '专科', value: 180 },
      { name: '高中及以下', value: 48 }
    ],
    salaryExpectation: [
      { name: '3-5K', value: 156 },
      { name: '5-8K', value: 324 },
      { name: '8-12K', value: 412 },
      { name: '12-20K', value: 268 },
      { name: '20K+', value: 88 }
    ],
    employmentStatus: [
      { name: '已就业', value: 687 },
      { name: '求职中', value: 324 },
      { name: '继续深造', value: 156 },
      { name: '暂不就业', value: 81 }
    ],
    monthlyTrend: {
      months: ['1月', '2月', '3月', '4月', '5月', '6月'],
      responses: [120, 156, 189, 234, 298, 351],
      completions: [105, 142, 167, 201, 267, 312]
    },
    regionDistribution: [
      { name: '北京', value: 245 },
      { name: '上海', value: 198 },
      { name: '广州', value: 156 },
      { name: '深圳', value: 134 },
      { name: '杭州', value: 112 },
      { name: '成都', value: 98 },
      { name: '其他', value: 305 }
    ],
    ageDistribution: [
      { name: '18-22岁', value: 312 },
      { name: '23-25岁', value: 456 },
      { name: '26-30岁', value: 298 },
      { name: '31-35岁', value: 134 },
      { name: '35岁以上', value: 48 }
    ],
    skillsHeatmap: {
      skills: ['Java', 'Python', 'JavaScript', 'React', 'Vue', 'Node.js'],
      levels: ['初级', '中级', '高级', '专家'],
      data: [
        { x: 0, y: 0, value: 45 }, { x: 0, y: 1, value: 78 }, { x: 0, y: 2, value: 123 }, { x: 0, y: 3, value: 89 },
        { x: 1, y: 0, value: 67 }, { x: 1, y: 1, value: 134 }, { x: 1, y: 2, value: 156 }, { x: 1, y: 3, value: 98 },
        { x: 2, y: 0, value: 89 }, { x: 2, y: 1, value: 167 }, { x: 2, y: 2, value: 189 }, { x: 2, y: 3, value: 134 },
        { x: 3, y: 0, value: 34 }, { x: 3, y: 1, value: 78 }, { x: 3, y: 2, value: 123 }, { x: 3, y: 3, value: 67 },
        { x: 4, y: 0, value: 23 }, { x: 4, y: 1, value: 56 }, { x: 4, y: 2, value: 89 }, { x: 4, y: 3, value: 45 },
        { x: 5, y: 0, value: 12 }, { x: 5, y: 1, value: 34 }, { x: 5, y: 2, value: 67 }, { x: 5, y: 3, value: 23 }
      ]
    }
  };

  useEffect(() => {
    loadData();
  }, [timeRange]);

  const loadData = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      setData(mockData);
    } catch (error) {
      message.error('数据加载失败');
    } finally {
      setLoading(false);
    }
  };

  const handleExport = () => {
    setExportModalVisible(true);
  };

  const handleRefresh = () => {
    loadData();
  };

  if (loading) {
    return (
      <div className={styles.content}>
        <div className={styles.loadingContainer}>
          <Spin size="large" />
        </div>
      </div>
    );
  }

  return (
    <div className={styles.content}>
      <div className={styles.header}>
        <Title level={2}>数据分析</Title>
        <Space>
          <Select
            value={timeRange}
            onChange={setTimeRange}
            style={{ width: 120 }}
          >
            <Option value="last7days">最近7天</Option>
            <Option value="last30days">最近30天</Option>
            <Option value="last90days">最近90天</Option>
            <Option value="custom">自定义</Option>
          </Select>
          <Button icon={<ReloadOutlined />} onClick={handleRefresh}>
            刷新
          </Button>
          <Link to="/analytics/advanced">
            <Button icon={<SettingOutlined />}>
              高级分析
            </Button>
          </Link>
          <Button type="primary" icon={<DownloadOutlined />} onClick={handleExport}>
            导出数据
          </Button>
        </Space>
      </div>

        {/* 统计卡片 */}
        <Row gutter={[16, 16]} className={styles.statsRow}>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="总回答数"
                value={data?.totalResponses}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="完成率"
                value={data?.completionRate}
                precision={1}
                suffix="%"
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="平均用时"
                value={data?.averageTime}
                precision={1}
                suffix="分钟"
                valueStyle={{ color: '#fa8c16' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="活跃用户"
                value={Math.floor((data?.totalResponses || 0) * 0.7)}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
        </Row>

        {/* 图表区域 */}
        <Row gutter={[16, 16]} className={styles.chartsRow}>
          <Col xs={24} lg={12}>
            <Card 
              title="学历分布" 
              extra={
                <Select value={chartType} onChange={setChartType} size="small">
                  <Option value="bar"><BarChartOutlined /> 柱状图</Option>
                  <Option value="pie"><PieChartOutlined /> 饼图</Option>
                </Select>
              }
            >
              <div data-chart="education-chart">
                {chartType === 'bar' ? (
                  <BarChart
                    data={data?.educationDistribution || []}
                    height={300}
                  />
                ) : (
                  <PieChart
                    data={data?.educationDistribution || []}
                    height={300}
                  />
                )}
              </div>
            </Card>
          </Col>
          <Col xs={24} lg={12}>
            <Card title="薪资期望分布">
              <div data-chart="salary-chart">
                <BarChart
                  data={data?.salaryExpectation || []}
                  height={300}
                  color={['#52c41a', '#1890ff', '#fa8c16', '#f5222d', '#722ed1']}
                />
              </div>
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 16]} className={styles.chartsRow}>
          <Col xs={24} lg={12}>
            <Card title="就业状态分布">
              <PieChart
                data={data?.employmentStatus || []}
                height={300}
                radius={['40%', '70%']}
              />
            </Card>
          </Col>
          <Col xs={24} lg={12}>
            <Card title="月度趋势" extra={<LineChartOutlined />}>
              <ComboChart
                data={[
                  { name: '问卷回答', type: 'bar', data: data?.monthlyTrend.responses || [], color: '#1890ff' },
                  { name: '完成数量', type: 'line', data: data?.monthlyTrend.completions || [], color: '#52c41a', yAxisIndex: 1 }
                ]}
                xAxisData={data?.monthlyTrend.months || []}
                height={300}
                yAxisConfig={{
                  left: { name: '回答数', unit: '份' },
                  right: { name: '完成数', unit: '份' }
                }}
              />
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 16]} className={styles.chartsRow}>
          <Col xs={24} lg={12}>
            <Card title="地区分布">
              <BarChart
                data={data?.regionDistribution || []}
                height={300}
                horizontal={true}
                color={['#722ed1', '#13c2c2', '#fa541c', '#eb2f96', '#52c41a', '#1890ff', '#faad14']}
              />
            </Card>
          </Col>
          <Col xs={24} lg={12}>
            <Card title="年龄分布">
              <PieChart
                data={data?.ageDistribution || []}
                height={300}
                showLegend={false}
                radius={['30%', '60%']}
              />
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 16]} className={styles.chartsRow}>
          <Col xs={24}>
            <Card title="技能水平热力图">
              <HeatmapChart
                data={data?.skillsHeatmap.data || []}
                xAxisData={data?.skillsHeatmap.skills || []}
                yAxisData={data?.skillsHeatmap.levels || []}
                height={400}
                colorRange={['#f7fbff', '#08519c']}
              />
            </Card>
          </Col>
        </Row>

      <ExportModal
        visible={exportModalVisible}
        onCancel={() => setExportModalVisible(false)}
        data={data}
      />
    </div>
  );
};
