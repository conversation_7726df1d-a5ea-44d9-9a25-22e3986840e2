import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Tabs,
  Button,
  Space,
  Typography,
  Spin,
  message,
  Drawer
} from 'antd';
import { 
  DownloadOutlined, 
  ReloadOutlined,
  FilterOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  HeatmapOutlined
} from '@ant-design/icons';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, ComboChart, HeatmapChart } from '../../components/charts';
import { AdvancedFilter, FilterValues } from '../../components/analytics/AdvancedFilter';
import { ComparisonAnalysis } from '../../components/analytics/ComparisonAnalysis';
import { useAuthStore } from '../../stores/authStore';
import styles from './AnalyticsPage.module.css';

const { Title } = Typography;
const { TabPane } = Tabs;

interface AnalyticsData {
  totalResponses: number;
  completionRate: number;
  averageTime: number;
  educationDistribution: Array<{ name: string; value: number }>;
  salaryExpectation: Array<{ name: string; value: number }>;
  employmentStatus: Array<{ name: string; value: number }>;
  monthlyTrend: {
    months: string[];
    responses: number[];
    completions: number[];
  };
  regionDistribution: Array<{ name: string; value: number }>;
  ageDistribution: Array<{ name: string; value: number }>;
  skillsHeatmap: {
    skills: string[];
    levels: string[];
    data: Array<{ x: number; y: number; value: number }>;
  };
}

export const AdvancedAnalyticsPage: React.FC = () => {
  const { user } = useAuthStore();
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState<AnalyticsData | null>(null);
  const [filters, setFilters] = useState<FilterValues>({});
  const [filterDrawerVisible, setFilterDrawerVisible] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  // 模拟数据
  const mockData: AnalyticsData = {
    totalResponses: 1248,
    completionRate: 87.5,
    averageTime: 12.3,
    educationDistribution: [
      { name: '本科', value: 520 },
      { name: '硕士', value: 380 },
      { name: '博士', value: 120 },
      { name: '专科', value: 180 },
      { name: '高中及以下', value: 48 }
    ],
    salaryExpectation: [
      { name: '3-5K', value: 156 },
      { name: '5-8K', value: 324 },
      { name: '8-12K', value: 412 },
      { name: '12-20K', value: 268 },
      { name: '20K+', value: 88 }
    ],
    employmentStatus: [
      { name: '已就业', value: 687 },
      { name: '求职中', value: 324 },
      { name: '继续深造', value: 156 },
      { name: '暂不就业', value: 81 }
    ],
    monthlyTrend: {
      months: ['1月', '2月', '3月', '4月', '5月', '6月'],
      responses: [120, 156, 189, 234, 298, 351],
      completions: [105, 142, 167, 201, 267, 312]
    },
    regionDistribution: [
      { name: '北京', value: 245 },
      { name: '上海', value: 198 },
      { name: '广州', value: 156 },
      { name: '深圳', value: 134 },
      { name: '杭州', value: 112 },
      { name: '成都', value: 98 },
      { name: '其他', value: 305 }
    ],
    ageDistribution: [
      { name: '18-22岁', value: 312 },
      { name: '23-25岁', value: 456 },
      { name: '26-30岁', value: 298 },
      { name: '31-35岁', value: 134 },
      { name: '35岁以上', value: 48 }
    ],
    skillsHeatmap: {
      skills: ['Java', 'Python', 'JavaScript', 'React', 'Vue', 'Node.js'],
      levels: ['初级', '中级', '高级', '专家'],
      data: [
        { x: 0, y: 0, value: 45 }, { x: 0, y: 1, value: 78 }, { x: 0, y: 2, value: 123 }, { x: 0, y: 3, value: 89 },
        { x: 1, y: 0, value: 67 }, { x: 1, y: 1, value: 134 }, { x: 1, y: 2, value: 156 }, { x: 1, y: 3, value: 98 },
        { x: 2, y: 0, value: 89 }, { x: 2, y: 1, value: 167 }, { x: 2, y: 2, value: 189 }, { x: 2, y: 3, value: 134 },
        { x: 3, y: 0, value: 34 }, { x: 3, y: 1, value: 78 }, { x: 3, y: 2, value: 123 }, { x: 3, y: 3, value: 67 },
        { x: 4, y: 0, value: 23 }, { x: 4, y: 1, value: 56 }, { x: 4, y: 2, value: 89 }, { x: 4, y: 3, value: 45 },
        { x: 5, y: 0, value: 12 }, { x: 5, y: 1, value: 34 }, { x: 5, y: 2, value: 67 }, { x: 5, y: 3, value: 23 }
      ]
    }
  };

  useEffect(() => {
    loadData();
  }, [filters]);

  const loadData = async () => {
    setLoading(true);
    try {
      // 模拟API调用，应用筛选条件
      await new Promise(resolve => setTimeout(resolve, 1000));
      setData(mockData);
    } catch (error) {
      message.error('数据加载失败');
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (newFilters: FilterValues) => {
    setFilters(newFilters);
  };

  const handleFilterReset = () => {
    setFilters({});
  };

  const handleExport = () => {
    message.success('数据导出功能开发中...');
  };

  const handleRefresh = () => {
    loadData();
  };

  if (loading) {
    return (
      <div className={styles.content}>
        <div className={styles.loadingContainer}>
          <Spin size="large" />
        </div>
      </div>
    );
  }

  return (
    <div className={styles.content}>
      <div className={styles.header}>
        <Title level={2}>高级数据分析</Title>
        <Space>
          <Button
            icon={<FilterOutlined />}
            onClick={() => setFilterDrawerVisible(true)}
          >
            高级筛选
          </Button>
          <Button icon={<ReloadOutlined />} onClick={handleRefresh}>
            刷新
          </Button>
          <Button type="primary" icon={<DownloadOutlined />} onClick={handleExport}>
            导出数据
          </Button>
        </Space>
      </div>

        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane 
            tab={
              <span>
                <BarChartOutlined />
                概览分析
              </span>
            } 
            key="overview"
          >
            <Row gutter={[16, 16]}>
              <Col xs={24} lg={12}>
                <Card title="学历分布">
                  <PieChart 
                    data={data?.educationDistribution || []}
                    height={300}
                  />
                </Card>
              </Col>
              <Col xs={24} lg={12}>
                <Card title="薪资期望分布">
                  <BarChart 
                    data={data?.salaryExpectation || []}
                    height={300}
                    color={['#52c41a', '#1890ff', '#fa8c16', '#f5222d', '#722ed1']}
                  />
                </Card>
              </Col>
            </Row>

            <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
              <Col xs={24} lg={12}>
                <Card title="地区分布">
                  <BarChart 
                    data={data?.regionDistribution || []}
                    height={300}
                    horizontal={true}
                  />
                </Card>
              </Col>
              <Col xs={24} lg={12}>
                <Card title="就业状态分布">
                  <PieChart 
                    data={data?.employmentStatus || []}
                    height={300}
                    radius={['40%', '70%']}
                  />
                </Card>
              </Col>
            </Row>
          </TabPane>

          <TabPane 
            tab={
              <span>
                <LineChartOutlined />
                趋势分析
              </span>
            } 
            key="trend"
          >
            <Row gutter={[16, 16]}>
              <Col xs={24}>
                <Card title="月度趋势">
                  <ComboChart
                    data={[
                      { name: '问卷回答', type: 'bar', data: data?.monthlyTrend.responses || [], color: '#1890ff' },
                      { name: '完成数量', type: 'line', data: data?.monthlyTrend.completions || [], color: '#52c41a', yAxisIndex: 1 }
                    ]}
                    xAxisData={data?.monthlyTrend.months || []}
                    height={400}
                    yAxisConfig={{
                      left: { name: '回答数', unit: '份' },
                      right: { name: '完成数', unit: '份' }
                    }}
                  />
                </Card>
              </Col>
            </Row>

            <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
              <Col xs={24} lg={12}>
                <Card title="年龄分布趋势">
                  <LineChart
                    data={[
                      { name: '18-22岁', data: [45, 52, 61, 68, 72, 78] },
                      { name: '23-25岁', data: [78, 85, 92, 98, 105, 112] },
                      { name: '26-30岁', data: [56, 61, 67, 72, 78, 85] }
                    ]}
                    xAxisData={data?.monthlyTrend.months || []}
                    height={300}
                    area={true}
                  />
                </Card>
              </Col>
              <Col xs={24} lg={12}>
                <Card title="学历分布趋势">
                  <LineChart
                    data={[
                      { name: '本科', data: [120, 135, 148, 162, 175, 189] },
                      { name: '硕士', data: [89, 95, 102, 108, 115, 123] },
                      { name: '博士', data: [23, 26, 29, 32, 35, 38] }
                    ]}
                    xAxisData={data?.monthlyTrend.months || []}
                    height={300}
                  />
                </Card>
              </Col>
            </Row>
          </TabPane>

          <TabPane 
            tab={
              <span>
                <HeatmapOutlined />
                技能分析
              </span>
            } 
            key="skills"
          >
            <Row gutter={[16, 16]}>
              <Col xs={24}>
                <Card title="技能水平热力图">
                  <HeatmapChart
                    data={data?.skillsHeatmap.data || []}
                    xAxisData={data?.skillsHeatmap.skills || []}
                    yAxisData={data?.skillsHeatmap.levels || []}
                    height={400}
                    colorRange={['#f7fbff', '#08519c']}
                  />
                </Card>
              </Col>
            </Row>
          </TabPane>

          <TabPane 
            tab={
              <span>
                <PieChartOutlined />
                对比分析
              </span>
            } 
            key="comparison"
          >
            <ComparisonAnalysis loading={loading} />
          </TabPane>
        </Tabs>

        <Drawer
          title="高级筛选"
          placement="right"
          width={400}
          onClose={() => setFilterDrawerVisible(false)}
          open={filterDrawerVisible}
        >
          <AdvancedFilter
            onFilterChange={handleFilterChange}
            onReset={handleFilterReset}
            loading={loading}
          />
        </Drawer>
    </div>
  );
};
