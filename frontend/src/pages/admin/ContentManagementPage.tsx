import React, { useState, useEffect } from 'react';
import {
  Typo<PERSON>,
  Space,
  Button,
  Tabs,
  message,
  Spin,
  Layout
} from 'antd';
import {
  ReloadOutlined,
  PlusOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { BatchOperations } from '../../components/admin/BatchOperations';
import { AdvancedSearch } from '../../components/admin/AdvancedSearch';
import { UserManagement } from '../../components/admin/UserManagement';
import { AdminLayout } from '../../components/layout/RoleBasedLayout';
import { useAuthStore } from '../../stores/authStore';
import styles from './ContentManagementPage.module.css';

const { Title } = Typography;
const { Content } = Layout;

interface ContentItem {
  id: string;
  title: string;
  status: 'pending' | 'approved' | 'rejected' | 'draft';
  submittedAt: string;
  submittedBy: string;
  type: 'questionnaire' | 'story' | 'comment';
}

export const ContentManagementPage: React.FC = () => {
  const { user } = useAuthStore();
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState<ContentItem[]>([]);
  const [filteredData, setFilteredData] = useState<ContentItem[]>([]);
  const [activeTab, setActiveTab] = useState('all');

  // 模拟数据
  const mockData: ContentItem[] = [
    {
      id: '1',
      title: '大学生就业现状调研问卷',
      status: 'pending',
      submittedAt: '2025-01-15 10:30:00',
      submittedBy: '<EMAIL>',
      type: 'questionnaire'
    },
    {
      id: '2',
      title: '我的求职经历分享',
      status: 'approved',
      submittedAt: '2025-01-14 15:20:00',
      submittedBy: '<EMAIL>',
      type: 'story'
    },
    {
      id: '3',
      title: '关于薪资期望的思考',
      status: 'pending',
      submittedAt: '2025-01-14 09:15:00',
      submittedBy: '<EMAIL>',
      type: 'story'
    },
    {
      id: '4',
      title: '技术岗位面试经验',
      status: 'rejected',
      submittedAt: '2025-01-13 16:45:00',
      submittedBy: '<EMAIL>',
      type: 'story'
    },
    {
      id: '5',
      title: '就业指导建议',
      status: 'draft',
      submittedAt: '2025-01-13 11:30:00',
      submittedBy: '<EMAIL>',
      type: 'comment'
    },
    {
      id: '6',
      title: '毕业生就业调查',
      status: 'approved',
      submittedAt: '2025-01-12 14:20:00',
      submittedBy: '<EMAIL>',
      type: 'questionnaire'
    },
    {
      id: '7',
      title: '职场新人的困惑',
      status: 'pending',
      submittedAt: '2025-01-12 08:45:00',
      submittedBy: '<EMAIL>',
      type: 'story'
    },
    {
      id: '8',
      title: '实习经历总结',
      status: 'approved',
      submittedAt: '2025-01-11 17:30:00',
      submittedBy: '<EMAIL>',
      type: 'story'
    }
  ];

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    filterDataByTab();
  }, [data, activeTab]);

  const loadData = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      setData(mockData);
    } catch (error) {
      message.error('数据加载失败');
    } finally {
      setLoading(false);
    }
  };

  const filterDataByTab = () => {
    let filtered = data;
    
    switch (activeTab) {
      case 'pending':
        filtered = data.filter(item => item.status === 'pending');
        break;
      case 'approved':
        filtered = data.filter(item => item.status === 'approved');
        break;
      case 'rejected':
        filtered = data.filter(item => item.status === 'rejected');
        break;
      case 'questionnaire':
        filtered = data.filter(item => item.type === 'questionnaire');
        break;
      case 'story':
        filtered = data.filter(item => item.type === 'story');
        break;
      case 'comment':
        filtered = data.filter(item => item.type === 'comment');
        break;
      default:
        filtered = data;
    }
    
    setFilteredData(filtered);
  };

  const handleSearch = (filters: any) => {
    let filtered = data;
    
    // 应用搜索筛选
    if (filters.keyword) {
      filtered = filtered.filter(item =>
        item.title.toLowerCase().includes(filters.keyword.toLowerCase()) ||
        item.submittedBy.toLowerCase().includes(filters.keyword.toLowerCase())
      );
    }
    
    if (filters.status?.length) {
      filtered = filtered.filter(item => filters.status.includes(item.status));
    }
    
    if (filters.type?.length) {
      filtered = filtered.filter(item => filters.type.includes(item.type));
    }
    
    if (filters.submittedBy) {
      filtered = filtered.filter(item =>
        item.submittedBy.toLowerCase().includes(filters.submittedBy.toLowerCase())
      );
    }
    
    setFilteredData(filtered);
    message.success(`找到 ${filtered.length} 条匹配记录`);
  };

  const handleSearchReset = () => {
    filterDataByTab();
    message.info('筛选条件已重置');
  };

  const handleRefresh = () => {
    loadData();
  };

  const getTabCount = (status?: string, type?: string) => {
    if (status) {
      return data.filter(item => item.status === status).length;
    }
    if (type) {
      return data.filter(item => item.type === type).length;
    }
    return data.length;
  };

  if (loading) {
    return (
      <Layout className="min-h-screen">
        <Content className={styles.content}>
          <div className={styles.loadingContainer}>
            <Spin size="large" />
          </div>
        </Content>
      </Layout>
    );
  }

  return (
    <AdminLayout>
      <div className={styles.content}>
        <div className={styles.header}>
          <Title level={2}>内容管理</Title>
          <Space>
            <Button icon={<PlusOutlined />} type="primary">
              新建内容
            </Button>
            <Button icon={<SettingOutlined />}>
              管理设置
            </Button>
            <Button icon={<ReloadOutlined />} onClick={handleRefresh}>
              刷新
            </Button>
          </Space>
        </div>

        <div className={styles.searchSection}>
          <AdvancedSearch
            onSearch={handleSearch}
            onReset={handleSearchReset}
            loading={loading}
          />
        </div>

        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={[
            { key: 'all', label: `全部 (${getTabCount()})` },
            { key: 'pending', label: `待审核 (${getTabCount('pending')})` },
            { key: 'approved', label: `已通过 (${getTabCount('approved')})` },
            { key: 'rejected', label: `已拒绝 (${getTabCount('rejected')})` },
            { key: 'questionnaire', label: `问卷 (${getTabCount(undefined, 'questionnaire')})` },
            { key: 'story', label: `故事 (${getTabCount(undefined, 'story')})` },
            { key: 'comment', label: `评论 (${getTabCount(undefined, 'comment')})` }
          ]}
        />

        <div className={styles.tableSection}>
          <BatchOperations
            data={filteredData}
            loading={loading}
            onRefresh={handleRefresh}
          />
        </div>
      </div>
    </AdminLayout>
  );
};
