import React, { useState, useEffect } from 'react';
import {
  Typo<PERSON>,
  Space,
  Button,
  Card,
  Row,
  Col,
  Statistic,
  message,
  Spin
} from 'antd';
import {
  ReloadOutlined,
  UserOutlined,
  TeamOutlined,
  CrownOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { AdminLayout } from '../../components/layout/RoleBasedLayout';
import { UserManagement } from '../../components/admin/UserManagement';
import { useAuthStore } from '../../stores/authStore';
import styles from './UserManagementPage.module.css';

const { Title } = Typography;

interface User {
  id: string;
  username: string;
  email: string;
  role: 'user' | 'admin' | 'super_admin';
  status: 'active' | 'inactive' | 'banned';
  lastLogin: string;
  createdAt: string;
  avatar?: string;
  questionnairesCount: number;
  storiesCount: number;
}

export const UserManagementPage: React.FC = () => {
  const { user } = useAuthStore();
  const [loading, setLoading] = useState(true);
  const [users, setUsers] = useState<User[]>([]);

  // 模拟用户数据
  const mockUsers: User[] = [
    {
      id: '1',
      username: 'admin',
      email: '<EMAIL>',
      role: 'super_admin',
      status: 'active',
      lastLogin: '2025-01-15 10:30:00',
      createdAt: '2024-01-01 00:00:00',
      questionnairesCount: 0,
      storiesCount: 0
    },
    {
      id: '2',
      username: 'manager1',
      email: '<EMAIL>',
      role: 'admin',
      status: 'active',
      lastLogin: '2025-01-15 09:15:00',
      createdAt: '2024-02-15 10:30:00',
      questionnairesCount: 3,
      storiesCount: 1
    },
    {
      id: '3',
      username: 'user001',
      email: '<EMAIL>',
      role: 'user',
      status: 'active',
      lastLogin: '2025-01-14 16:45:00',
      createdAt: '2024-03-10 14:20:00',
      questionnairesCount: 5,
      storiesCount: 8
    },
    {
      id: '4',
      username: 'user002',
      email: '<EMAIL>',
      role: 'user',
      status: 'active',
      lastLogin: '2025-01-14 11:20:00',
      createdAt: '2024-03-12 09:15:00',
      questionnairesCount: 2,
      storiesCount: 3
    },
    {
      id: '5',
      username: 'user003',
      email: '<EMAIL>',
      role: 'user',
      status: 'inactive',
      lastLogin: '2024-12-20 15:30:00',
      createdAt: '2024-04-05 11:45:00',
      questionnairesCount: 1,
      storiesCount: 0
    },
    {
      id: '6',
      username: 'spammer',
      email: '<EMAIL>',
      role: 'user',
      status: 'banned',
      lastLogin: '2024-11-15 08:20:00',
      createdAt: '2024-05-20 16:30:00',
      questionnairesCount: 0,
      storiesCount: 12
    },
    {
      id: '7',
      username: 'user004',
      email: '<EMAIL>',
      role: 'user',
      status: 'active',
      lastLogin: '2025-01-13 14:10:00',
      createdAt: '2024-06-08 13:25:00',
      questionnairesCount: 4,
      storiesCount: 6
    },
    {
      id: '8',
      username: 'user005',
      email: '<EMAIL>',
      role: 'user',
      status: 'active',
      lastLogin: '2025-01-12 17:55:00',
      createdAt: '2024-07-15 10:40:00',
      questionnairesCount: 3,
      storiesCount: 2
    }
  ];

  useEffect(() => {
    loadUsers();
  }, []);

  const loadUsers = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      setUsers(mockUsers);
    } catch (error) {
      message.error('用户数据加载失败');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = () => {
    loadUsers();
  };

  const getStatistics = () => {
    const totalUsers = users.length;
    const activeUsers = users.filter(u => u.status === 'active').length;
    const adminUsers = users.filter(u => u.role === 'admin' || u.role === 'super_admin').length;
    const bannedUsers = users.filter(u => u.status === 'banned').length;

    return {
      totalUsers,
      activeUsers,
      adminUsers,
      bannedUsers
    };
  };

  const stats = getStatistics();

  if (loading) {
    return (
      <AdminLayout>
        <div className={styles.content}>
          <div className={styles.loadingContainer}>
            <Spin size="large" />
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className={styles.content}>
        <div className={styles.header}>
          <Title level={2}>用户管理</Title>
          <Space>
            <Button icon={<ReloadOutlined />} onClick={handleRefresh}>
              刷新
            </Button>
          </Space>
        </div>

        {/* 统计卡片 */}
        <Row gutter={[16, 16]} className={styles.statsRow}>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="总用户数"
                value={stats.totalUsers}
                prefix={<UserOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="活跃用户"
                value={stats.activeUsers}
                prefix={<TeamOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="管理员"
                value={stats.adminUsers}
                prefix={<CrownOutlined />}
                valueStyle={{ color: '#fa8c16' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="封禁用户"
                value={stats.bannedUsers}
                prefix={<ExclamationCircleOutlined />}
                valueStyle={{ color: '#f5222d' }}
              />
            </Card>
          </Col>
        </Row>

        {/* 用户管理表格 */}
        <div className={styles.tableSection}>
          <UserManagement
            data={users}
            loading={loading}
            onRefresh={handleRefresh}
          />
        </div>
      </div>
    </AdminLayout>
  );
};
