import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Space,
  Button,
  Card,
  Row,
  Col,
  <PERSON>ati<PERSON>,
  Tabs,
  Alert,
  message,
  Spin
} from 'antd';
import {
  ReloadOutlined,
  SettingOutlined,
  DatabaseOutlined,
  MonitorOutlined,
  SecurityScanOutlined,
  BugOutlined
} from '@ant-design/icons';
import { DataManagement } from '../../components/admin/DataManagement';
import { AdminLayout } from '../../components/layout/RoleBasedLayout';
import { useAuthStore } from '../../stores/authStore';
import styles from './SystemManagementPage.module.css';

const { Title } = Typography;
const { TabPane } = Tabs;

interface SystemStatus {
  cpuUsage: number;
  memoryUsage: number;
  diskUsage: number;
  activeUsers: number;
  totalRequests: number;
  errorRate: number;
}

export const SystemManagementPage: React.FC = () => {
  const { user } = useAuthStore();
  const [loading, setLoading] = useState(true);
  const [systemStatus, setSystemStatus] = useState<SystemStatus | null>(null);

  // 模拟系统状态数据
  const mockSystemStatus: SystemStatus = {
    cpuUsage: 45.2,
    memoryUsage: 67.8,
    diskUsage: 34.5,
    activeUsers: 128,
    totalRequests: 15420,
    errorRate: 0.3
  };

  useEffect(() => {
    loadSystemStatus();
  }, []);

  const loadSystemStatus = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      setSystemStatus(mockSystemStatus);
    } catch (error) {
      message.error('系统状态加载失败');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = () => {
    loadSystemStatus();
  };

  const handleSystemRestart = () => {
    message.info('系统重启功能需要超级管理员权限');
  };

  const handleClearCache = () => {
    message.success('系统缓存已清理');
  };

  const getStatusColor = (value: number, type: 'usage' | 'rate') => {
    if (type === 'usage') {
      if (value > 80) return '#f5222d';
      if (value > 60) return '#fa8c16';
      return '#52c41a';
    } else {
      if (value > 1) return '#f5222d';
      if (value > 0.5) return '#fa8c16';
      return '#52c41a';
    }
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className={styles.content}>
          <div className={styles.loadingContainer}>
            <Spin size="large" />
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className={styles.content}>
        <div className={styles.header}>
          <Title level={2}>系统管理</Title>
          <Space>
            <Button icon={<ReloadOutlined />} onClick={handleRefresh}>
              刷新状态
            </Button>
            <Button icon={<SettingOutlined />}>
              系统设置
            </Button>
          </Space>
        </div>

        {/* 系统状态概览 */}
        <Row gutter={[16, 16]} className={styles.statsRow}>
          <Col xs={24} sm={12} lg={4}>
            <Card>
              <Statistic
                title="CPU 使用率"
                value={systemStatus?.cpuUsage}
                precision={1}
                suffix="%"
                valueStyle={{ color: getStatusColor(systemStatus?.cpuUsage || 0, 'usage') }}
                prefix={<MonitorOutlined />}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={4}>
            <Card>
              <Statistic
                title="内存使用率"
                value={systemStatus?.memoryUsage}
                precision={1}
                suffix="%"
                valueStyle={{ color: getStatusColor(systemStatus?.memoryUsage || 0, 'usage') }}
                prefix={<DatabaseOutlined />}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={4}>
            <Card>
              <Statistic
                title="磁盘使用率"
                value={systemStatus?.diskUsage}
                precision={1}
                suffix="%"
                valueStyle={{ color: getStatusColor(systemStatus?.diskUsage || 0, 'usage') }}
                prefix={<DatabaseOutlined />}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={4}>
            <Card>
              <Statistic
                title="在线用户"
                value={systemStatus?.activeUsers}
                valueStyle={{ color: '#1890ff' }}
                prefix={<SecurityScanOutlined />}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={4}>
            <Card>
              <Statistic
                title="总请求数"
                value={systemStatus?.totalRequests}
                valueStyle={{ color: '#52c41a' }}
                prefix={<MonitorOutlined />}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={4}>
            <Card>
              <Statistic
                title="错误率"
                value={systemStatus?.errorRate}
                precision={2}
                suffix="%"
                valueStyle={{ color: getStatusColor(systemStatus?.errorRate || 0, 'rate') }}
                prefix={<BugOutlined />}
              />
            </Card>
          </Col>
        </Row>

        {/* 系统警告 */}
        {systemStatus && systemStatus.memoryUsage > 70 && (
          <Alert
            message="内存使用率较高"
            description="当前内存使用率超过70%，建议清理缓存或重启系统"
            type="warning"
            showIcon
            closable
            style={{ marginBottom: 16 }}
            action={
              <Space>
                <Button size="small" onClick={handleClearCache}>
                  清理缓存
                </Button>
                <Button size="small" type="primary" onClick={handleSystemRestart}>
                  重启系统
                </Button>
              </Space>
            }
          />
        )}

        {/* 管理功能标签页 */}
        <Tabs defaultActiveKey="data">
          <TabPane
            tab={
              <span>
                <DatabaseOutlined />
                数据管理
              </span>
            }
            key="data"
          >
            <DataManagement onRefresh={handleRefresh} />
          </TabPane>

          <TabPane
            tab={
              <span>
                <MonitorOutlined />
                性能监控
              </span>
            }
            key="monitor"
          >
            <Card title="性能监控">
              <Row gutter={[16, 16]}>
                <Col span={24}>
                  <Alert
                    message="性能监控功能"
                    description="实时监控系统性能指标，包括CPU、内存、网络等使用情况"
                    type="info"
                    showIcon
                  />
                </Col>
              </Row>
            </Card>
          </TabPane>

          <TabPane
            tab={
              <span>
                <SecurityScanOutlined />
                安全管理
              </span>
            }
            key="security"
          >
            <Card title="安全管理">
              <Row gutter={[16, 16]}>
                <Col span={24}>
                  <Alert
                    message="安全管理功能"
                    description="管理系统安全设置，包括访问控制、安全日志、威胁检测等"
                    type="info"
                    showIcon
                  />
                </Col>
              </Row>
            </Card>
          </TabPane>

          <TabPane
            tab={
              <span>
                <BugOutlined />
                日志管理
              </span>
            }
            key="logs"
          >
            <Card title="日志管理">
              <Row gutter={[16, 16]}>
                <Col span={24}>
                  <Alert
                    message="日志管理功能"
                    description="查看和管理系统日志，包括访问日志、错误日志、操作日志等"
                    type="info"
                    showIcon
                  />
                </Col>
              </Row>
            </Card>
          </TabPane>

          <TabPane
            tab={
              <span>
                <SettingOutlined />
                系统配置
              </span>
            }
            key="config"
          >
            <Card title="系统配置">
              <Row gutter={[16, 16]}>
                <Col span={24}>
                  <Alert
                    message="系统配置功能"
                    description="管理系统全局配置，包括邮件设置、存储配置、API限制等"
                    type="info"
                    showIcon
                  />
                </Col>
              </Row>
            </Card>
          </TabPane>
        </Tabs>
      </div>
    </AdminLayout>
  );
};
