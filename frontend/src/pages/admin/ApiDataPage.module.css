.container {
  padding: 24px;
  background: #fff;
  min-height: calc(100vh - 64px);
}

.header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e8e8e8;
}

.header h2 {
  margin-bottom: 8px !important;
  color: #333 !important;
}

.header p {
  color: #666 !important;
  margin-bottom: 0 !important;
}

/* 表格样式优化 */
.container .ant-table {
  background: #fff !important;
}

.container .ant-table-thead > tr > th {
  background: #fafafa !important;
  color: #333 !important;
  font-weight: 500 !important;
  border-bottom: 1px solid #e8e8e8 !important;
}

.container .ant-table-tbody > tr > td {
  background: #fff !important;
  color: #333 !important;
  border-bottom: 1px solid #f0f0f0 !important;
}

.container .ant-table-tbody > tr:hover > td {
  background: #f5f5f5 !important;
}

/* 标签样式 */
.container .ant-tag {
  border-radius: 4px !important;
  font-size: 12px !important;
  line-height: 1.2 !important;
  padding: 2px 6px !important;
}

/* 卡片样式 */
.container .ant-card {
  background: #fff !important;
  border: 1px solid #e8e8e8 !important;
  border-radius: 6px !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02) !important;
}

.container .ant-card-head {
  background: #fafafa !important;
  border-bottom: 1px solid #e8e8e8 !important;
}

.container .ant-card-head-title {
  color: #333 !important;
  font-weight: 500 !important;
}

/* 标签页样式 */
.container .ant-tabs-tab {
  color: #666 !important;
}

.container .ant-tabs-tab-active {
  color: #333 !important;
}

.container .ant-tabs-tab:hover {
  color: #333 !important;
}

.container .ant-tabs-ink-bar {
  background: #333 !important;
}

/* 折叠面板样式 */
.container .ant-collapse {
  background: #fff !important;
  border: 1px solid #e8e8e8 !important;
}

.container .ant-collapse-header {
  background: #fafafa !important;
  color: #333 !important;
  font-weight: 500 !important;
}

.container .ant-collapse-content {
  background: #fff !important;
  border-top: 1px solid #e8e8e8 !important;
}

/* 描述列表样式 */
.container .ant-descriptions-item-label {
  color: #666 !important;
  font-weight: 500 !important;
}

.container .ant-descriptions-item-content {
  color: #333 !important;
}

/* 警告框样式 */
.container .ant-alert {
  border-radius: 6px !important;
}

.container .ant-alert-info {
  background: #f6f8ff !important;
  border: 1px solid #d6e3ff !important;
}

.container .ant-alert-info .ant-alert-icon {
  color: #1890ff !important;
}

/* 徽章样式 */
.container .ant-badge-count {
  background: #52c41a !important;
  color: #fff !important;
  font-size: 12px !important;
  min-width: 20px !important;
  height: 20px !important;
  line-height: 18px !important;
}

/* 代码样式 */
.container .ant-typography code {
  background: #f5f5f5 !important;
  border: 1px solid #e8e8e8 !important;
  color: #333 !important;
  padding: 2px 6px !important;
  border-radius: 3px !important;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
  font-size: 12px !important;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .container {
    padding: 16px;
  }
  
  .container .ant-table {
    font-size: 12px;
  }
  
  .container .ant-tag {
    font-size: 10px !important;
    padding: 1px 4px !important;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 12px;
  }
  
  .header h2 {
    font-size: 18px !important;
  }
  
  .container .ant-table {
    font-size: 11px;
  }
  
  .container .ant-tabs-tab {
    padding: 8px 12px !important;
    font-size: 12px !important;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 8px;
  }
  
  .header {
    margin-bottom: 16px;
    padding-bottom: 12px;
  }
  
  .header h2 {
    font-size: 16px !important;
  }
  
  .header p {
    font-size: 12px !important;
  }
}
