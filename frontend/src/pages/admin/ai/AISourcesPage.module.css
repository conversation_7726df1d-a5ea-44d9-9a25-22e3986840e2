/* AI水源配置页面样式 */

.container {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.header {
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header h2 {
  margin-bottom: 8px;
  color: #1890ff;
}

.statsCard {
  text-align: center;
  padding: 16px;
}

.statsCard .ant-statistic-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.statsCard .ant-statistic-content {
  font-size: 24px;
  font-weight: 600;
}

.actionBar {
  margin-bottom: 16px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.sourceTable {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.sourceTable .ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
  color: #333;
}

.sourceTable .ant-table-tbody > tr:hover > td {
  background: #f0f9ff;
}

.statusTag {
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.healthProgress {
  width: 80px;
}

.usageInfo {
  font-size: 12px;
  color: #666;
}

.actionButtons {
  display: flex;
  gap: 4px;
}

.actionButtons .ant-btn {
  border: none;
  box-shadow: none;
}

.actionButtons .ant-btn:hover {
  background: #f0f9ff;
  color: #1890ff;
}

.actionButtons .ant-btn.ant-btn-dangerous:hover {
  background: #fff2f0;
  color: #ff4d4f;
}

.modal .ant-form-item-label > label {
  font-weight: 600;
}

.modal .ant-input,
.modal .ant-select-selector,
.modal .ant-input-number {
  border-radius: 6px;
}

.modal .ant-input:focus,
.modal .ant-select-focused .ant-select-selector,
.modal .ant-input-number:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }
  
  .header {
    padding: 16px;
  }
  
  .statsCard {
    margin-bottom: 16px;
  }
  
  .actionBar {
    padding: 12px;
  }
  
  .actionButtons {
    flex-direction: column;
    gap: 8px;
  }
  
  .modal {
    margin: 0;
    max-width: 100vw;
  }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .container {
    background: #141414;
  }
  
  .header,
  .actionBar,
  .sourceTable {
    background: #1f1f1f;
    border: 1px solid #303030;
  }
  
  .header h2 {
    color: #1890ff;
  }
  
  .sourceTable .ant-table-thead > tr > th {
    background: #262626;
    color: #fff;
    border-bottom: 1px solid #303030;
  }
  
  .sourceTable .ant-table-tbody > tr > td {
    border-bottom: 1px solid #303030;
    color: #fff;
  }
  
  .sourceTable .ant-table-tbody > tr:hover > td {
    background: #262626;
  }
}

/* 动画效果 */
.container .ant-card {
  transition: all 0.3s ease;
}

.container .ant-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.actionButtons .ant-btn {
  transition: all 0.2s ease;
}

.healthProgress .ant-progress-bg {
  transition: all 0.3s ease;
}

/* 状态指示器 */
.statusIndicator {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.statusIndicator.active {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.statusIndicator.inactive {
  background: #f5f5f5;
  color: #8c8c8c;
  border: 1px solid #d9d9d9;
}

.statusIndicator.error {
  background: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

.statusIndicator.maintenance {
  background: #fff7e6;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

/* 加载状态 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #666;
}

.loading .ant-spin {
  margin-right: 8px;
}

/* 空状态 */
.empty {
  text-align: center;
  padding: 48px 24px;
  color: #666;
}

.empty .ant-empty-image {
  margin-bottom: 16px;
}

.empty .ant-empty-description {
  margin-bottom: 16px;
  font-size: 14px;
}

/* 工具提示样式 */
.tooltip {
  max-width: 300px;
}

.tooltip .ant-tooltip-inner {
  border-radius: 6px;
}

/* 表格自定义样式 */
.sourceTable .ant-table-pagination {
  margin: 16px 0;
  text-align: right;
}

.sourceTable .ant-pagination-total-text {
  color: #666;
  font-size: 14px;
}

/* 模态框样式 */
.modal .ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 24px;
}

.modal .ant-modal-title {
  font-size: 16px;
  font-weight: 600;
  color: #1890ff;
}

.modal .ant-modal-body {
  padding: 24px;
}

.modal .ant-modal-footer {
  border-top: 1px solid #f0f0f0;
  padding: 10px 16px;
  text-align: right;
}

/* 表单样式优化 */
.modal .ant-form-item {
  margin-bottom: 16px;
}

.modal .ant-form-item-label {
  padding-bottom: 4px;
}

.modal .ant-form-item-control-input {
  min-height: 32px;
}

/* 统计卡片样式 */
.statsCard .ant-statistic {
  text-align: center;
}

.statsCard .ant-statistic-title {
  color: #666;
  font-size: 14px;
  margin-bottom: 4px;
}

.statsCard .ant-statistic-content {
  color: #1890ff;
  font-size: 20px;
  font-weight: 600;
}

.statsCard .ant-statistic-content-prefix {
  margin-right: 4px;
  color: #1890ff;
}
