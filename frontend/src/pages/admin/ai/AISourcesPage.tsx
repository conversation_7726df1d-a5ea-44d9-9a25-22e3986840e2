/**
 * AI水源配置页面
 * 
 * 管理AI服务提供商的配置、监控和状态管理
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  InputNumber,
  message,
  Tooltip,
  Progress,
  Statistic,
  Row,
  Col,
  Typography,
  Divider
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  ReloadOutlined,
  ThunderboltOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined
} from '@ant-design/icons';
import { AdminLayout } from '../../../components/layout/RoleBasedLayout';
// import { aiWaterManagementService } from '../../../services/aiWaterManagementService';
import { mockAIService } from '../../../services/mockAIService';
import type { AISource, AISourceConfig, AISourceStatus, AIProvider } from '../../../types/ai-water-management';
import styles from './AISourcesPage.module.css';

const { Title, Text } = Typography;
const { Option } = Select;

export const AISourcesPage: React.FC = () => {
  // 状态管理
  const [sources, setSources] = useState<AISource[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingSource, setEditingSource] = useState<AISource | null>(null);
  const [testingSource, setTestingSource] = useState<string | null>(null);
  const [form] = Form.useForm();

  // 加载AI水源列表
  const loadSources = async () => {
    setLoading(true);
    try {
      const data = await mockAIService.getAllSources();
      setSources(data);
    } catch (error) {
      message.error('加载AI水源失败');
      console.error('加载AI水源失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 组件挂载时加载数据
  useEffect(() => {
    loadSources();
  }, []);

  // 测试连接
  const handleTestConnection = async (sourceId: string) => {
    setTestingSource(sourceId);
    try {
      const result = await mockAIService.testSourceConnection(sourceId);
      if (result.success) {
        message.success(`连接测试成功，响应时间: ${result.responseTime}ms`);
      } else {
        message.error(`连接测试失败: ${result.error}`);
      }
    } catch (error) {
      message.error('连接测试失败');
    } finally {
      setTestingSource(null);
    }
  };

  // 切换水源状态
  const handleToggleStatus = async (source: AISource) => {
    try {
      const newStatus = source.status === 'active' ? 'inactive' : 'active';
      await aiWaterManagementService.updateSourceStatus(source.id, newStatus as AISourceStatus);
      message.success(`${source.name} 已${newStatus === 'active' ? '启用' : '禁用'}`);
      loadSources();
    } catch (error) {
      message.error('状态更新失败');
    }
  };

  // 删除水源
  const handleDeleteSource = async (source: AISource) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除AI水源 "${source.name}" 吗？此操作不可恢复。`,
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await aiWaterManagementService.removeSource(source.id);
          message.success('AI水源删除成功');
          loadSources();
        } catch (error) {
          message.error('删除失败');
        }
      }
    });
  };

  // 打开编辑模态框
  const handleEdit = (source?: AISource) => {
    setEditingSource(source || null);
    setModalVisible(true);
    
    if (source) {
      form.setFieldsValue({
        name: source.name,
        provider: source.provider,
        type: source.type,
        endpoint: source.config.endpoint,
        model: source.config.model,
        maxConcurrent: source.config.maxConcurrent,
        rateLimit: source.config.rateLimit,
        costPerToken: source.config.costPerToken,
        timeout: source.config.timeout
      });
    } else {
      form.resetFields();
    }
  };

  // 保存水源配置
  const handleSave = async (values: any) => {
    try {
      const config: AISourceConfig = {
        id: editingSource?.id || `${values.provider}_${Date.now()}`,
        name: values.name,
        type: values.type,
        provider: values.provider,
        status: 'inactive',
        config: {
          apiKey: values.apiKey || '',
          endpoint: values.endpoint,
          model: values.model,
          maxConcurrent: values.maxConcurrent,
          rateLimit: values.rateLimit,
          costPerToken: values.costPerToken,
          timeout: values.timeout,
          retryAttempts: 3,
          retryDelay: 1000
        },
        features: {
          streaming: true,
          functionCalling: false,
          imageGeneration: false,
          codeExecution: false,
          multimodal: false
        },
        limits: {
          maxTokensPerRequest: 4096,
          maxRequestsPerMinute: values.rateLimit,
          maxTokensPerMinute: values.rateLimit * 1000,
          maxRequestsPerDay: values.rateLimit * 1440
        }
      };

      if (editingSource) {
        await aiWaterManagementService.updateSource(editingSource.id, config);
        message.success('AI水源更新成功');
      } else {
        await aiWaterManagementService.addSource(config);
        message.success('AI水源添加成功');
      }

      setModalVisible(false);
      loadSources();
    } catch (error) {
      message.error(editingSource ? '更新失败' : '添加失败');
    }
  };

  // 获取状态标签
  const getStatusTag = (status: AISourceStatus) => {
    const statusConfig = {
      active: { color: 'green', icon: <CheckCircleOutlined />, text: '运行中' },
      inactive: { color: 'default', icon: <PauseCircleOutlined />, text: '已停用' },
      error: { color: 'red', icon: <CloseCircleOutlined />, text: '错误' },
      maintenance: { color: 'orange', icon: <ExclamationCircleOutlined />, text: '维护中' }
    };
    
    const config = statusConfig[status];
    return (
      <Tag color={config.color} icon={config.icon}>
        {config.text}
      </Tag>
    );
  };

  // 表格列定义
  const columns = [
    {
      title: '水源名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: AISource) => (
        <Space>
          <ThunderboltOutlined style={{ color: '#1890ff' }} />
          <strong>{text}</strong>
          <Text type="secondary">({record.provider.toUpperCase()})</Text>
        </Space>
      )
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => {
        const typeConfig = {
          primary: { color: 'blue', text: '主要' },
          secondary: { color: 'green', text: '次要' },
          backup: { color: 'orange', text: '备用' }
        };
        const config = typeConfig[type as keyof typeof typeConfig];
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: AISourceStatus) => getStatusTag(status)
    },
    {
      title: '健康度',
      dataIndex: 'health',
      key: 'health',
      render: (health: any) => (
        <Space direction="vertical" size="small">
          <Progress
            percent={health.uptime}
            size="small"
            status={health.uptime > 95 ? 'success' : health.uptime > 80 ? 'normal' : 'exception'}
            format={() => `${health.uptime}%`}
          />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            响应: {health.responseTime}ms
          </Text>
        </Space>
      )
    },
    {
      title: '今日使用',
      dataIndex: 'usage',
      key: 'usage',
      render: (usage: any) => (
        <Space direction="vertical" size="small">
          <Text>{usage.requestsToday} 次请求</Text>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            成本: ${usage.costToday.toFixed(2)}
          </Text>
        </Space>
      )
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record: AISource) => (
        <Space>
          <Tooltip title="测试连接">
            <Button
              type="text"
              icon={<ReloadOutlined />}
              loading={testingSource === record.id}
              onClick={() => handleTestConnection(record.id)}
            />
          </Tooltip>
          <Tooltip title={record.status === 'active' ? '停用' : '启用'}>
            <Button
              type="text"
              icon={record.status === 'active' ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
              onClick={() => handleToggleStatus(record)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleDeleteSource(record)}
            />
          </Tooltip>
        </Space>
      )
    }
  ];

  // 统计数据
  const stats = {
    total: sources.length,
    active: sources.filter(s => s.status === 'active').length,
    totalRequests: sources.reduce((sum, s) => sum + s.usage.requestsToday, 0),
    totalCost: sources.reduce((sum, s) => sum + s.usage.costToday, 0)
  };

  return (
    <AdminLayout>
      <div className={styles.container}>
        <div className={styles.header}>
          <Title level={2}>
            <ThunderboltOutlined /> AI水源配置
          </Title>
          <Text type="secondary">
            管理AI服务提供商的配置、监控和状态
          </Text>
        </div>

        {/* 统计卡片 */}
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={6}>
            <Card>
              <Statistic
                title="总水源数"
                value={stats.total}
                prefix={<ThunderboltOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="运行中"
                value={stats.active}
                valueStyle={{ color: '#3f8600' }}
                prefix={<CheckCircleOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="今日请求"
                value={stats.totalRequests}
                prefix={<ReloadOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="今日成本"
                value={stats.totalCost}
                precision={2}
                prefix="$"
                valueStyle={{ color: '#cf1322' }}
              />
            </Card>
          </Col>
        </Row>

        {/* 操作栏 */}
        <Card style={{ marginBottom: 16 }}>
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => handleEdit()}
            >
              添加AI水源
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={loadSources}
              loading={loading}
            >
              刷新
            </Button>
          </Space>
        </Card>

        {/* 水源列表 */}
        <Card>
          <Table
            columns={columns}
            dataSource={sources}
            rowKey="id"
            loading={loading}
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 个AI水源`
            }}
          />
        </Card>

        {/* 编辑模态框 */}
        <Modal
          title={editingSource ? '编辑AI水源' : '添加AI水源'}
          open={modalVisible}
          onCancel={() => setModalVisible(false)}
          onOk={() => form.submit()}
          width={600}
        >
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSave}
          >
            <Form.Item
              name="name"
              label="水源名称"
              rules={[{ required: true, message: '请输入水源名称' }]}
            >
              <Input placeholder="例如: OpenAI GPT-4" />
            </Form.Item>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="provider"
                  label="服务提供商"
                  rules={[{ required: true, message: '请选择服务提供商' }]}
                >
                  <Select placeholder="选择提供商">
                    <Option value="openai">OpenAI</Option>
                    <Option value="grok">Grok</Option>
                    <Option value="gemini">Google Gemini</Option>
                    <Option value="claude">Anthropic Claude</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="type"
                  label="水源类型"
                  rules={[{ required: true, message: '请选择水源类型' }]}
                >
                  <Select placeholder="选择类型">
                    <Option value="primary">主要</Option>
                    <Option value="secondary">次要</Option>
                    <Option value="backup">备用</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              name="endpoint"
              label="API端点"
              rules={[{ required: true, message: '请输入API端点' }]}
            >
              <Input placeholder="https://api.openai.com/v1/chat/completions" />
            </Form.Item>

            <Form.Item
              name="model"
              label="模型名称"
              rules={[{ required: true, message: '请输入模型名称' }]}
            >
              <Input placeholder="gpt-4" />
            </Form.Item>

            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  name="maxConcurrent"
                  label="最大并发"
                  rules={[{ required: true, message: '请输入最大并发数' }]}
                >
                  <InputNumber min={1} max={100} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="rateLimit"
                  label="速率限制(/分钟)"
                  rules={[{ required: true, message: '请输入速率限制' }]}
                >
                  <InputNumber min={1} max={1000} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="timeout"
                  label="超时时间(ms)"
                  rules={[{ required: true, message: '请输入超时时间' }]}
                >
                  <InputNumber min={1000} max={60000} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              name="costPerToken"
              label="每Token成本($)"
              rules={[{ required: true, message: '请输入每Token成本' }]}
            >
              <InputNumber
                min={0}
                max={1}
                step={0.000001}
                precision={6}
                style={{ width: '100%' }}
              />
            </Form.Item>
          </Form>
        </Modal>
      </div>
    </AdminLayout>
  );
};
