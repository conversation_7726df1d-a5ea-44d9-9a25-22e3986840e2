import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  Space, 
  Tag, 
  Modal, 
  Form, 
  Input, 
  Select, 
  Typography, 
  Row,
  Col,
  Statistic,
  message,
  Popconfirm,
  Tooltip
} from 'antd';
import { 
  UserAddOutlined, 
  EditOutlined, 
  DeleteOutlined,
  SafetyOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  TeamOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { AdminLayout } from '../../components/layout/RoleBasedLayout';
import { useAuthStore } from '../../stores/authStore';
import styles from './AdminPages.module.css';

const { Title, Paragraph, Text } = Typography;
const { Option } = Select;

interface Reviewer {
  id: string;
  username: string;
  email: string;
  realName: string;
  status: 'active' | 'inactive' | 'suspended';
  createdAt: string;
  lastLoginAt?: string;
  reviewStats: {
    totalReviews: number;
    approvedReviews: number;
    rejectedReviews: number;
    avgReviewTime: number;
  };
  permissions: string[];
}

export const ReviewerManagementPage: React.FC = () => {
  const { user } = useAuthStore();
  const [loading, setLoading] = useState(false);
  const [reviewers, setReviewers] = useState<Reviewer[]>([]);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [selectedReviewer, setSelectedReviewer] = useState<Reviewer | null>(null);
  const [createForm] = Form.useForm();
  const [editForm] = Form.useForm();

  // 模拟数据
  useEffect(() => {
    const mockData: Reviewer[] = [
      {
        id: '1',
        username: 'reviewer001',
        email: '<EMAIL>',
        realName: '张审核',
        status: 'active',
        createdAt: '2024-01-15 10:00:00',
        lastLoginAt: '2024-01-27 14:30:00',
        reviewStats: {
          totalReviews: 156,
          approvedReviews: 142,
          rejectedReviews: 14,
          avgReviewTime: 4.2
        },
        permissions: ['review_questionnaires', 'review_stories', 'review_voices']
      },
      {
        id: '2',
        username: 'reviewer002',
        email: '<EMAIL>',
        realName: '李审核',
        status: 'active',
        createdAt: '2024-01-10 09:30:00',
        lastLoginAt: '2024-01-27 11:15:00',
        reviewStats: {
          totalReviews: 89,
          approvedReviews: 78,
          rejectedReviews: 11,
          avgReviewTime: 5.8
        },
        permissions: ['review_questionnaires', 'review_stories']
      },
      {
        id: '3',
        username: 'reviewer003',
        email: '<EMAIL>',
        realName: '王审核',
        status: 'inactive',
        createdAt: '2024-01-05 16:20:00',
        lastLoginAt: '2024-01-20 08:45:00',
        reviewStats: {
          totalReviews: 45,
          approvedReviews: 40,
          rejectedReviews: 5,
          avgReviewTime: 3.5
        },
        permissions: ['review_voices']
      }
    ];
    setReviewers(mockData);
  }, []);

  const getStatusTag = (status: string) => {
    switch (status) {
      case 'active':
        return <Tag color="green" icon={<CheckCircleOutlined />}>活跃</Tag>;
      case 'inactive':
        return <Tag color="orange">不活跃</Tag>;
      case 'suspended':
        return <Tag color="red" icon={<CloseCircleOutlined />}>已停用</Tag>;
      default:
        return <Tag>未知</Tag>;
    }
  };

  const getPermissionTags = (permissions: string[]) => {
    const permissionMap: Record<string, { label: string; color: string }> = {
      'review_questionnaires': { label: '问卷审核', color: 'blue' },
      'review_stories': { label: '故事审核', color: 'green' },
      'review_voices': { label: '心声审核', color: 'purple' }
    };

    return permissions.map(permission => {
      const config = permissionMap[permission];
      return config ? (
        <Tag key={permission} color={config.color} style={{ fontSize: '11px' }}>
          {config.label}
        </Tag>
      ) : null;
    });
  };

  const handleCreateReviewer = async (values: any) => {
    setLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));

      const newReviewer: Reviewer = {
        id: Date.now().toString(),
        username: values.username,
        email: values.email,
        realName: values.realName,
        status: 'active',
        createdAt: new Date().toLocaleString(),
        reviewStats: {
          totalReviews: 0,
          approvedReviews: 0,
          rejectedReviews: 0,
          avgReviewTime: 0
        },
        permissions: values.permissions || []
      };

      setReviewers(prev => [...prev, newReviewer]);
      message.success('审核员创建成功');
      setCreateModalVisible(false);
      createForm.resetFields();
    } catch (error) {
      message.error('创建失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const handleEditReviewer = async (values: any) => {
    if (!selectedReviewer) return;

    setLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));

      setReviewers(prev => prev.map(reviewer => 
        reviewer.id === selectedReviewer.id 
          ? { ...reviewer, ...values }
          : reviewer
      ));

      message.success('审核员信息更新成功');
      setEditModalVisible(false);
      setSelectedReviewer(null);
    } catch (error) {
      message.error('更新失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteReviewer = async (reviewerId: string) => {
    setLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));

      setReviewers(prev => prev.filter(reviewer => reviewer.id !== reviewerId));
      message.success('审核员删除成功');
    } catch (error) {
      message.error('删除失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const handleEditClick = (reviewer: Reviewer) => {
    setSelectedReviewer(reviewer);
    editForm.setFieldsValue(reviewer);
    setEditModalVisible(true);
  };

  const columns = [
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
      width: 120,
      render: (username: string) => (
        <Space>
          <SafetyOutlined />
          <Text strong>{username}</Text>
        </Space>
      )
    },
    {
      title: '真实姓名',
      dataIndex: 'realName',
      key: 'realName',
      width: 100
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
      width: 180,
      ellipsis: true
    },
    {
      title: '权限',
      dataIndex: 'permissions',
      key: 'permissions',
      width: 200,
      render: (permissions: string[]) => (
        <Space wrap>
          {getPermissionTags(permissions)}
        </Space>
      )
    },
    {
      title: '审核统计',
      key: 'reviewStats',
      width: 150,
      render: (_, record: Reviewer) => (
        <Space direction="vertical" size={0}>
          <Text style={{ fontSize: '12px' }}>
            总计: {record.reviewStats.totalReviews}
          </Text>
          <Text style={{ fontSize: '12px' }}>
            通过率: {record.reviewStats.totalReviews > 0 
              ? ((record.reviewStats.approvedReviews / record.reviewStats.totalReviews) * 100).toFixed(1)
              : 0}%
          </Text>
        </Space>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => getStatusTag(status),
      filters: [
        { text: '活跃', value: 'active' },
        { text: '不活跃', value: 'inactive' },
        { text: '已停用', value: 'suspended' }
      ],
      onFilter: (value: any, record: Reviewer) => record.status === value
    },
    {
      title: '最后登录',
      dataIndex: 'lastLoginAt',
      key: 'lastLoginAt',
      width: 150,
      render: (lastLoginAt: string) => lastLoginAt || '从未登录'
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record: Reviewer) => (
        <Space>
          <Tooltip title="编辑">
            <Button
              type="link"
              icon={<EditOutlined />}
              onClick={() => handleEditClick(record)}
              size="small"
            />
          </Tooltip>
          <Tooltip title="查看详情">
            <Button
              type="link"
              icon={<EyeOutlined />}
              size="small"
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个审核员吗？"
            onConfirm={() => handleDeleteReviewer(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                type="link"
                icon={<DeleteOutlined />}
                danger
                size="small"
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      )
    }
  ];

  // 统计数据
  const stats = {
    total: reviewers.length,
    active: reviewers.filter(r => r.status === 'active').length,
    inactive: reviewers.filter(r => r.status === 'inactive').length,
    suspended: reviewers.filter(r => r.status === 'suspended').length,
    totalReviews: reviewers.reduce((sum, r) => sum + r.reviewStats.totalReviews, 0),
    avgApprovalRate: reviewers.length > 0 
      ? reviewers.reduce((sum, r) => {
          const rate = r.reviewStats.totalReviews > 0 
            ? (r.reviewStats.approvedReviews / r.reviewStats.totalReviews) * 100 
            : 0;
          return sum + rate;
        }, 0) / reviewers.length
      : 0
  };

  return (
    <AdminLayout>
      <div className={styles.container}>
        <div className={styles.header}>
          <Title level={2}>
            <TeamOutlined /> 审核员管理
          </Title>
          <Paragraph>
            管理审核员账号，分配审核权限，监控审核工作质量。
          </Paragraph>
        </div>

        {/* 统计卡片 */}
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="总审核员"
                value={stats.total}
                prefix={<TeamOutlined />}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="活跃审核员"
                value={stats.active}
                prefix={<CheckCircleOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="总审核数"
                value={stats.totalReviews}
                prefix={<BarChartOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="平均通过率"
                value={stats.avgApprovalRate.toFixed(1)}
                suffix="%"
                prefix={<CheckCircleOutlined />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
        </Row>

        {/* 审核员列表 */}
        <Card 
          title="审核员列表"
          extra={
            <Button
              type="primary"
              icon={<UserAddOutlined />}
              onClick={() => setCreateModalVisible(true)}
            >
              添加审核员
            </Button>
          }
        >
          <Table
            columns={columns}
            dataSource={reviewers}
            rowKey="id"
            loading={loading}
            pagination={{
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => 
                `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
            }}
          />
        </Card>

        {/* 创建审核员弹窗 */}
        <Modal
          title="添加审核员"
          open={createModalVisible}
          onCancel={() => setCreateModalVisible(false)}
          footer={null}
          destroyOnClose
        >
          <Form
            form={createForm}
            layout="vertical"
            onFinish={handleCreateReviewer}
          >
            <Form.Item
              name="username"
              label="用户名"
              rules={[
                { required: true, message: '请输入用户名' },
                { min: 3, message: '用户名至少3个字符' }
              ]}
            >
              <Input placeholder="请输入用户名" />
            </Form.Item>

            <Form.Item
              name="realName"
              label="真实姓名"
              rules={[{ required: true, message: '请输入真实姓名' }]}
            >
              <Input placeholder="请输入真实姓名" />
            </Form.Item>

            <Form.Item
              name="email"
              label="邮箱"
              rules={[
                { required: true, message: '请输入邮箱' },
                { type: 'email', message: '请输入有效的邮箱地址' }
              ]}
            >
              <Input placeholder="请输入邮箱地址" />
            </Form.Item>

            <Form.Item
              name="permissions"
              label="审核权限"
              rules={[{ required: true, message: '请选择审核权限' }]}
            >
              <Select
                mode="multiple"
                placeholder="请选择审核权限"
                options={[
                  { label: '问卷审核', value: 'review_questionnaires' },
                  { label: '故事审核', value: 'review_stories' },
                  { label: '心声审核', value: 'review_voices' }
                ]}
              />
            </Form.Item>

            <Form.Item>
              <Space>
                <Button type="primary" htmlType="submit" loading={loading}>
                  创建审核员
                </Button>
                <Button onClick={() => setCreateModalVisible(false)}>
                  取消
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Modal>

        {/* 编辑审核员弹窗 */}
        <Modal
          title="编辑审核员"
          open={editModalVisible}
          onCancel={() => setEditModalVisible(false)}
          footer={null}
          destroyOnClose
        >
          <Form
            form={editForm}
            layout="vertical"
            onFinish={handleEditReviewer}
          >
            <Form.Item
              name="realName"
              label="真实姓名"
              rules={[{ required: true, message: '请输入真实姓名' }]}
            >
              <Input placeholder="请输入真实姓名" />
            </Form.Item>

            <Form.Item
              name="email"
              label="邮箱"
              rules={[
                { required: true, message: '请输入邮箱' },
                { type: 'email', message: '请输入有效的邮箱地址' }
              ]}
            >
              <Input placeholder="请输入邮箱地址" />
            </Form.Item>

            <Form.Item
              name="status"
              label="状态"
              rules={[{ required: true, message: '请选择状态' }]}
            >
              <Select placeholder="请选择状态">
                <Option value="active">活跃</Option>
                <Option value="inactive">不活跃</Option>
                <Option value="suspended">已停用</Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="permissions"
              label="审核权限"
              rules={[{ required: true, message: '请选择审核权限' }]}
            >
              <Select
                mode="multiple"
                placeholder="请选择审核权限"
                options={[
                  { label: '问卷审核', value: 'review_questionnaires' },
                  { label: '故事审核', value: 'review_stories' },
                  { label: '心声审核', value: 'review_voices' }
                ]}
              />
            </Form.Item>

            <Form.Item>
              <Space>
                <Button type="primary" htmlType="submit" loading={loading}>
                  更新信息
                </Button>
                <Button onClick={() => setEditModalVisible(false)}>
                  取消
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Modal>
      </div>
    </AdminLayout>
  );
};
