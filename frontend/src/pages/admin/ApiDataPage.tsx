import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Table, 
  Tabs, 
  Tag, 
  Button, 
  Space, 
  Typography, 
  Descriptions,
  Alert,
  Collapse,
  Badge,
  Tooltip
} from 'antd';
import { 
  ApiOutlined, 
  DatabaseOutlined, 
  TableOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { AdminLayout } from '../../components/layout/RoleBasedLayout';
import styles from './ApiDataPage.module.css';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { Panel } = Collapse;

interface ApiEndpoint {
  id: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  path: string;
  description: string;
  page: string;
  database: string;
  tables: string[];
  status: 'active' | 'deprecated' | 'planned';
  parameters?: string[];
  response?: string;
}

interface DatabaseTable {
  name: string;
  type: 'temp' | 'valid' | 'analytics' | 'system';
  description: string;
  recordCount?: number;
  lastUpdated?: string;
  relatedApis: string[];
}

export const ApiDataPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [apiEndpoints] = useState<ApiEndpoint[]>([
    // 用户认证相关API
    {
      id: 'auth-login',
      method: 'POST',
      path: '/api/auth/login',
      description: '用户登录',
      page: '登录页面',
      database: 'main',
      tables: ['users'],
      status: 'active',
      parameters: ['username', 'password'],
      response: 'JWT token + user info'
    },
    {
      id: 'auth-register',
      method: 'POST',
      path: '/api/auth/register',
      description: '用户注册',
      page: '注册页面',
      database: 'main',
      tables: ['users'],
      status: 'active',
      parameters: ['username', 'email', 'password'],
      response: 'User info'
    },
    
    // 问卷相关API
    {
      id: 'questionnaire-submit',
      method: 'POST',
      path: '/api/questionnaire/submit',
      description: '提交问卷数据',
      page: '问卷页面',
      database: 'main',
      tables: ['questionnaire_submissions_temp'],
      status: 'active',
      parameters: ['questionnaire_data'],
      response: 'Submission ID'
    },
    {
      id: 'questionnaire-pending',
      method: 'GET',
      path: '/api/admin/questionnaire/pending',
      description: '获取待审核问卷',
      page: '管理后台',
      database: 'main',
      tables: ['questionnaire_submissions_temp'],
      status: 'active',
      parameters: ['page', 'limit'],
      response: 'Pending submissions list'
    },
    {
      id: 'questionnaire-approve',
      method: 'PUT',
      path: '/api/admin/questionnaire/:id/approve',
      description: '审核通过问卷',
      page: '管理后台',
      database: 'main',
      tables: ['questionnaire_submissions_temp', 'questionnaire_submissions'],
      status: 'active',
      parameters: ['id', 'review_notes'],
      response: 'Success status'
    },
    
    // 数据分析相关API
    {
      id: 'analytics-summary',
      method: 'GET',
      path: '/api/analytics/summary',
      description: '获取统计摘要',
      page: '数据可视化页面',
      database: 'main',
      tables: ['analytics_summary'],
      status: 'active',
      parameters: ['period'],
      response: 'Summary statistics'
    },
    {
      id: 'analytics-demographics',
      method: 'GET',
      path: '/api/analytics/demographics',
      description: '获取人口统计数据',
      page: '数据可视化页面',
      database: 'main',
      tables: ['analytics_demographics'],
      status: 'active',
      parameters: ['dimension', 'period'],
      response: 'Demographics data'
    },
    {
      id: 'analytics-employment',
      method: 'GET',
      path: '/api/analytics/employment',
      description: '获取就业统计数据',
      page: '数据可视化页面',
      database: 'main',
      tables: ['analytics_employment'],
      status: 'active',
      parameters: ['filters'],
      response: 'Employment statistics'
    },
    
    // 内容管理相关API
    {
      id: 'stories-list',
      method: 'GET',
      path: '/api/stories',
      description: '获取故事列表',
      page: '故事墙页面',
      database: 'main',
      tables: ['stories'],
      status: 'active',
      parameters: ['page', 'category'],
      response: 'Stories list'
    },
    {
      id: 'voices-list',
      method: 'GET',
      path: '/api/voices',
      description: '获取心声列表',
      page: '问卷心声页面',
      database: 'main',
      tables: ['voices'],
      status: 'active',
      parameters: ['page', 'mood'],
      response: 'Voices list'
    },
    
    // 系统管理相关API
    {
      id: 'users-list',
      method: 'GET',
      path: '/api/admin/users',
      description: '获取用户列表',
      page: '用户管理页面',
      database: 'main',
      tables: ['users'],
      status: 'active',
      parameters: ['page', 'role', 'status'],
      response: 'Users list'
    },
    {
      id: 'audit-logs',
      method: 'GET',
      path: '/api/admin/audit-logs',
      description: '获取审计日志',
      page: '管理后台',
      database: 'main',
      tables: ['audit_logs'],
      status: 'active',
      parameters: ['page', 'action', 'user_id'],
      response: 'Audit logs'
    }
  ]);

  const [databaseTables] = useState<DatabaseTable[]>([
    // 临时存储表 (表A)
    {
      name: 'questionnaire_submissions_temp',
      type: 'temp',
      description: '问卷临时存储表 - 用户提交的原始数据，等待审核',
      recordCount: 50,
      lastUpdated: '2024-01-27 10:30:00',
      relatedApis: ['questionnaire-submit', 'questionnaire-pending', 'questionnaire-approve']
    },
    
    // 有效数据表 (表B)
    {
      name: 'questionnaire_submissions',
      type: 'valid',
      description: '问卷有效数据表 - 经过审核的有效数据，用于统计分析',
      recordCount: 35,
      lastUpdated: '2024-01-27 09:45:00',
      relatedApis: ['questionnaire-approve']
    },
    
    // 可视化副表 (静态统计表)
    {
      name: 'analytics_summary',
      type: 'analytics',
      description: '总体统计摘要表 - 预计算的统计数据',
      recordCount: 12,
      lastUpdated: '2024-01-27 02:00:00',
      relatedApis: ['analytics-summary']
    },
    {
      name: 'analytics_demographics',
      type: 'analytics',
      description: '人口统计数据表 - 按维度分组的统计数据',
      recordCount: 85,
      lastUpdated: '2024-01-27 02:00:00',
      relatedApis: ['analytics-demographics']
    },
    {
      name: 'analytics_employment',
      type: 'analytics',
      description: '就业状况统计表 - 就业相关的统计数据',
      recordCount: 120,
      lastUpdated: '2024-01-27 02:00:00',
      relatedApis: ['analytics-employment']
    },
    {
      name: 'analytics_skills',
      type: 'analytics',
      description: '技能统计表 - 技能相关的统计数据',
      recordCount: 200,
      lastUpdated: '2024-01-27 02:00:00',
      relatedApis: []
    },
    
    // 系统管理表
    {
      name: 'users',
      type: 'system',
      description: '用户表 - 系统用户信息',
      recordCount: 7,
      lastUpdated: '2024-01-27 11:20:00',
      relatedApis: ['auth-login', 'auth-register', 'users-list']
    },
    {
      name: 'stories',
      type: 'system',
      description: '故事墙表 - 用户分享的就业故事',
      recordCount: 0,
      lastUpdated: '2024-01-27 00:00:00',
      relatedApis: ['stories-list']
    },
    {
      name: 'voices',
      type: 'system',
      description: '问卷心声表 - 用户的心声分享',
      recordCount: 0,
      lastUpdated: '2024-01-27 00:00:00',
      relatedApis: ['voices-list']
    },
    {
      name: 'audit_logs',
      type: 'system',
      description: '审计日志表 - 系统操作记录',
      recordCount: 0,
      lastUpdated: '2024-01-27 00:00:00',
      relatedApis: ['audit-logs']
    }
  ]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'green';
      case 'deprecated': return 'red';
      case 'planned': return 'blue';
      default: return 'default';
    }
  };

  const getTableTypeColor = (type: string) => {
    switch (type) {
      case 'temp': return 'orange';
      case 'valid': return 'green';
      case 'analytics': return 'blue';
      case 'system': return 'purple';
      default: return 'default';
    }
  };

  const getMethodColor = (method: string) => {
    switch (method) {
      case 'GET': return 'blue';
      case 'POST': return 'green';
      case 'PUT': return 'orange';
      case 'DELETE': return 'red';
      default: return 'default';
    }
  };

  const apiColumns = [
    {
      title: '方法',
      dataIndex: 'method',
      key: 'method',
      width: 80,
      render: (method: string) => (
        <Tag color={getMethodColor(method)}>{method}</Tag>
      )
    },
    {
      title: 'API路径',
      dataIndex: 'path',
      key: 'path',
      width: 250,
      render: (path: string) => <Text code>{path}</Text>
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      width: 200
    },
    {
      title: '使用页面',
      dataIndex: 'page',
      key: 'page',
      width: 150
    },
    {
      title: '相关表',
      dataIndex: 'tables',
      key: 'tables',
      width: 200,
      render: (tables: string[]) => (
        <Space wrap>
          {tables.map(table => (
            <Tag key={table} color="blue" style={{ fontSize: '11px' }}>
              {table}
            </Tag>
          ))}
        </Space>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {status === 'active' ? '正常' : status === 'deprecated' ? '已废弃' : '计划中'}
        </Tag>
      )
    }
  ];

  const tableColumns = [
    {
      title: '表名',
      dataIndex: 'name',
      key: 'name',
      width: 250,
      render: (name: string) => <Text code>{name}</Text>
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      render: (type: string) => (
        <Tag color={getTableTypeColor(type)}>
          {type === 'temp' ? '临时表A' : 
           type === 'valid' ? '有效表B' : 
           type === 'analytics' ? '可视化表' : '系统表'}
        </Tag>
      )
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      width: 300
    },
    {
      title: '记录数',
      dataIndex: 'recordCount',
      key: 'recordCount',
      width: 100,
      render: (count: number) => (
        <Badge count={count} style={{ backgroundColor: '#52c41a' }} />
      )
    },
    {
      title: '最后更新',
      dataIndex: 'lastUpdated',
      key: 'lastUpdated',
      width: 150
    },
    {
      title: '相关API',
      dataIndex: 'relatedApis',
      key: 'relatedApis',
      render: (apis: string[]) => (
        <Space wrap>
          {apis.map(api => (
            <Tag key={api} style={{ fontSize: '10px' }}>
              {api}
            </Tag>
          ))}
        </Space>
      )
    }
  ];

  return (
    <AdminLayout>
      <div className={styles.container}>
        <div className={styles.header}>
          <Title level={2}>
            <ApiOutlined /> API与数据库对应关系
          </Title>
          <Paragraph>
            本页面展示了系统中所有API接口与数据库表的对应关系，方便开发调试和问题排查。
          </Paragraph>
        </div>

        <Alert
          message="数据流向说明"
          description="用户提交数据 → 临时表A → 审核 → 有效数据表B → 定时同步 → 可视化副表"
          type="info"
          showIcon
          style={{ marginBottom: 24 }}
        />

        <Tabs defaultActiveKey="api-overview">
          <TabPane tab={<span><ApiOutlined />API概览</span>} key="api-overview">
            <Card>
              <Table
                columns={apiColumns}
                dataSource={apiEndpoints}
                rowKey="id"
                pagination={{ pageSize: 10 }}
                scroll={{ x: 1200 }}
              />
            </Card>
          </TabPane>

          <TabPane tab={<span><DatabaseOutlined />数据库表</span>} key="database-tables">
            <Card>
              <Table
                columns={tableColumns}
                dataSource={databaseTables}
                rowKey="name"
                pagination={{ pageSize: 10 }}
                scroll={{ x: 1200 }}
              />
            </Card>
          </TabPane>

          <TabPane tab={<span><TableOutlined />数据流向</span>} key="data-flow">
            <Card title="3层数据结构设计">
              <Collapse>
                <Panel header="第1层：临时存储表A" key="layer1">
                  <Descriptions column={1}>
                    <Descriptions.Item label="表名">questionnaire_submissions_temp</Descriptions.Item>
                    <Descriptions.Item label="作用">存储用户提交的原始问卷数据</Descriptions.Item>
                    <Descriptions.Item label="数据流向">用户提交 → 临时表A</Descriptions.Item>
                    <Descriptions.Item label="审核状态">pending, approved, rejected, flagged</Descriptions.Item>
                    <Descriptions.Item label="相关API">
                      /api/questionnaire/submit, /api/admin/questionnaire/pending
                    </Descriptions.Item>
                  </Descriptions>
                </Panel>
                
                <Panel header="第2层：有效数据表B" key="layer2">
                  <Descriptions column={1}>
                    <Descriptions.Item label="表名">questionnaire_submissions</Descriptions.Item>
                    <Descriptions.Item label="作用">存储经过审核的有效问卷数据</Descriptions.Item>
                    <Descriptions.Item label="数据流向">临时表A → 审核通过 → 有效数据表B</Descriptions.Item>
                    <Descriptions.Item label="用途">用于统计分析和数据可视化</Descriptions.Item>
                    <Descriptions.Item label="相关API">
                      /api/admin/questionnaire/:id/approve
                    </Descriptions.Item>
                  </Descriptions>
                </Panel>
                
                <Panel header="第3层：可视化副表" key="layer3">
                  <Descriptions column={1}>
                    <Descriptions.Item label="表名">
                      analytics_summary, analytics_demographics, analytics_employment, analytics_skills
                    </Descriptions.Item>
                    <Descriptions.Item label="作用">存储预计算的统计数据</Descriptions.Item>
                    <Descriptions.Item label="数据流向">有效数据表B → 定时同步 → 可视化副表</Descriptions.Item>
                    <Descriptions.Item label="更新频率">每日凌晨2点自动同步</Descriptions.Item>
                    <Descriptions.Item label="相关API">
                      /api/analytics/summary, /api/analytics/demographics, /api/analytics/employment
                    </Descriptions.Item>
                  </Descriptions>
                </Panel>
              </Collapse>
            </Card>
          </TabPane>
        </Tabs>
      </div>
    </AdminLayout>
  );
};
