.content {
  padding: 24px;
  background: #fff;
  min-height: calc(100vh - 64px);
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  background: white;
  padding: 16px 24px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  box-shadow: none;
}

.statsRow {
  margin-bottom: 24px;
}

.tableSection {
  margin-top: 16px;
}

@media (max-width: 768px) {
  .content {
    padding: 16px;
  }
  
  .header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
}
