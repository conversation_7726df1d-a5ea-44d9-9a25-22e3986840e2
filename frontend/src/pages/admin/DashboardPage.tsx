import React, { useEffect, useState } from 'react';
import { Card, Row, Col, Statistic, Table, Tag, Button, Space, Typography, Tabs } from 'antd';
import {
  UserOutlined,
  FileTextOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  EyeOutlined,
  EditOutlined,
  ApiOutlined,
  DatabaseOutlined,
  SettingOutlined,
  Bar<PERSON><PERSON>Outlined,
  ThunderboltOutlined
} from '@ant-design/icons';
import { Link } from 'react-router-dom';
import { AdminLayout } from '../../components/layout/RoleBasedLayout';
import { useQuestionnaireStore } from '../../stores/questionnaireStore';
import { useManagementAuthStore } from '../../stores/managementAuthStore';


const { Title } = Typography;
const { TabPane } = Tabs;

export const DashboardPage: React.FC = () => {
  const { currentUser } = useManagementAuthStore();
  const {
    questionnaires,
    pagination,
    isLoading,
    fetchQuestionnaires,
    updateQuestionnaireStatus
  } = useQuestionnaireStore();

  // 使用管理系统的用户信息
  const finalUser = currentUser;

  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    approved: 0,
    rejected: 0
  });

  useEffect(() => {
    fetchQuestionnaires();
  }, [fetchQuestionnaires]);

  useEffect(() => {
    // 计算统计数据
    const newStats = questionnaires.reduce((acc, q) => {
      acc.total++;
      if (q.status === 'pending') acc.pending++;
      else if (q.status === 'approved') acc.approved++;
      else if (q.status === 'rejected') acc.rejected++;
      return acc;
    }, { total: 0, pending: 0, approved: 0, rejected: 0 });
    
    setStats(newStats);
  }, [questionnaires]);

  const handleStatusUpdate = async (id: string, status: 'approved' | 'rejected') => {
    try {
      await updateQuestionnaireStatus(id, status);
    } catch (error) {
      console.error('更新状态失败:', error);
    }
  };

  const getStatusTag = (status: string) => {
    const statusMap = {
      pending: { color: 'orange', text: '待审核' },
      approved: { color: 'green', text: '已通过' },
      rejected: { color: 'red', text: '已拒绝' }
    };
    const config = statusMap[status as keyof typeof statusMap] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 从新数据结构中提取信息的辅助函数
  const extractFromResponseData = (responseData: any, sectionId: string, questionId: string) => {
    try {
      if (!responseData || !responseData.sectionResponses) return '-';

      const section = responseData.sectionResponses.find((s: any) => s.sectionId === sectionId);
      if (!section || !section.questionResponses) return '-';

      const question = section.questionResponses.find((q: any) => q.questionId === questionId);
      return question?.value || '-';
    } catch {
      return '-';
    }
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 100,
      render: (id: string) => id.substring(0, 8) + '...'
    },
    {
      title: '问卷类型',
      dataIndex: 'questionnaireId',
      key: 'questionnaireId',
      render: (id: string) => {
        const typeMap: Record<string, string> = {
          'employment-survey-v1': '就业调研'
        };
        return typeMap[id] || id;
      }
    },
    {
      title: '姓名',
      key: 'name',
      render: (_, record: any) => {
        // 兼容新旧数据结构
        if (record.responseData) {
          return extractFromResponseData(record.responseData, 'personal', 'name');
        } else if (record.personal_info) {
          try {
            const info = JSON.parse(record.personal_info);
            return info.name || '-';
          } catch {
            return '-';
          }
        }
        return '-';
      }
    },
    {
      title: '学校',
      key: 'university',
      render: (_, record: any) => {
        // 兼容新旧数据结构
        if (record.responseData) {
          return extractFromResponseData(record.responseData, 'education', 'university');
        } else if (record.education_info) {
          try {
            const info = JSON.parse(record.education_info);
            return info.university || '-';
          } catch {
            return '-';
          }
        }
        return '-';
      }
    },
    {
      title: '专业',
      key: 'major',
      render: (_, record: any) => {
        // 兼容新旧数据结构
        if (record.responseData) {
          return extractFromResponseData(record.responseData, 'education', 'major');
        } else if (record.education_info) {
          try {
            const info = JSON.parse(record.education_info);
            return info.major || '-';
          } catch {
            return '-';
          }
        }
        return '-';
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => getStatusTag(status)
    },
    {
      title: '提交者',
      key: 'submitter',
      render: (_, record: any) => {
        // 检查是否为匿名提交
        if (record.metadata) {
          return record.metadata.isAnonymous ? '匿名用户' : (record.userId || '匿名用户');
        } else {
          return record.user_id ? record.user_id : '匿名用户';
        }
      }
    },
    {
      title: '提交时间',
      key: 'submittedAt',
      render: (_, record: any) => {
        // 优先使用新数据结构的时间
        if (record.metadata && record.metadata.submittedAt) {
          return new Date(record.metadata.submittedAt).toLocaleString();
        } else if (record.created_at) {
          return new Date(record.created_at).toLocaleString();
        } else if (record.createdAt) {
          return new Date(record.createdAt).toLocaleString();
        }
        return '-';
      }
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record: any) => (
        <Space>
          <Button 
            type="link" 
            icon={<EyeOutlined />} 
            size="small"
          >
            查看
          </Button>
          {record.status === 'pending' && (
            <>
              <Button 
                type="link" 
                size="small"
                onClick={() => handleStatusUpdate(record.id, 'approved')}
              >
                通过
              </Button>
              <Button 
                type="link" 
                danger 
                size="small"
                onClick={() => handleStatusUpdate(record.id, 'rejected')}
              >
                拒绝
              </Button>
            </>
          )}
        </Space>
      )
    }
  ];

  return (
    <AdminLayout>
      <div style={{ padding: '24px', background: '#fff7e6', minHeight: 'calc(100vh - 64px)' }}>
        {/* 页面标题 */}
        <div style={{ marginBottom: '24px', borderBottom: '1px solid #e8e8e8', paddingBottom: '16px' }}>
          <Title level={2} style={{ margin: 0, color: '#333' }}>
            管理仪表板
          </Title>
          <div style={{ fontSize: '14px', color: '#666', marginTop: '4px' }}>
            欢迎，{finalUser?.display_name || finalUser?.username} ({finalUser?.userType})
          </div>
        </div>
        {/* 统计卡片 */}
        <Row gutter={[16, 16]} className="mb-8">
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="总问卷数"
                value={stats.total}
                prefix={<FileTextOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="待审核"
                value={stats.pending}
                prefix={<ClockCircleOutlined />}
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="已通过"
                value={stats.approved}
                prefix={<CheckCircleOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="已拒绝"
                value={stats.rejected}
                prefix={<UserOutlined />}
                valueStyle={{ color: '#f5222d' }}
              />
            </Card>
          </Col>
        </Row>

        {/* 快捷导航 */}
        <Card title="管理功能" style={{ marginBottom: '24px' }}>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} md={6}>
              <Link to="/admin/users">
                <Card hoverable style={{ textAlign: 'center' }}>
                  <UserOutlined style={{ fontSize: '24px', color: '#1890ff', marginBottom: '8px' }} />
                  <div>用户管理</div>
                </Card>
              </Link>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Link to="/admin/content">
                <Card hoverable style={{ textAlign: 'center' }}>
                  <EditOutlined style={{ fontSize: '24px', color: '#52c41a', marginBottom: '8px' }} />
                  <div>内容管理</div>
                </Card>
              </Link>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Link to="/admin/api-data">
                <Card hoverable style={{ textAlign: 'center' }}>
                  <ApiOutlined style={{ fontSize: '24px', color: '#722ed1', marginBottom: '8px' }} />
                  <div>API与数据</div>
                </Card>
              </Link>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Link to="/analytics">
                <Card hoverable style={{ textAlign: 'center' }}>
                  <BarChartOutlined style={{ fontSize: '24px', color: '#fa8c16', marginBottom: '8px' }} />
                  <div>数据可视化</div>
                </Card>
              </Link>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Link to="/admin/data-generator">
                <Card hoverable style={{ textAlign: 'center' }}>
                  <ThunderboltOutlined style={{ fontSize: '24px', color: '#13c2c2', marginBottom: '8px' }} />
                  <div>数据生成器</div>
                </Card>
              </Link>
            </Col>
          </Row>
        </Card>

        {/* 问卷列表 */}
        <Card title="问卷管理" className="mb-8">
          <Tabs defaultActiveKey="all">
            <TabPane tab="全部问卷" key="all">
              <Table
                columns={columns}
                dataSource={questionnaires}
                rowKey="id"
                loading={isLoading}
                pagination={{
                  current: pagination.page,
                  pageSize: pagination.pageSize,
                  total: pagination.total,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total, range) => 
                    `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                }}
              />
            </TabPane>
            <TabPane tab={`待审核 (${stats.pending})`} key="pending">
              <Table
                columns={columns}
                dataSource={questionnaires.filter(q => q.status === 'pending')}
                rowKey="id"
                loading={isLoading}
                pagination={false}
              />
            </TabPane>
          </Tabs>
        </Card>

        {/* 快速操作 */}
        <Card title="快速操作">
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} md={8}>
              <Card size="small" hoverable>
                <div className="text-center">
                  <FileTextOutlined className="text-2xl text-blue-600 mb-2" />
                  <div className="font-medium">导出数据</div>
                  <div className="text-sm text-gray-500">导出问卷数据为Excel</div>
                </div>
              </Card>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Card size="small" hoverable>
                <div className="text-center">
                  <UserOutlined className="text-2xl text-green-600 mb-2" />
                  <div className="font-medium">用户管理</div>
                  <div className="text-sm text-gray-500">管理系统用户</div>
                </div>
              </Card>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Card size="small" hoverable>
                <div className="text-center">
                  <EditOutlined className="text-2xl text-purple-600 mb-2" />
                  <div className="font-medium">系统设置</div>
                  <div className="text-sm text-gray-500">配置系统参数</div>
                </div>
              </Card>
            </Col>
          </Row>
        </Card>
      </div>
    </AdminLayout>
  );
};
