.container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
  background: #fff;
  min-height: calc(100vh - 64px);
}

.header {
  margin-bottom: 24px;
  text-align: center;
}

.header h2 {
  color: #333 !important;
  margin-bottom: 8px !important;
}

.header p {
  color: #666 !important;
  font-size: 14px !important;
  margin-bottom: 0 !important;
}

/* 表格样式 */
.container .ant-table {
  background: #fff !important;
}

.container .ant-table-thead > tr > th {
  background: #fafafa !important;
  color: #333 !important;
  font-weight: 500 !important;
  border-bottom: 1px solid #e8e8e8 !important;
}

.container .ant-table-tbody > tr > td {
  background: #fff !important;
  color: #333 !important;
  border-bottom: 1px solid #f0f0f0 !important;
}

.container .ant-table-tbody > tr:hover > td {
  background: #f5f5f5 !important;
}

.failedRow {
  background: #fff2f0 !important;
}

.failedRow:hover {
  background: #ffece8 !important;
}

/* 卡片样式 */
.container .ant-card {
  border-radius: 8px !important;
  border: 1px solid #e8e8e8 !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02) !important;
}

.container .ant-card-head {
  background: #fafafa !important;
  border-bottom: 1px solid #e8e8e8 !important;
}

.container .ant-card-head-title {
  color: #333 !important;
  font-weight: 500 !important;
}

/* 标签样式 */
.container .ant-tag {
  border-radius: 4px !important;
  font-size: 12px !important;
  line-height: 1.2 !important;
  padding: 2px 6px !important;
  border: none !important;
}

/* 按钮样式 */
.container .ant-btn {
  border-radius: 4px !important;
  font-size: 14px !important;
  height: 32px !important;
  padding: 0 16px !important;
}

.container .ant-btn-primary {
  background: #1890ff !important;
  border-color: #1890ff !important;
}

.container .ant-btn-primary:hover {
  background: #40a9ff !important;
  border-color: #40a9ff !important;
}

/* 警告框样式 */
.container .ant-alert {
  border-radius: 6px !important;
  margin-bottom: 8px !important;
}

.container .ant-alert-success {
  background: #f6ffed !important;
  border: 1px solid #b7eb8f !important;
}

.container .ant-alert-error {
  background: #fff2f0 !important;
  border: 1px solid #ffccc7 !important;
}

.container .ant-alert-warning {
  background: #fffbe6 !important;
  border: 1px solid #ffe58f !important;
}

/* 分割线样式 */
.container .ant-divider {
  margin: 16px 0 !important;
}

.container .ant-divider-horizontal.ant-divider-with-text-left {
  color: #333 !important;
  font-weight: 500 !important;
}

/* 代码样式 */
.container .ant-typography code {
  background: #f5f5f5 !important;
  border: 1px solid #e8e8e8 !important;
  border-radius: 3px !important;
  padding: 2px 4px !important;
  font-size: 12px !important;
  color: #333 !important;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .container {
    padding: 16px;
    max-width: none;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 12px;
  }
  
  .header h2 {
    font-size: 20px !important;
  }
  
  .header p {
    font-size: 13px !important;
  }
  
  .container .ant-table {
    font-size: 12px !important;
  }
  
  .container .ant-btn {
    height: 28px !important;
    padding: 0 12px !important;
    font-size: 12px !important;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 8px;
  }
  
  .header h2 {
    font-size: 18px !important;
  }
  
  .header p {
    font-size: 12px !important;
  }
  
  .container .ant-table {
    font-size: 11px !important;
  }
  
  .container .ant-btn {
    height: 24px !important;
    padding: 0 8px !important;
    font-size: 11px !important;
  }
  
  .container .ant-tag {
    font-size: 10px !important;
    padding: 1px 4px !important;
  }
}
