.container {
  padding: 24px;
  background: #f6f8ff;
  min-height: calc(100vh - 64px);
}

.header {
  margin-bottom: 24px;
  padding: 24px;
  background: #fff;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
}

.header h2 {
  margin-bottom: 8px !important;
  color: #1890ff !important;
  font-weight: 600 !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
}

.header p {
  margin-bottom: 0 !important;
  color: #666 !important;
  font-size: 14px !important;
}

/* 卡片样式 */
.container .ant-card {
  border-radius: 8px !important;
  border: 1px solid #e8e8e8 !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02) !important;
  transition: all 0.3s ease !important;
}

.container .ant-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08) !important;
}

.container .ant-card-head {
  background: #fafafa !important;
  border-bottom: 1px solid #e8e8e8 !important;
}

.container .ant-card-head-title {
  color: #333 !important;
  font-weight: 500 !important;
}

/* 统计数字样式 */
.container .ant-statistic-title {
  color: #666 !important;
  font-size: 14px !important;
  margin-bottom: 4px !important;
}

.container .ant-statistic-content {
  font-size: 24px !important;
  font-weight: 600 !important;
}

.container .ant-statistic-content-prefix {
  margin-right: 8px !important;
}

/* 表格样式 */
.container .ant-table {
  background: #fff !important;
}

.container .ant-table-thead > tr > th {
  background: #fafafa !important;
  color: #333 !important;
  font-weight: 500 !important;
  border-bottom: 1px solid #e8e8e8 !important;
}

.container .ant-table-tbody > tr > td {
  background: #fff !important;
  color: #333 !important;
  border-bottom: 1px solid #f0f0f0 !important;
}

.container .ant-table-tbody > tr:hover > td {
  background: #f5f5f5 !important;
}

/* 标签样式 */
.container .ant-tag {
  border-radius: 4px !important;
  font-size: 12px !important;
  line-height: 1.2 !important;
  padding: 2px 6px !important;
  border: none !important;
}

/* 按钮样式 */
.container .ant-btn {
  border-radius: 4px !important;
}

.container .ant-btn-link {
  color: #1890ff !important;
  padding: 0 !important;
  height: auto !important;
  font-size: 12px !important;
}

.container .ant-btn-link:hover {
  color: #40a9ff !important;
}

.container .ant-btn-primary {
  background: #1890ff !important;
  border-color: #1890ff !important;
}

.container .ant-btn-primary:hover {
  background: #40a9ff !important;
  border-color: #40a9ff !important;
}

/* 模态框样式 */
.container .ant-modal-header {
  background: #fafafa !important;
  border-bottom: 1px solid #e8e8e8 !important;
}

.container .ant-modal-title {
  color: #333 !important;
  font-weight: 500 !important;
}

/* 描述列表样式 */
.container .ant-descriptions-item-label {
  color: #666 !important;
  font-weight: 500 !important;
}

.container .ant-descriptions-item-content {
  color: #333 !important;
}

/* 表单样式 */
.container .ant-form-item-label > label {
  color: #333 !important;
  font-weight: 500 !important;
}

.container .ant-input,
.container .ant-input-number,
.container .ant-select-selector {
  border-radius: 4px !important;
  border: 1px solid #d9d9d9 !important;
}

.container .ant-input:focus,
.container .ant-input-number:focus,
.container .ant-select-focused .ant-select-selector {
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

.container .ant-radio-wrapper {
  color: #333 !important;
}

.container .ant-radio-checked .ant-radio-inner {
  border-color: #1890ff !important;
  background-color: #1890ff !important;
}

/* 内容展示样式 */
.contentPreview {
  max-height: 200px;
  overflow-y: auto;
  padding: 12px;
  background: #fafafa;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
  font-size: 14px;
  line-height: 1.6;
  color: #333;
}

.contentPreview::-webkit-scrollbar {
  width: 6px;
}

.contentPreview::-webkit-scrollbar-track {
  background: #f0f0f0;
  border-radius: 3px;
}

.contentPreview::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 3px;
}

.contentPreview::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .container {
    padding: 16px;
  }
  
  .header {
    padding: 16px;
    margin-bottom: 16px;
  }
  
  .header h2 {
    font-size: 20px !important;
  }
  
  .header p {
    font-size: 13px !important;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 12px;
  }
  
  .header {
    padding: 12px;
    margin-bottom: 12px;
  }
  
  .header h2 {
    font-size: 18px !important;
  }
  
  .header p {
    font-size: 12px !important;
  }
  
  .container .ant-statistic-content {
    font-size: 20px !important;
  }
  
  .container .ant-table {
    font-size: 12px !important;
  }
  
  .contentPreview {
    max-height: 150px;
    padding: 8px;
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 8px;
  }
  
  .header {
    padding: 8px;
    margin-bottom: 8px;
  }
  
  .header h2 {
    font-size: 16px !important;
    flex-direction: column !important;
    gap: 4px !important;
  }
  
  .header p {
    font-size: 11px !important;
  }
  
  .container .ant-statistic-content {
    font-size: 18px !important;
  }
  
  .container .ant-statistic-title {
    font-size: 12px !important;
  }
  
  .container .ant-table {
    font-size: 11px !important;
  }
  
  .contentPreview {
    max-height: 120px;
    padding: 6px;
    font-size: 11px;
  }
}
