import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Statistic, Table, Tag, Button, Space } from 'antd';
import {
  FileTextOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  EyeOutlined,
  BookOutlined,
  MessageOutlined,
  ThunderboltOutlined
} from '@ant-design/icons';
import { Link } from 'react-router-dom';
import { ReviewerLayout } from '../../components/layout/RoleBasedLayout';
import { useManagementAuthStore } from '../../stores/managementAuthStore';
import styles from './ReviewerDashboard.module.css';



export const ReviewerDashboard: React.FC = () => {
  const { currentUser } = useManagementAuthStore();
  const [loading, setLoading] = useState(false);

  // 模拟数据
  const [stats] = useState({
    pendingQuestionnaires: 15,
    pendingStories: 8,
    pendingVoices: 12,
    todayReviewed: 23,
    totalReviewed: 156,
    averageReviewTime: 3.5
  });

  const [recentItems] = useState([
    {
      id: '1',
      type: 'questionnaire',
      title: '计算机专业毕业生就业调查',
      submittedAt: '2024-01-27 14:30:00',
      status: 'pending'
    },
    {
      id: '2',
      type: 'story',
      title: '从实习生到正式员工的心路历程',
      submittedAt: '2024-01-27 13:45:00',
      status: 'pending'
    },
    {
      id: '3',
      type: 'voice',
      title: '希望能找到心仪的工作...',
      submittedAt: '2024-01-27 12:20:00',
      status: 'pending'
    },
    {
      id: '4',
      type: 'questionnaire',
      title: '市场营销专业就业现状调查',
      submittedAt: '2024-01-27 11:15:00',
      status: 'reviewed'
    },
    {
      id: '5',
      type: 'story',
      title: '转行程序员的经历分享',
      submittedAt: '2024-01-27 10:30:00',
      status: 'reviewed'
    }
  ]);

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'questionnaire': return <FileTextOutlined />;
      case 'story': return <BookOutlined />;
      case 'voice': return <MessageOutlined />;
      default: return <FileTextOutlined />;
    }
  };

  const getTypeName = (type: string) => {
    switch (type) {
      case 'questionnaire': return '问卷';
      case 'story': return '故事';
      case 'voice': return '心声';
      default: return '未知';
    }
  };

  const getStatusTag = (status: string) => {
    switch (status) {
      case 'pending':
        return <Tag color="orange" icon={<ClockCircleOutlined />}>待审核</Tag>;
      case 'reviewed':
        return <Tag color="green" icon={<CheckCircleOutlined />}>已审核</Tag>;
      default:
        return <Tag>未知</Tag>;
    }
  };

  const columns = [
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 80,
      render: (type: string) => (
        <Space>
          {getTypeIcon(type)}
          {getTypeName(type)}
        </Space>
      )
    },
    {
      title: '标题',
      dataIndex: 'title',
      key: 'title',
      ellipsis: true
    },
    {
      title: '提交时间',
      dataIndex: 'submittedAt',
      key: 'submittedAt',
      width: 150
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => getStatusTag(status)
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (_, record: any) => (
        <Button
          type="link"
          icon={<EyeOutlined />}
          size="small"
          disabled={record.status === 'reviewed'}
        >
          审核
        </Button>
      )
    }
  ];

  return (
    <ReviewerLayout>
      <div className={styles.container}>


        {/* 统计卡片 */}
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col xs={24} sm={12} md={8}>
            <Card>
              <Statistic
                title="待审核问卷"
                value={stats.pendingQuestionnaires}
                prefix={<FileTextOutlined />}
                valueStyle={{ color: '#fa8c16' }}
              />
              <div style={{ marginTop: 8 }}>
                <Link to="/reviewer/questionnaires">
                  <Button type="link" size="small">立即审核</Button>
                </Link>
              </div>
            </Card>
          </Col>
          <Col xs={24} sm={12} md={8}>
            <Card>
              <Statistic
                title="待审核故事"
                value={stats.pendingStories}
                prefix={<BookOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
              <div style={{ marginTop: 8 }}>
                <Link to="/reviewer/stories">
                  <Button type="link" size="small">立即审核</Button>
                </Link>
              </div>
            </Card>
          </Col>
          <Col xs={24} sm={12} md={8}>
            <Card>
              <Statistic
                title="待审核心声"
                value={stats.pendingVoices}
                prefix={<MessageOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
              <div style={{ marginTop: 8 }}>
                <Link to="/reviewer/voices">
                  <Button type="link" size="small">立即审核</Button>
                </Link>
              </div>
            </Card>
          </Col>
        </Row>

        {/* 快速审核区域 */}
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col span={24}>
            <Card
              title={
                <span>
                  <ThunderboltOutlined style={{ color: '#1890ff', marginRight: 8 }} />
                  快速审核模式
                </span>
              }
              style={{
                background: 'linear-gradient(135deg, #f0f8ff 0%, #e6f7ff 100%)',
                border: '1px solid #d9f7be'
              }}
            >
              <Row gutter={[16, 16]} align="middle">
                <Col xs={24} md={12}>
                  <div>
                    <h4 style={{ margin: '0 0 8px 0', color: '#1890ff' }}>
                      高效审核工具
                    </h4>
                    <p style={{ margin: 0, color: '#666' }}>
                      批次处理 + 键盘快捷键，审核效率提升3-5倍
                    </p>
                  </div>
                </Col>
                <Col xs={24} md={12}>
                  <Space size="middle" style={{ width: '100%', justifyContent: 'flex-end' }}>
                    <Link to="/reviewer/quick-review/voice">
                      <Button
                        type="primary"
                        icon={<MessageOutlined />}
                        style={{
                          background: 'linear-gradient(135deg, #52c41a, #73d13d)',
                          border: 'none'
                        }}
                      >
                        快速审核心声
                      </Button>
                    </Link>
                    <Link to="/reviewer/quick-review/story">
                      <Button
                        type="primary"
                        icon={<BookOutlined />}
                        style={{
                          background: 'linear-gradient(135deg, #1890ff, #40a9ff)',
                          border: 'none'
                        }}
                      >
                        快速审核故事
                      </Button>
                    </Link>
                  </Space>
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>

        {/* 今日统计 */}
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col xs={24} sm={8}>
            <Card>
              <Statistic
                title="今日已审核"
                value={stats.todayReviewed}
                prefix={<CheckCircleOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={8}>
            <Card>
              <Statistic
                title="累计审核"
                value={stats.totalReviewed}
                prefix={<CheckCircleOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={8}>
            <Card>
              <Statistic
                title="平均审核时间"
                value={stats.averageReviewTime}
                suffix="分钟"
                precision={1}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
        </Row>

        {/* 最近提交的内容 */}
        <Card title="最近提交的内容" extra={
          <Link to="/reviewer/history">
            <Button type="link">查看全部</Button>
          </Link>
        }>
          <Table
            columns={columns}
            dataSource={recentItems}
            rowKey="id"
            pagination={false}
            size="small"
          />
        </Card>
      </div>
    </ReviewerLayout>
  );
};
