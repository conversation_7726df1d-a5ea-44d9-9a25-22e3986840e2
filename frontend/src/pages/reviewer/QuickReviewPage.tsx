/**
 * 快速审核页面
 * 支持问卷心声和故事墙的快速审核
 */

import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Row,
  Col,
  Button,
  Typography,
  Space,
  Statistic,
  Tag,
  Alert,
  Modal
} from 'antd';
import {
  ThunderboltOutlined,
  FileTextOutlined,
  BookOutlined,
  MessageOutlined,
  ArrowLeftOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';

import { ReviewerLayout } from '../../components/layout/RoleBasedLayout';
import { QuickReviewPanel } from '../../components/quickReview/QuickReviewPanel';
import type { QuickReviewContentType } from '../../types/quickReview.types';
import { useManagementAuthStore } from '../../stores/managementAuthStore';
import styles from './QuickReviewPage.module.css';

const { Title, Text, Paragraph } = Typography;

// 内容类型配置
const CONTENT_TYPE_CONFIG = {
  voice: {
    label: '问卷心声',
    icon: <MessageOutlined />,
    color: '#52c41a',
    description: '审核用户在问卷中提交的心声和反馈内容'
  },
  story: {
    label: '故事墙',
    icon: <BookOutlined />,
    color: '#1890ff',
    description: '审核用户分享的就业故事和经验'
  }
};

export const QuickReviewPage: React.FC = () => {
  const { contentType } = useParams<{ contentType: string }>();
  const navigate = useNavigate();
  const { currentUser } = useManagementAuthStore();
  
  const [showExitConfirm, setShowExitConfirm] = useState(false);
  const [isReviewMode, setIsReviewMode] = useState(false);

  // 验证内容类型
  const validContentType = contentType as QuickReviewContentType;
  const isValidType = validContentType && ['voice', 'story'].includes(validContentType);

  if (!isValidType) {
    return (
      <ReviewerLayout>
        <div className={styles.container}>
          <Alert
            message="无效的内容类型"
            description="请选择有效的审核内容类型"
            type="error"
            showIcon
            action={
              <Button onClick={() => navigate('/reviewer')}>
                返回工作台
              </Button>
            }
          />
        </div>
      </ReviewerLayout>
    );
  }

  const config = CONTENT_TYPE_CONFIG[validContentType];

  // 处理退出
  const handleExit = () => {
    if (isReviewMode) {
      setShowExitConfirm(true);
    } else {
      navigate('/reviewer');
    }
  };

  const handleExitConfirm = () => {
    setShowExitConfirm(false);
    navigate('/reviewer');
  };

  // 开始快速审核
  const handleStartReview = () => {
    setIsReviewMode(true);
  };

  // 如果已经在审核模式，显示审核面板
  if (isReviewMode) {
    return (
      <ReviewerLayout>
        <QuickReviewPanel
          contentType={validContentType}
          onExit={handleExit}
        />
        
        {/* 退出确认对话框 */}
        <Modal
          title="确认退出"
          open={showExitConfirm}
          onOk={handleExitConfirm}
          onCancel={() => setShowExitConfirm(false)}
          okText="确认退出"
          cancelText="继续审核"
          icon={<ExclamationCircleOutlined />}
        >
          <p>您确定要退出快速审核模式吗？</p>
          <p>当前的审核进度将会保存，您可以稍后继续。</p>
        </Modal>
      </ReviewerLayout>
    );
  }

  // 显示快速审核介绍页面
  return (
    <ReviewerLayout>
      <div className={styles.container}>
        {/* 返回按钮 */}
        <div className={styles.backButton}>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/reviewer')}
          >
            返回工作台
          </Button>
        </div>

        {/* 页面标题 */}
        <div className={styles.header}>
          <div className={styles.titleSection}>
            <Title level={1} className={styles.title}>
              <ThunderboltOutlined style={{ color: config.color, marginRight: 12 }} />
              快速审核 - {config.label}
            </Title>
            <Paragraph className={styles.description}>
              {config.description}
            </Paragraph>
          </div>
        </div>

        {/* 功能介绍 */}
        <Row gutter={[24, 24]} style={{ marginBottom: 32 }}>
          <Col span={24}>
            <Card title="快速审核功能特色" className={styles.featureCard}>
              <Row gutter={[16, 16]}>
                <Col xs={24} sm={12} md={6}>
                  <div className={styles.feature}>
                    <ThunderboltOutlined style={{ fontSize: 32, color: '#1890ff', marginBottom: 8 }} />
                    <Title level={4}>批次处理</Title>
                    <Text type="secondary">一次处理50条内容，大幅提升审核效率</Text>
                  </div>
                </Col>
                <Col xs={24} sm={12} md={6}>
                  <div className={styles.feature}>
                    <FileTextOutlined style={{ fontSize: 32, color: '#52c41a', marginBottom: 8 }} />
                    <Title level={4}>键盘快捷键</Title>
                    <Text type="secondary">全键盘操作，A批准/R拒绝/S跳过</Text>
                  </div>
                </Col>
                <Col xs={24} sm={12} md={6}>
                  <div className={styles.feature}>
                    <BookOutlined style={{ fontSize: 32, color: '#fa8c16', marginBottom: 8 }} />
                    <Title level={4}>实时统计</Title>
                    <Text type="secondary">审核进度和效率实时监控</Text>
                  </div>
                </Col>
                <Col xs={24} sm={12} md={6}>
                  <div className={styles.feature}>
                    <MessageOutlined style={{ fontSize: 32, color: '#722ed1', marginBottom: 8 }} />
                    <Title level={4}>智能辅助</Title>
                    <Text type="secondary">AI预评分和风险等级提示</Text>
                  </div>
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>

        {/* 审核员信息和统计 */}
        <Row gutter={[24, 24]} style={{ marginBottom: 32 }}>
          <Col xs={24} md={12}>
            <Card title="审核员信息" className={styles.infoCard}>
              <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                <div className={styles.reviewerInfo}>
                  <Text strong>审核员：</Text>
                  <Text>{currentUser?.display_name || currentUser?.username}</Text>
                </div>
                <div className={styles.reviewerInfo}>
                  <Text strong>权限级别：</Text>
                  <Tag color="blue">{currentUser?.userType}</Tag>
                </div>
                <div className={styles.reviewerInfo}>
                  <Text strong>审核类型：</Text>
                  <Tag color={config.color} icon={config.icon}>
                    {config.label}
                  </Tag>
                </div>
              </Space>
            </Card>
          </Col>
          
          <Col xs={24} md={12}>
            <Card title="今日统计" className={styles.statsCard}>
              <Row gutter={16}>
                <Col span={8}>
                  <Statistic
                    title="已审核"
                    value={0}
                    suffix="条"
                  />
                </Col>
                <Col span={8}>
                  <Statistic
                    title="批准率"
                    value={0}
                    suffix="%"
                  />
                </Col>
                <Col span={8}>
                  <Statistic
                    title="平均用时"
                    value={0}
                    suffix="秒"
                  />
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>

        {/* 快捷键提示 */}
        <Row gutter={[24, 24]} style={{ marginBottom: 32 }}>
          <Col span={24}>
            <Card title="键盘快捷键" className={styles.shortcutCard}>
              <Row gutter={[16, 16]}>
                <Col xs={12} sm={8} md={4}>
                  <div className={styles.shortcut}>
                    <Tag className={styles.shortcutKey}>A</Tag>
                    <Text>批准</Text>
                  </div>
                </Col>
                <Col xs={12} sm={8} md={4}>
                  <div className={styles.shortcut}>
                    <Tag className={styles.shortcutKey}>R</Tag>
                    <Text>拒绝</Text>
                  </div>
                </Col>
                <Col xs={12} sm={8} md={4}>
                  <div className={styles.shortcut}>
                    <Tag className={styles.shortcutKey}>S</Tag>
                    <Text>跳过</Text>
                  </div>
                </Col>
                <Col xs={12} sm={8} md={4}>
                  <div className={styles.shortcut}>
                    <Tag className={styles.shortcutKey}>N</Tag>
                    <Text>下一个</Text>
                  </div>
                </Col>
                <Col xs={12} sm={8} md={4}>
                  <div className={styles.shortcut}>
                    <Tag className={styles.shortcutKey}>P</Tag>
                    <Text>上一个</Text>
                  </div>
                </Col>
                <Col xs={12} sm={8} md={4}>
                  <div className={styles.shortcut}>
                    <Tag className={styles.shortcutKey}>Z</Tag>
                    <Text>撤销</Text>
                  </div>
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>

        {/* 开始审核按钮 */}
        <div className={styles.startSection}>
          <Button
            type="primary"
            size="large"
            icon={<ThunderboltOutlined />}
            onClick={handleStartReview}
            className={styles.startButton}
          >
            开始快速审核
          </Button>
          <div className={styles.startTip}>
            <Text type="secondary">
              点击开始后将进入专注的快速审核模式，建议在安静的环境中进行
            </Text>
          </div>
        </div>
      </div>
    </ReviewerLayout>
  );
};
