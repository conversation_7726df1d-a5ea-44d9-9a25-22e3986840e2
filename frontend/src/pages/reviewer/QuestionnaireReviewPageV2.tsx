import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  Space, 
  Tag, 
  Modal, 
  Form, 
  Input, 
  Radio, 
  Typography, 
  Descriptions,
  Row,
  Col,
  Statistic,
  message,
  Spin,
  Alert
} from 'antd';
import { 
  EyeOutlined, 
  CheckCircleOutlined, 
  CloseCircleOutlined,
  FileTextOutlined,
  ClockCircleOutlined,
  UserOutlined
} from '@ant-design/icons';
import { ReviewerLayout } from '../../components/layout/RoleBasedLayout';
import { useAuthStore } from '../../stores/authStore';
import { QuestionnaireService } from '../../services/questionnaire';
import type { QuestionnaireResponse } from '../../types/questionnaire-engine';
import styles from './ReviewerPages.module.css';

const { Title, Paragraph, Text } = Typography;
const { TextArea } = Input;

interface QuestionnaireSubmissionV2 {
  id: string;
  questionnaireId: string;
  userId?: string;
  responseData: QuestionnaireResponse;
  metadata: any;
  status: 'pending' | 'approved' | 'rejected';
  reviewerId?: string;
  reviewComment?: string;
  reviewedAt?: string;
  createdAt: string;
  updatedAt: string;
}

export const QuestionnaireReviewPageV2: React.FC = () => {
  const { user } = useAuthStore();
  const [loading, setLoading] = useState(false);
  const [submissions, setSubmissions] = useState<QuestionnaireSubmissionV2[]>([]);
  const [selectedSubmission, setSelectedSubmission] = useState<QuestionnaireSubmissionV2 | null>(null);
  const [reviewModalVisible, setReviewModalVisible] = useState(false);
  const [reviewForm] = Form.useForm();
  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    approved: 0,
    rejected: 0
  });

  // 加载问卷数据
  useEffect(() => {
    fetchSubmissions();
  }, []);

  const fetchSubmissions = async () => {
    setLoading(true);
    try {
      const response = await QuestionnaireService.getQuestionnaires({
        page: 1,
        pageSize: 100
      });
      
      if (response.success && response.data) {
        setSubmissions(response.data);
        
        // 计算统计数据
        const newStats = response.data.reduce((acc, q) => {
          acc.total++;
          acc[q.status]++;
          return acc;
        }, { total: 0, pending: 0, approved: 0, rejected: 0 });
        
        setStats(newStats);
      }
    } catch (error) {
      console.error('获取问卷数据失败:', error);
      message.error('获取问卷数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 从响应数据中提取信息
  const extractFromResponseData = (responseData: QuestionnaireResponse, sectionId: string, questionId: string) => {
    try {
      const section = responseData.sectionResponses.find(s => s.sectionId === sectionId);
      if (!section) return '-';
      
      const question = section.questionResponses.find(q => q.questionId === questionId);
      return question?.value || '-';
    } catch {
      return '-';
    }
  };

  // 处理审核
  const handleReview = (record: QuestionnaireSubmissionV2) => {
    setSelectedSubmission(record);
    setReviewModalVisible(true);
    reviewForm.resetFields();
  };

  // 提交审核结果
  const handleReviewSubmit = async () => {
    if (!selectedSubmission) return;
    
    try {
      const values = await reviewForm.validateFields();
      
      await QuestionnaireService.updateQuestionnaireStatus(
        selectedSubmission.id,
        values.status,
        values.comment
      );
      
      message.success('审核完成');
      setReviewModalVisible(false);
      setSelectedSubmission(null);
      fetchSubmissions(); // 重新加载数据
    } catch (error) {
      console.error('审核失败:', error);
      message.error('审核失败，请重试');
    }
  };

  // 获取状态标签
  const getStatusTag = (status: string) => {
    const statusMap = {
      pending: { color: 'orange', text: '待审核' },
      approved: { color: 'green', text: '已通过' },
      rejected: { color: 'red', text: '已拒绝' }
    };
    const config = statusMap[status as keyof typeof statusMap] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 表格列定义
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 100,
      render: (id: string) => id.substring(0, 8) + '...'
    },
    {
      title: '问卷类型',
      dataIndex: 'questionnaireId',
      key: 'questionnaireId',
      render: (id: string) => {
        const typeMap: Record<string, string> = {
          'employment-survey-v1': '就业调研'
        };
        return typeMap[id] || id;
      }
    },
    {
      title: '提交者',
      key: 'submitter',
      render: (_, record: QuestionnaireSubmissionV2) => {
        return record.metadata.isAnonymous ? '匿名用户' : (record.userId || '匿名用户');
      }
    },
    {
      title: '姓名',
      key: 'name',
      render: (_, record: QuestionnaireSubmissionV2) => {
        return extractFromResponseData(record.responseData, 'personal', 'name');
      }
    },
    {
      title: '专业',
      key: 'major',
      render: (_, record: QuestionnaireSubmissionV2) => {
        return extractFromResponseData(record.responseData, 'education', 'major');
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => getStatusTag(status)
    },
    {
      title: '提交时间',
      key: 'submittedAt',
      render: (_, record: QuestionnaireSubmissionV2) => {
        const time = record.metadata.submittedAt || record.createdAt;
        return new Date(time).toLocaleString();
      }
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record: QuestionnaireSubmissionV2) => (
        <Space>
          <Button 
            type="link" 
            icon={<EyeOutlined />} 
            size="small"
            onClick={() => handleReview(record)}
          >
            查看详情
          </Button>
          {record.status === 'pending' && (
            <>
              <Button 
                type="link" 
                size="small"
                style={{ color: '#52c41a' }}
                onClick={() => {
                  setSelectedSubmission(record);
                  reviewForm.setFieldsValue({ status: 'approved' });
                  handleReviewSubmit();
                }}
              >
                快速通过
              </Button>
              <Button 
                type="link" 
                size="small"
                style={{ color: '#ff4d4f' }}
                onClick={() => {
                  setSelectedSubmission(record);
                  setReviewModalVisible(true);
                  reviewForm.setFieldsValue({ status: 'rejected' });
                }}
              >
                拒绝
              </Button>
            </>
          )}
        </Space>
      )
    }
  ];

  // 渲染问卷详情
  const renderQuestionnaireDetails = (responseData: QuestionnaireResponse) => {
    return responseData.sectionResponses.map((section) => (
      <Card key={section.sectionId} title={section.sectionTitle} style={{ marginBottom: 16 }}>
        <Descriptions column={2} size="small">
          {section.questionResponses.map((response) => (
            <Descriptions.Item 
              key={response.questionId} 
              label={response.questionTitle || response.questionId}
            >
              {formatResponseValue(response.value, response.questionType)}
            </Descriptions.Item>
          ))}
        </Descriptions>
      </Card>
    ));
  };

  // 格式化响应值
  const formatResponseValue = (value: any, questionType?: string) => {
    if (value === null || value === undefined) return '-';
    
    if (Array.isArray(value)) {
      return value.join(', ');
    }
    
    if (typeof value === 'object') {
      return JSON.stringify(value);
    }
    
    return String(value);
  };

  return (
    <ReviewerLayout>
      <div className={styles.container}>
        <Title level={2}>
          <FileTextOutlined /> 问卷审核管理
        </Title>
        
        {/* 统计卡片 */}
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={6}>
            <Card>
              <Statistic
                title="总提交数"
                value={stats.total}
                prefix={<FileTextOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="待审核"
                value={stats.pending}
                prefix={<ClockCircleOutlined />}
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="已通过"
                value={stats.approved}
                prefix={<CheckCircleOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="已拒绝"
                value={stats.rejected}
                prefix={<CloseCircleOutlined />}
                valueStyle={{ color: '#ff4d4f' }}
              />
            </Card>
          </Col>
        </Row>

        {/* 问卷列表 */}
        <Card title="问卷列表">
          <Table
            columns={columns}
            dataSource={submissions}
            rowKey="id"
            loading={loading}
            pagination={{
              total: submissions.length,
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 条记录`
            }}
          />
        </Card>

        {/* 审核详情模态框 */}
        <Modal
          title="问卷审核"
          open={reviewModalVisible}
          onCancel={() => {
            setReviewModalVisible(false);
            setSelectedSubmission(null);
          }}
          onOk={handleReviewSubmit}
          width={800}
          okText="提交审核"
          cancelText="取消"
        >
          {selectedSubmission && (
            <div>
              {/* 基本信息 */}
              <Card title="基本信息" size="small" style={{ marginBottom: 16 }}>
                <Descriptions column={2} size="small">
                  <Descriptions.Item label="问卷ID">
                    {selectedSubmission.id}
                  </Descriptions.Item>
                  <Descriptions.Item label="问卷类型">
                    {selectedSubmission.questionnaireId}
                  </Descriptions.Item>
                  <Descriptions.Item label="提交者">
                    {selectedSubmission.metadata.isAnonymous ? '匿名用户' : selectedSubmission.userId}
                  </Descriptions.Item>
                  <Descriptions.Item label="提交时间">
                    {new Date(selectedSubmission.metadata.submittedAt || selectedSubmission.createdAt).toLocaleString()}
                  </Descriptions.Item>
                </Descriptions>
              </Card>

              {/* 问卷内容 */}
              <div style={{ marginBottom: 16 }}>
                <Title level={4}>问卷内容</Title>
                {renderQuestionnaireDetails(selectedSubmission.responseData)}
              </div>

              {/* 审核表单 */}
              <Form form={reviewForm} layout="vertical">
                <Form.Item
                  name="status"
                  label="审核结果"
                  rules={[{ required: true, message: '请选择审核结果' }]}
                >
                  <Radio.Group>
                    <Radio value="approved">通过</Radio>
                    <Radio value="rejected">拒绝</Radio>
                  </Radio.Group>
                </Form.Item>
                
                <Form.Item
                  name="comment"
                  label="审核意见"
                  rules={[{ required: true, message: '请填写审核意见' }]}
                >
                  <TextArea rows={4} placeholder="请填写审核意见..." />
                </Form.Item>
              </Form>
            </div>
          )}
        </Modal>
      </div>
    </ReviewerLayout>
  );
};
