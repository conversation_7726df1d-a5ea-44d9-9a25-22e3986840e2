/* 布局示例页面样式 */

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

.header {
  text-align: center;
  margin-bottom: 32px;
}

.header h2 {
  color: #1890ff;
  margin-bottom: 16px;
}

/* 卡片样式 */
.featureCard,
.controlCard,
.guideCard,
.techCard {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e8e8e8;
  transition: all 0.3s ease;
}

.featureCard:hover,
.controlCard:hover,
.guideCard:hover,
.techCard:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

/* 特性展示 */
.feature {
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  border-left: 4px solid #1890ff;
}

.feature:not(:last-child) {
  margin-bottom: 16px;
}

/* 控制面板 */
.controlGroup {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f9f9f9;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
}

/* 使用说明 */
.step {
  padding: 16px;
  background: #f6ffed;
  border-radius: 8px;
  border: 1px solid #b7eb8f;
}

.step:not(:last-child) {
  margin-bottom: 16px;
}

.codeBlock {
  margin-top: 8px;
  padding: 12px;
  background: #f5f5f5;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  overflow-x: auto;
}

.codeBlock code {
  background: transparent !important;
  padding: 0 !important;
  font-size: 13px;
  line-height: 1.4;
  white-space: pre-wrap;
}

/* 技术特性网格 */
.techGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-top: 16px;
}

.techItem {
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
  text-align: center;
  transition: transform 0.3s ease;
}

.techItem:hover {
  transform: scale(1.05);
}

.techItem h4 {
  color: white !important;
  margin-bottom: 8px;
  font-size: 16px;
}

.techItem p {
  color: rgba(255, 255, 255, 0.9) !important;
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 16px 12px;
  }
  
  .header {
    margin-bottom: 24px;
  }
  
  .header h2 {
    font-size: 20px;
  }
  
  .controlGroup {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .techGrid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .techItem {
    padding: 16px;
  }
  
  .codeBlock {
    font-size: 12px;
    padding: 8px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 12px 8px;
  }
  
  .feature,
  .step {
    padding: 12px;
  }
  
  .controlGroup {
    padding: 8px 12px;
  }
  
  .techItem {
    padding: 12px;
  }
  
  .techItem h4 {
    font-size: 14px;
  }
  
  .techItem p {
    font-size: 12px;
  }
}

/* 平板端优化 */
@media (min-width: 769px) and (max-width: 1024px) {
  .container {
    padding: 20px 16px;
  }
  
  .techGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 大屏优化 */
@media (min-width: 1400px) {
  .container {
    max-width: 1400px;
    padding: 32px 24px;
  }
  
  .techGrid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* 深色模式 */
@media (prefers-color-scheme: dark) {
  .featureCard,
  .controlCard,
  .guideCard,
  .techCard {
    background: #1f1f1f;
    border-color: #333;
  }
  
  .feature {
    background: #2a2a2a;
    border-left-color: #40a9ff;
  }
  
  .controlGroup {
    background: #2a2a2a;
    border-color: #444;
  }
  
  .step {
    background: #1f2f1f;
    border-color: #52c41a;
  }
  
  .codeBlock {
    background: #2a2a2a;
    border-color: #444;
  }
  
  .codeBlock code {
    color: #ccc;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .featureCard,
  .controlCard,
  .guideCard,
  .techCard {
    border: 2px solid #000;
  }
  
  .feature {
    border-left: 6px solid #1890ff;
  }
  
  .step {
    border: 2px solid #52c41a;
  }
  
  .codeBlock {
    border: 2px solid #000;
  }
  
  .techItem {
    border: 2px solid #fff;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.featureCard,
.controlCard,
.guideCard,
.techCard {
  animation: fadeInUp 0.6s ease-out;
}

.featureCard {
  animation-delay: 0.1s;
}

.controlCard {
  animation-delay: 0.2s;
}

.guideCard {
  animation-delay: 0.3s;
}

.techCard {
  animation-delay: 0.4s;
}

/* 无障碍优化 */
.controlGroup:focus-within {
  outline: 2px solid #1890ff;
  outline-offset: 2px;
}

.techItem:focus {
  outline: 2px solid #fff;
  outline-offset: 2px;
}

/* 打印样式 */
@media print {
  .container {
    max-width: none;
    padding: 0;
  }
  
  .featureCard,
  .controlCard,
  .guideCard,
  .techCard {
    box-shadow: none;
    border: 1px solid #000;
    break-inside: avoid;
  }
  
  .techItem {
    background: #f5f5f5 !important;
    color: #000 !important;
    border: 1px solid #000;
  }
  
  .techItem h4,
  .techItem p {
    color: #000 !important;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .featureCard,
  .controlCard,
  .guideCard,
  .techCard,
  .techItem {
    animation: none;
    transition: none;
  }
  
  .featureCard:hover,
  .controlCard:hover,
  .guideCard:hover,
  .techCard:hover {
    transform: none;
  }
  
  .techItem:hover {
    transform: none;
  }
}
