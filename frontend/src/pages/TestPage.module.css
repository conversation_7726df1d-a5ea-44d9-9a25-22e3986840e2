.container {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 20px;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.title {
  color: #000;
  margin-bottom: 10px;
  font-weight: 600;
}

.status {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
}

.statusOnline {
  background: #f8f9fa;
  color: #000;
  border: 1px solid #e8e8e8;
}

.statusOffline {
  background: #f8f9fa;
  color: #666;
  border: 1px solid #e8e8e8;
}

.statusDot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.statusDotOnline {
  background: #000;
}

.statusDotOffline {
  background: #666;
}

.testSection {
  max-width: 800px;
  margin: 0 auto;
}

.testCard {
  margin-bottom: 20px;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
  background: #fff;
}

.testCardHeader {
  background: #000;
  color: #fff;
  border-bottom: 1px solid #e8e8e8;
  padding: 16px 24px;
  border-radius: 4px 4px 0 0;
}

.testCardTitle {
  margin: 0;
  color: #fff;
  font-size: 16px;
  font-weight: 500;
}

.testCardBody {
  padding: 24px;
}

.inputGroup {
  margin-bottom: 16px;
}

.inputLabel {
  display: block;
  margin-bottom: 8px;
  color: #000;
  font-weight: 500;
}

.testInput {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  font-size: 14px;
}

.testInput:focus {
  border-color: #000;
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
  outline: none;
}

.testButton {
  background: #000;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.testButton:hover {
  background: #333;
}

.testButton:disabled {
  background: #f5f5f5;
  color: #bfbfbf;
  cursor: not-allowed;
}

.responseSection {
  margin-top: 16px;
}

.responseLabel {
  display: block;
  margin-bottom: 8px;
  color: #000;
  font-weight: 500;
}

.responseBox {
  background: #f8f9fa;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 12px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 300px;
  overflow-y: auto;
}

.buttonGroup {
  display: flex;
  gap: 12px;
  align-items: center;
}

.description {
  color: #666;
  font-size: 14px;
}

.inputRow {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.inputField {
  flex: 0 0 200px;
}

@media (max-width: 768px) {
  .container {
    padding: 16px;
  }
  
  .inputRow {
    flex-direction: column;
    align-items: stretch;
  }
  
  .inputField {
    flex: 1;
  }
  
  .buttonGroup {
    flex-direction: column;
    align-items: stretch;
  }
}
