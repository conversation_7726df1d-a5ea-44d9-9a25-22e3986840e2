.container {
  min-height: calc(100vh - 64px); /* 减去header高度 */
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 48px 16px;
}

.content {
  max-width: 400px;
  width: 100%;
}

.header {
  text-align: center;
  margin-bottom: 32px;
}

.title {
  color: #000;
  font-size: 24px;
  font-weight: 500;
  margin-bottom: 8px;
  line-height: 1.3;
}

.subtitle {
  color: #666;
  font-size: 14px;
  margin: 0;
}

.formCard {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  box-shadow: none;
}

.formCard .ant-card-body {
  padding: 32px;
}

.errorAlert {
  margin-bottom: 24px;
}

.submitButton {
  width: 100%;
  background: #1890ff;
  border-color: #1890ff;
  color: #fff;
  height: 40px;
  font-size: 14px;
  font-weight: 400;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.submitButton:hover {
  background: #40a9ff;
  border-color: #40a9ff;
  color: #fff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

.submitButton:disabled {
  background: #f5f5f5;
  border-color: #d9d9d9;
  color: #bfbfbf;
}

.footer {
  text-align: center;
  margin-top: 24px;
  color: #666;
  font-size: 14px;
}

.footer a {
  color: #000;
  text-decoration: none;
}

.footer a:hover {
  color: #333;
  text-decoration: underline;
}

/* 表单项样式 */
.formCard .ant-form-item-label > label {
  color: #000;
  font-weight: 500;
  font-size: 14px;
}

.formCard .ant-input {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  height: 40px;
}

.formCard .ant-input:hover {
  border-color: #000;
}

.formCard .ant-input:focus {
  border-color: #000;
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
}

.inputIcon {
  color: #999;
}

/* 角色专用样式 */
.card {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  background: #fff;
}

.header .icon {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
}

/* 登录选项 */
.loginOptions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.loginOptions .ant-checkbox-wrapper {
  color: #666 !important;
  font-size: 14px !important;
}

/* 底部链接 */
.divider {
  color: #d9d9d9;
  margin: 0 8px;
}

.link {
  color: #666 !important;
  text-decoration: none !important;
  font-size: 13px !important;
}

.link:hover {
  color: #333 !important;
  text-decoration: underline !important;
}

/* 安全提示 */
.securityNotice {
  margin-top: 24px;
}

.securityNotice .ant-alert {
  border-radius: 6px !important;
}

.securityNotice .ant-alert-warning {
  background: #fff7e6 !important;
  border: 1px solid #ffd591 !important;
}

.securityNotice .ant-alert-info {
  background: #f6f8ff !important;
  border: 1px solid #d6e3ff !important;
}

.securityNotice .ant-alert-description p {
  margin: 4px 0 !important;
  font-size: 13px !important;
  color: #666 !important;
}

.securityNotice .ant-alert-description p:last-child {
  margin-bottom: 0 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 24px 16px;
  }

  .formCard .ant-card-body {
    padding: 24px;
  }

  .title {
    font-size: 20px;
  }

  .header .icon {
    font-size: 40px;
    margin-bottom: 12px;
  }

  .securityNotice {
    margin-top: 16px;
  }
}
